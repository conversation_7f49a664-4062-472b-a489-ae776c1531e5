---
description: 
globs: 
alwaysApply: true
---
# PISP 项目开发规范

## 项目概述
- 这是一个基于 Spring Boot 3.x 的微服务项目
- 使用 Maven 作为构建工具，主POM配置见 [pom.xml](mdc:pom.xml)
- 采用分层架构: Controller -> Service -> Mapper
- 使用 MyBatis-Plus 作为 ORM 框架

## Java Web 项目规范

### Controller 层规范
- Controller 只负责数据传输，使用 DTO 对象与前端和 Service 层交互
- Controller 不能直接与数据库通信
- 必须使用统一的 Result<T> 包装返回结果，参考 [Result.java](mdc:pisp-common/pisp-common-web/src/main/java/com/bdyl/erp/pisp/common/web/result/Result.java)
- 异常处理统一使用 BusinessException，参考 [BusinessException.java](mdc:pisp-common/pisp-common-core/src/main/java/com/bdyl/erp/pisp/common/core/exception/BusinessException.java)

### 分页查询规范
- **所有 QueryRequest 类必须继承 PageRequest 基类**
  - ❌ 错误: 在 QueryRequest 中重新定义 pageNum, pageSize 等分页字段
  - ✅ 正确: 继承 PageRequest，使用 @EqualsAndHashCode(callSuper = true)
  - 参考正确实现: [UserQueryRequest.java](mdc:pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/dto/request/UserQueryRequest.java)
  - 参考正确实现: [RoleQueryRequest.java](mdc:pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/dto/request/RoleQueryRequest.java)
  - PageRequest 基类: [PageRequest.java](mdc:pisp-common/pisp-common-web/src/main/java/com/bdyl/erp/pisp/common/web/dto/PageRequest.java)

```java
@Data
@EqualsAndHashCode(callSuper = true)
public class UserQueryRequest extends PageRequest {
    // 只定义业务查询字段，不要重复定义分页字段
    private String username;
    private String email;
    // ... 其他业务字段
}
```

### REST API 规范
- **分页查询不应使用 /page 后缀**
  - ❌ 错误: @GetMapping("/page") 或 @GetMapping("/users/page")
  - ✅ 正确: @GetMapping 或 @GetMapping("/users")
  - 分页参数通过 QueryRequest 对象传递，而不是 URL 路径
  - 参考正确实现: [UserController.java](mdc:pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/controller/UserController.java)
  - 参考正确实现: [RoleController.java](mdc:pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/controller/RoleController.java)

### URL 设计规范
- 使用标准 REST 风格:
```java
@GetMapping              // 分页查询资源列表
@GetMapping("/{id}")     // 根据ID查询单个资源
@PostMapping             // 创建资源
@PutMapping("/{id}")     // 更新资源
@DeleteMapping("/{id}")  // 删除单个资源
@DeleteMapping("/batch") // 批量删除
```

### 异常处理规范
- 业务异常必须使用 BusinessException
- 使用 ResponseCode 枚举定义错误码，参考 [ResponseCode.java](mdc:pisp-common/pisp-common-core/src/main/java/com/bdyl/erp/pisp/common/core/result/ResponseCode.java)
- ❌ 错误: throw new RuntimeException("用户不存在")
- ✅ 正确: throw new BusinessException(ResponseCode.USER_NOT_FOUND)

### 外部库使用规范
- 使用外部库时，优先查阅 Context7 获取正确的使用文档和示例
- 确保使用官方推荐的最佳实践

## 代码质量要求
- 所有类都需要添加完整的 JavaDoc 注释
- 方法参数使用 @Valid 或 @Validated 进行验证
- 使用 Lombok 简化代码，统一使用 @Data, @Builder 等注解
- 遵循阿里巴巴 Java 开发手册规范

## 测试规范
- 每个 Service 必须有对应的单元测试
- Controller 需要有集成测试
- 测试覆盖率不低于 80%
- 参考测试实现: [UserServiceIntegrationTest.java](mdc:pisp-services/pisp-user/src/test/java/com/bdyl/erp/pisp/user/integration/UserServiceIntegrationTest.java)

## 数据库规范
- 实体类必须继承 BaseEntity，参考 [BaseEntity.java](mdc:pisp-common/pisp-common-core/src/main/java/com/bdyl/erp/pisp/common/core/entity/BaseEntity.java)
- 使用乐观锁 (@Version) 处理并发更新
- 软删除使用 @TableLogic 注解
- 字段命名使用下划线分隔 (snake_case)
- 参考实体实现: [User.java](mdc:pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/entity/User.java)

## 响应格式规范
- 统一使用 Result<T> 包装响应数据
- 分页查询使用 PageResult<T>，参考 [PageResult.java](mdc:pisp-common/pisp-common-web/src/main/java/com/bdyl/erp/pisp/common/web/result/PageResult.java)
- 错误响应包含错误码和详细消息

## 安全规范
- 敏感信息不得记录到日志中
- 密码必须加密存储
- API 接口需要进行权限验证
- 使用 JWT 进行身份认证

## Git 提交规范
- 提交信息格式: type(scope): description
- type: feat, fix, docs, style, refactor, test, chore
- 示例: feat(user): add role management functionality

## 项目结构参考
- 用户管理模块: [pisp-user](mdc:pisp-services/pisp-user)
- 公共模块: [pisp-common](mdc:pisp-common)
- 网关模块: [pisp-gateway](mdc:pisp-gateway)

请在开发过程中严格遵循以上规范，确保代码质量和项目一致性。
