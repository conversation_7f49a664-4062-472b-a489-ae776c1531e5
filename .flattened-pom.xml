<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.bdyl.build</groupId>
    <artifactId>bdyl-parent</artifactId>
    <version>2025.2.0.0-SNAPSHOT</version>
  </parent>
  <groupId>com.bdyl.erp.pisp</groupId>
  <artifactId>pisp-root</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <packaging>pom</packaging>
  <name>PISP Purchase Inventory Sales Management System</name>
  <description>基于Spring Boot 3.4.7和Spring Cloud 2024.0.1的微服务架构进销存管理系统</description>
  <modules>
    <module>pisp-common</module>
    <module>pisp-gateway</module>
    <module>pisp-api</module>
    <module>pisp-services</module>
  </modules>
  <properties>
    <maven-failsafe-plugin.version>3.2.2</maven-failsafe-plugin.version>
    <maven-surefire-plugin.version>3.2.2</maven-surefire-plugin.version>
    <jjwt.version>0.12.6</jjwt.version>
    <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
    <revision>1.0.0-SNAPSHOT</revision>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.bdyl.erp.pisp</groupId>
        <artifactId>pisp-api</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.bdyl.erp.pisp</groupId>
        <artifactId>pisp-common-core</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.bdyl.erp.pisp</groupId>
        <artifactId>pisp-common-web</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.bdyl.erp.pisp</groupId>
        <artifactId>pisp-common-security</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.bdyl.erp.pisp</groupId>
        <artifactId>pisp-common-redis</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.bdyl.erp.pisp</groupId>
        <artifactId>pisp-common-rocketmq</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-api</artifactId>
        <version>${jjwt.version}</version>
      </dependency>
      <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-impl</artifactId>
        <version>${jjwt.version}</version>
      </dependency>
      <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-jackson</artifactId>
        <version>${jjwt.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <dependencies>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <repositories>
    <repository>
      <id>central</id>
      <name>Maven Central</name>
      <url>https://repo1.maven.org/maven2</url>
    </repository>
    <repository>
      <id>spring-milestones</id>
      <name>Spring Milestones</name>
      <url>https://repo.spring.io/milestone</url>
    </repository>
  </repositories>
  <pluginRepositories>
    <pluginRepository>
      <id>central</id>
      <name>Maven Central</name>
      <url>https://repo1.maven.org/maven2</url>
    </pluginRepository>
    <pluginRepository>
      <id>spring-milestones</id>
      <name>Spring Milestones</name>
      <url>https://repo.spring.io/milestone</url>
    </pluginRepository>
  </pluginRepositories>
  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${maven-surefire-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-failsafe-plugin</artifactId>
          <version>${maven-failsafe-plugin.version}</version>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>
</project>
