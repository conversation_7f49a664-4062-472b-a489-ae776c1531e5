<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Annotation profile for PISP Purchase Inventory Sales Management System" enabled="true">
        <sourceOutputDir name="../generated-sources/annotations" />
        <sourceTestOutputDir name="../generated-test-sources/test-annotations" />
        <module name="pisp-inventory.main" />
        <module name="pisp-common-core.test" />
        <module name="pisp-sales.main" />
        <module name="pisp-common-security.test" />
        <module name="pisp-base-data.test" />
        <module name="pisp-finance.test" />
        <module name="pisp-common-redis.test" />
        <module name="pisp-user.test" />
        <module name="pisp-report.test" />
        <module name="pisp-common-web.test" />
        <module name="pisp-purchase.test" />
        <module name="pisp-finance.main" />
        <module name="pisp-user.main" />
        <module name="pisp-retail.main" />
        <module name="pisp-common-rocketmq.main" />
        <module name="pisp-gateway.test" />
        <module name="pisp-inventory.test" />
        <module name="pisp-common-redis.main" />
        <module name="pisp-system.main" />
        <module name="pisp-common-web.main" />
        <module name="pisp-base-data.main" />
        <module name="pisp-report.main" />
        <module name="pisp-api.main" />
        <module name="pisp-purchase.main" />
        <module name="pisp-common-core.main" />
        <module name="pisp-api.test" />
        <module name="pisp-common-security.main" />
        <module name="pisp-system.test" />
        <module name="pisp-sales.test" />
        <module name="pisp-gateway.main" />
        <module name="pisp-common-rocketmq.test" />
        <module name="pisp-retail.test" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="pisp-api" options="-parameters" />
      <module name="pisp-api.main" options="-parameters" />
      <module name="pisp-api.test" options="-parameters" />
      <module name="pisp-base-data" options="-parameters" />
      <module name="pisp-base-data.main" options="-parameters" />
      <module name="pisp-base-data.test" options="-parameters" />
      <module name="pisp-common-core" options="-parameters" />
      <module name="pisp-common-core.main" options="-parameters" />
      <module name="pisp-common-core.test" options="-parameters" />
      <module name="pisp-common-redis" options="-parameters" />
      <module name="pisp-common-redis.main" options="-parameters" />
      <module name="pisp-common-redis.test" options="-parameters" />
      <module name="pisp-common-rocketmq" options="-parameters" />
      <module name="pisp-common-rocketmq.main" options="-parameters" />
      <module name="pisp-common-rocketmq.test" options="-parameters" />
      <module name="pisp-common-security" options="-parameters" />
      <module name="pisp-common-security.main" options="-parameters" />
      <module name="pisp-common-security.test" options="-parameters" />
      <module name="pisp-common-web" options="-parameters" />
      <module name="pisp-common-web.main" options="-parameters" />
      <module name="pisp-common-web.test" options="-parameters" />
      <module name="pisp-finance" options="-parameters" />
      <module name="pisp-finance.main" options="-parameters" />
      <module name="pisp-finance.test" options="-parameters" />
      <module name="pisp-gateway" options="-parameters" />
      <module name="pisp-gateway.main" options="-parameters" />
      <module name="pisp-gateway.test" options="-parameters" />
      <module name="pisp-inventory" options="-parameters" />
      <module name="pisp-inventory.main" options="-parameters" />
      <module name="pisp-inventory.test" options="-parameters" />
      <module name="pisp-purchase" options="-parameters" />
      <module name="pisp-purchase.main" options="-parameters" />
      <module name="pisp-purchase.test" options="-parameters" />
      <module name="pisp-report" options="-parameters" />
      <module name="pisp-report.main" options="-parameters" />
      <module name="pisp-report.test" options="-parameters" />
      <module name="pisp-retail" options="-parameters" />
      <module name="pisp-retail.main" options="-parameters" />
      <module name="pisp-retail.test" options="-parameters" />
      <module name="pisp-sales" options="-parameters" />
      <module name="pisp-sales.main" options="-parameters" />
      <module name="pisp-sales.test" options="-parameters" />
      <module name="pisp-system" options="-parameters" />
      <module name="pisp-system.main" options="-parameters" />
      <module name="pisp-system.test" options="-parameters" />
      <module name="pisp-user" options="-parameters" />
      <module name="pisp-user.main" options="-parameters" />
      <module name="pisp-user.test" options="-parameters" />
    </option>
  </component>
</project>