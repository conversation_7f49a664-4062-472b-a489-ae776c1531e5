<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding">
    <file url="file://$PROJECT_DIR$/pisp-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-common/pisp-common-core/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-common/pisp-common-core/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-common/pisp-common-redis/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-common/pisp-common-redis/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-common/pisp-common-rocketmq/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-common/pisp-common-rocketmq/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-common/pisp-common-security/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-common/pisp-common-security/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-common/pisp-common-web/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-common/pisp-common-web/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-common/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-gateway/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-gateway/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-services/pisp-base-data/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-services/pisp-base-data/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-services/pisp-finance/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-services/pisp-finance/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-services/pisp-inventory/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-services/pisp-inventory/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-services/pisp-purchase/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-services/pisp-purchase/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-services/pisp-report/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-services/pisp-report/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-services/pisp-retail/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-services/pisp-retail/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-services/pisp-sales/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-services/pisp-sales/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-services/pisp-system/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-services/pisp-system/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-services/pisp-user/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-services/pisp-user/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-services/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pisp-services/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources" charset="UTF-8" />
  </component>
</project>