<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="jboss-public-repository-group" />
      <option name="name" value="JBoss Public Maven Repository Group" />
      <option name="url" value="https://repository.jboss.org/nexus/content/groups/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="clojars" />
      <option name="name" value="clojars" />
      <option name="url" value="https://clojars.org/repo" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="spring-milestones" />
      <option name="name" value="Spring Milestones" />
      <option name="url" value="https://repo.spring.io/milestone/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="2229192-release-TpUn0w" />
      <option name="name" value="2229192-release-TpUn0w" />
      <option name="url" value="https://packages.aliyun.com/maven/repository/2229192-release-TpUn0w" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="central" />
      <option name="url" value="https://repo1.maven.org/maven2/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="spring-snapshots" />
      <option name="name" value="Spring Snapshots" />
      <option name="url" value="https://repo.spring.io/snapshot" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="2229192-snapshot-sAjG1A" />
      <option name="name" value="2229192-snapshot-sAjG1A" />
      <option name="url" value="https://packages.aliyun.com/maven/repository/2229192-snapshot-sAjG1A" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="akka" />
      <option name="name" value="Akka library repository" />
      <option name="url" value="https://repo.akka.io/maven" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="spring-releases" />
      <option name="name" value="Spring Releases" />
      <option name="url" value="https://repo.spring.io/release/" />
    </remote-repository>
  </component>
</project>