# PISP进销存管理系统

基于Spring Boot 3.4.7和Spring Cloud 2024.0.1的微服务架构进销存管理系统

## 项目结构

```
pisp-root/
├── pom.xml                           # 父POM，统一管理依赖版本
├── pisp-common/                      # 公共模块
│   ├── pisp-common-core/            # 核心工具类
│   ├── pisp-common-security/        # 安全认证
│   ├── pisp-common-redis/           # Redis配置
│   ├── pisp-common-rocketmq/        # RocketMQ配置
│   └── pisp-common-web/             # Web通用配置
├── pisp-gateway/                     # ShenYu网关
├── pisp-api/                         # API接口定义
└── pisp-services/                    # 业务服务模块
    ├── pisp-user/                   # 用户管理服务 (8001)
    ├── pisp-base-data/              # 基础数据服务 (8002)
    ├── pisp-purchase/               # 采购管理服务 (8003)
    ├── pisp-sales/                  # 销售管理服务 (8004)
    ├── pisp-inventory/              # 库存管理服务 (8005)
    ├── pisp-finance/                # 财务管理服务 (8006)
    ├── pisp-report/                 # 报表分析服务 (8007)
    ├── pisp-system/                 # 系统管理服务 (8008)
    └── pisp-retail/                 # 零售管理服务 (8009)
```

## 技术栈

### 后端技术栈
- **Java 21 (LTS)** - 开发语言
- **Spring Boot 3.4.7** - 微服务框架
- **Spring Cloud 2024.0.1** - 微服务生态
- **Maven 3.9.x** - 项目构建和依赖管理
- **Nacos 3.0.2** - 注册中心和配置中心
- **Apache ShenYu 2.7.0.1** - API网关
- **MyBatis-Plus 3.5.12** - ORM框架
- **PostgreSQL 17** - 主数据库
- **Redis 7.x** - 缓存和会话存储
- **Apache RocketMQ 5.3.1** - 消息中间件

### 工具库
- **Lombok 1.18.30** - 简化Java代码
- **Hutool 5.8.25** - Java工具库
- **MapStruct 1.5.5.Final** - 对象映射
- **Knife4j 4.4.0** - API文档

## 服务端口分配

| 服务名称 | 端口 | 描述 |
|----------|------|------|
| pisp-gateway | 9999 | API网关 |
| pisp-user-service | 8001 | 用户管理服务 |
| pisp-base-data-service | 8002 | 基础数据服务 |
| pisp-purchase-service | 8003 | 采购管理服务 |
| pisp-sales-service | 8004 | 销售管理服务 |
| pisp-inventory-service | 8005 | 库存管理服务 |
| pisp-finance-service | 8006 | 财务管理服务 |
| pisp-report-service | 8007 | 报表分析服务 |
| pisp-system-service | 8008 | 系统管理服务 |
| pisp-retail-service | 8009 | 零售管理服务 |

## 模块说明

### 公共模块 (pisp-common)
- **pisp-common-core**: 核心工具类、基础实体、统一响应结果等
- **pisp-common-security**: 安全认证、权限控制相关组件
- **pisp-common-redis**: Redis配置和缓存工具
- **pisp-common-rocketmq**: RocketMQ消息队列配置和工具
- **pisp-common-web**: Web层通用配置、异常处理、拦截器等

### 业务服务模块 (pisp-services)
- **pisp-user**: 用户管理、角色管理、权限管理
- **pisp-base-data**: 商品管理、客户管理、供应商管理、门店管理等基础数据
- **pisp-purchase**: 采购订单、采购入库、采购退货等采购业务
- **pisp-sales**: 销售订单、销售出库、销售退货等销售业务
- **pisp-inventory**: 库存查询、库存盘点、库存预警等库存管理
- **pisp-finance**: 应收应付、成本核算、财务报表等财务管理
- **pisp-report**: 业务报表、数据分析、零售分析等报表功能
- **pisp-system**: 系统配置、日志管理、数据字典等系统管理
- **pisp-retail**: POS销售、会员管理、促销活动等零售业务

### 网关模块 (pisp-gateway)
基于Apache ShenYu的API网关，提供：
- 路由转发
- 负载均衡
- 限流熔断
- 安全认证
- 监控统计

### API模块 (pisp-api)
统一的API接口定义，包含：
- DTO (数据传输对象)
- VO (视图对象)
- 接口定义
- 常量定义

## 开发环境要求

- JDK 21+
- Maven 3.9+
- PostgreSQL 17
- Redis 7.x
- Nacos 3.0.2
- Apache RocketMQ 5.3.1

## 快速开始

1. 克隆项目
```bash
git clone <repository-url>
cd pisp-system
```

2. 编译项目
```bash
mvn clean compile
```

3. 启动基础设施
- 启动PostgreSQL数据库
- 启动Redis服务
- 启动Nacos服务
- 启动RocketMQ服务

4. 启动服务
- 先启动网关服务
- 再启动各个业务服务

## 注意事项

- 本项目目前处于初始化阶段，只创建了基础的模块结构
- 具体的业务逻辑和功能实现需要后续开发
- 所有配置文件中的地址和端口请根据实际环境调整
