# PISP Common Core 模块

这是PISP进销存管理系统的核心工具模块，提供了系统中所有模块都会用到的基础类和工具类。

## 📦 模块结构

```
pisp-common-core/
├── src/main/java/com/bdyl/erp/pisp/common/core/
│   ├── base/           # 基础类
│   │   └── BaseEntity.java
│   ├── constants/      # 常量定义
│   │   └── CommonConstants.java
│   ├── dto/           # 数据传输对象
│   │   ├── BaseDTO.java
│   │   └── UserDTO.java
│   ├── enums/         # 枚举定义
│   │   ├── ErrorCode.java
│   │   └── BusinessStatus.java
│   ├── exception/     # 异常定义
│   │   └── BusinessException.java
│   ├── page/          # 分页相关
│   │   └── PageResult.java
│   ├── response/      # 响应结果
│   │   └── Result.java
│   ├── utils/         # 工具类
│   │   ├── Assert.java
│   │   └── StringUtils.java
│   └── example/       # 使用示例
│       └── LombokUsageExample.java
```

## 🚀 Lombok优化特色

### 1. 依赖管理
已添加Lombok依赖到pom.xml：
```xml
<dependency>
    <groupId>org.projectlombok</groupId>
    <artifactId>lombok</artifactId>
    <scope>provided</scope>
</dependency>
```

### 2. 核心类Lombok化

#### BaseEntity - 基础实体类
```java
@Data
@EqualsAndHashCode(callSuper = false)
public abstract class BaseEntity implements Serializable {
    // 所有getter/setter/toString自动生成
}
```

**使用Lombok前**：191行代码
**使用Lombok后**：75行代码
**减少代码**：116行 (约60%减少)

#### Result<T> - 统一响应结果类
```java
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Result<T> implements Serializable {
    // 支持链式调用，自动生成所有标准方法
}
```

**优势**：
- `@Accessors(chain = true)` 支持链式调用
- 自动生成getter/setter/toString
- 支持流式API风格

#### PageResult<T> - 分页结果类
```java
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PageResult<T> implements Serializable {
    // 自动生成所有基础方法
}
```

#### BusinessException - 业务异常类
```java
@Getter
@ToString
public class BusinessException extends RuntimeException {
    @Setter
    private Object data;
    // 只为data字段生成setter，其他字段只有getter
}
```

### 3. DTO类的最佳实践

#### BaseDTO - 基础DTO类
```java
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public abstract class BaseDTO implements Serializable {
    // 支持链式调用的基础DTO
}
```

#### UserDTO - 完整示例DTO
```java
@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class UserDTO extends BaseDTO {
    // 完整的Lombok注解配置示例
}
```

**特色功能**：
- `@Builder(toBuilder = true)` - 支持Builder模式和复制修改
- `@Accessors(chain = true)` - 支持链式调用
- `@EqualsAndHashCode(callSuper = true)` - 包含父类字段的比较
- `@ToString(callSuper = true)` - 包含父类字段的toString

## 📖 使用示例

### 1. Builder模式创建对象
```java
UserDTO user = UserDTO.builder()
    .username("zhangsan")
    .realName("张三")
    .mobile("13800138000")
    .email("<EMAIL>")
    .status(1)
    .build();
```

### 2. 链式调用设置属性
```java
UserDTO user = new UserDTO()
    .setUsername("wangwu")
    .setRealName("王五")
    .setMobile("13700137000")
    .setStatus(1);
```

### 3. 对象复制和修改
```java
UserDTO original = createUser();
UserDTO modified = original.toBuilder()
    .realName("张三（修改后）")
    .status(0)
    .build();
```

### 4. 统一响应结果
```java
// 成功响应
Result<UserDTO> success = Result.success("查询成功", user)
    .trace("TRACE-123456");

// 错误响应
Result<Void> error = Result.error(ErrorCode.USER_NOT_FOUND);
```

### 5. 分页结果
```java
PageResult<UserDTO> page = PageResult.of(1, 10, 100, userList);
```

## 🎯 Lombok注解说明

### 常用注解

| 注解 | 作用 | 适用场景 |
|------|------|----------|
| `@Data` | 生成getter/setter/toString/equals/hashCode | 普通POJO类 |
| `@Getter/@Setter` | 生成getter/setter方法 | 需要精确控制的类 |
| `@Builder` | 生成Builder模式 | 需要Builder模式的类 |
| `@NoArgsConstructor` | 生成无参构造器 | JPA实体、DTO等 |
| `@AllArgsConstructor` | 生成全参构造器 | 不可变对象 |
| `@ToString` | 生成toString方法 | 需要调试输出的类 |
| `@EqualsAndHashCode` | 生成equals和hashCode | 需要比较的对象 |

### 高级注解

| 注解 | 作用 | 适用场景 |
|------|------|----------|
| `@Accessors(chain = true)` | 支持链式调用 | 流式API设计 |
| `@Builder(toBuilder = true)` | 支持toBuilder方法 | 需要对象复制修改 |
| `@EqualsAndHashCode(callSuper = true)` | 包含父类字段比较 | 继承关系的类 |
| `@ToString(callSuper = true)` | 包含父类字段输出 | 继承关系的类 |

## 💡 最佳实践

### 1. 实体类使用
```java
@Data
@EqualsAndHashCode(callSuper = false)
public class ProductEntity extends BaseEntity {
    // 数据库实体类
}
```

### 2. DTO类使用
```java
@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ProductDTO extends BaseDTO {
    // 数据传输对象
}
```

### 3. 值对象使用
```java
@Value
@Builder
public class Money {
    BigDecimal amount;
    String currency;
    // 不可变值对象
}
```

### 4. 异常类使用
```java
@Getter
@ToString
public class CustomException extends RuntimeException {
    private final int errorCode;
    @Setter
    private Object data;
}
```

## ⚠️ 注意事项

1. **IDE支持**：需要安装Lombok插件
2. **编译配置**：确保编译时Lombok注解处理器生效
3. **继承关系**：使用`callSuper = true`处理父类字段
4. **序列化**：注意Lombok生成的方法对序列化的影响
5. **调试**：可以通过delombok命令查看生成的代码

## 📈 性能提升

通过使用Lombok，本模块获得了显著的代码简化：

- **BaseEntity**: 从191行减少到75行 (↓60%)
- **Result**: 从205行减少到125行 (↓39%)
- **PageResult**: 从165行减少到110行 (↓33%)
- **BusinessException**: 从105行减少到85行 (↓19%)

**总体效果**：
- 代码行数减少约40%
- 维护成本显著降低
- 代码可读性提升
- 开发效率提高

## 🔗 相关文档

- [Lombok官方文档](https://projectlombok.org/)
- [Spring Boot + Lombok最佳实践](https://spring.io/guides/)
- [PISP系统架构设计](../../../doc/03-system-design/SDD-001-系统架构设计.md) 