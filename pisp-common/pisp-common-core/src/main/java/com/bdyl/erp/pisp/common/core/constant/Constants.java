package com.bdyl.erp.pisp.common.core.constant;

/**
 * 系统常量定义
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
public final class Constants {

    private Constants() {
        // 工具类，禁止实例化
    }

    // ========== 系统常量 ==========

    /**
     * 系统名称
     */
    public static final String SYSTEM_NAME = "PISP";

    /**
     * 系统版本
     */
    public static final String SYSTEM_VERSION = "1.0.0";

    /**
     * 默认字符编码
     */
    public static final String DEFAULT_CHARSET = "UTF-8";

    /**
     * 默认时区
     */
    public static final String DEFAULT_TIMEZONE = "Asia/Shanghai";

    // ========== 分页常量 ==========

    /**
     * 默认页码
     */
    public static final int DEFAULT_PAGE_NUM = 1;

    /**
     * 默认页大小
     */
    public static final int DEFAULT_PAGE_SIZE = 20;

    /**
     * 最大页大小
     */
    public static final int MAX_PAGE_SIZE = 1000;

    // ========== 缓存常量 ==========

    /**
     * 缓存键分隔符
     */
    public static final String CACHE_KEY_SEPARATOR = ":";

    /**
     * 默认缓存过期时间（秒）
     */
    public static final long DEFAULT_CACHE_EXPIRE = 3600L;

    /**
     * 短期缓存过期时间（秒）
     */
    public static final long SHORT_CACHE_EXPIRE = 300L;

    /**
     * 长期缓存过期时间（秒）
     */
    public static final long LONG_CACHE_EXPIRE = 86400L;

    // ========== 请求头常量 ==========

    /**
     * 授权头
     */
    public static final String AUTHORIZATION_HEADER = "Authorization";

    /**
     * Bearer令牌前缀
     */
    public static final String BEARER_PREFIX = "Bearer ";

    /**
     * 用户ID头
     */
    public static final String USER_ID_HEADER = "X-User-Id";

    /**
     * 租户ID头
     */
    public static final String TENANT_ID_HEADER = "X-Tenant-Id";

    /**
     * 请求追踪ID头
     */
    public static final String TRACE_ID_HEADER = "X-Trace-Id";

    /**
     * 客户端IP头
     */
    public static final String CLIENT_IP_HEADER = "X-Real-IP";

    // ========== 状态常量 ==========

    /**
     * 启用状态
     */
    public static final Integer STATUS_ENABLED = 1;

    /**
     * 禁用状态
     */
    public static final Integer STATUS_DISABLED = 0;

    /**
     * 删除标记 - 未删除
     */
    public static final Integer DELETED_NO = 0;

    /**
     * 删除标记 - 已删除
     */
    public static final Integer DELETED_YES = 1;

    // ========== 用户相关常量 ==========

    /**
     * 超级管理员用户ID
     */
    public static final Long SUPER_ADMIN_USER_ID = 1L;

    /**
     * 系统用户ID
     */
    public static final Long SYSTEM_USER_ID = 0L;

    /**
     * 默认密码
     */
    public static final String DEFAULT_PASSWORD = "123456";

    /**
     * 密码最小长度
     */
    public static final int PASSWORD_MIN_LENGTH = 6;

    /**
     * 密码最大长度
     */
    public static final int PASSWORD_MAX_LENGTH = 20;

    // ========== 文件相关常量 ==========

    /**
     * 文件上传最大大小（字节）- 10MB
     */
    public static final long MAX_FILE_SIZE = 10 * 1024 * 1024L;

    /**
     * 图片文件最大大小（字节）- 5MB
     */
    public static final long MAX_IMAGE_SIZE = 5 * 1024 * 1024L;

    /**
     * 允许的图片文件类型
     */
    public static final String[] ALLOWED_IMAGE_TYPES = {"jpg", "jpeg", "png", "gif", "bmp"};

    /**
     * 允许的文档文件类型
     */
    public static final String[] ALLOWED_DOCUMENT_TYPES = {"pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt"};

    // ========== 业务常量 ==========

    /**
     * 默认货币代码
     */
    public static final String DEFAULT_CURRENCY = "CNY";

    /**
     * 金额精度（小数位数）
     */
    public static final int AMOUNT_SCALE = 2;

    /**
     * 数量精度（小数位数）
     */
    public static final int QUANTITY_SCALE = 4;

    /**
     * 单价精度（小数位数）
     */
    public static final int PRICE_SCALE = 4;

    // ========== 正则表达式常量 ==========

    /**
     * 手机号正则表达式
     */
    public static final String PHONE_REGEX = "^1[3-9]\\d{9}$";

    /**
     * 邮箱正则表达式
     */
    public static final String EMAIL_REGEX = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";

    /**
     * 身份证号正则表达式
     */
    public static final String ID_CARD_REGEX =
        "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";

    /**
     * 统一社会信用代码正则表达式
     */
    public static final String CREDIT_CODE_REGEX = "^[0-9A-HJ-NPQRTUWXY]{2}\\d{6}[0-9A-HJ-NPQRTUWXY]{10}$";

    // ========== 日期时间格式常量 ==========

    /**
     * 标准日期时间格式
     */
    public static final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 标准日期格式
     */
    public static final String DATE_FORMAT = "yyyy-MM-dd";

    /**
     * 标准时间格式
     */
    public static final String TIME_FORMAT = "HH:mm:ss";

    /**
     * 紧凑日期时间格式
     */
    public static final String COMPACT_DATETIME_FORMAT = "yyyyMMddHHmmss";

    /**
     * 紧凑日期格式
     */
    public static final String COMPACT_DATE_FORMAT = "yyyyMMdd";
}
