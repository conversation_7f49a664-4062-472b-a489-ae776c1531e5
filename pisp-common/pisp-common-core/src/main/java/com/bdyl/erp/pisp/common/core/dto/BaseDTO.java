package com.bdyl.erp.pisp.common.core.dto;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 基础数据传输对象 所有DTO类都应继承此类，提供统一的基础字段
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
public abstract class BaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 更新人姓名
     */
    private String updaterName;

    /**
     * 额外信息
     */
    private String additionalInfo;

    /**
     * 备注信息
     */
    private String remark;
}
