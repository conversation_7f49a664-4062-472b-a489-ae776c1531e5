package com.bdyl.erp.pisp.common.core.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * 基础实体类 所有业务实体都应继承此类，提供统一的基础字段和功能
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
public abstract class BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 创建时间 在插入记录时自动填充
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间 在插入和更新记录时自动填充
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人ID 在插入记录时自动填充
     */
    @TableField(value = "creator_id", fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 更新人ID 在插入和更新记录时自动填充
     */
    @TableField(value = "updater_id", fill = FieldFill.INSERT_UPDATE)
    private Long updaterId;

    /**
     * 额外信息 JSON格式，用于保存业务相关的扩展信息
     */
    @TableField(value = "additional_info")
    private String additionalInfo;

    /**
     * 备注信息 用于保存业务备注
     */
    @TableField(value = "remark")
    private String remark;
}
