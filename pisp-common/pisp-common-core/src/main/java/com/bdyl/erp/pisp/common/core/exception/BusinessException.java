package com.bdyl.erp.pisp.common.core.exception;

import lombok.Getter;

import com.bdyl.erp.pisp.common.core.result.ResponseCode;

/**
 * 业务异常类 用于处理业务逻辑中的异常情况
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
public class BusinessException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private final Integer code;

    /**
     * 错误消息
     */
    private final String message;

    /**
     * 错误数据
     */
    private final Object data;

    /**
     * 构造函数 - 使用默认错误码
     *
     * @param message 错误消息
     */
    public BusinessException(String message) {
        super(message);
        this.code = ResponseCode.BUSINESS_ERROR.getCode();
        this.message = message;
        this.data = null;
    }

    /**
     * 构造函数 - 指定错误码和消息
     *
     * @param code 错误码
     * @param message 错误消息
     */
    public BusinessException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
        this.data = null;
    }

    /**
     * 构造函数 - 指定错误码、消息和数据
     *
     * @param code 错误码
     * @param message 错误消息
     * @param data 错误数据
     */
    public BusinessException(Integer code, String message, Object data) {
        super(message);
        this.code = code;
        this.message = message;
        this.data = data;
    }

    /**
     * 构造函数 - 使用响应码枚举
     *
     * @param responseCode 响应码
     */
    public BusinessException(ResponseCode responseCode) {
        super(responseCode.getMessage());
        this.code = responseCode.getCode();
        this.message = responseCode.getMessage();
        this.data = null;
    }

    /**
     * 构造函数 - 使用响应码枚举和自定义消息
     *
     * @param responseCode 响应码
     * @param message 错误消息
     */
    public BusinessException(ResponseCode responseCode, String message) {
        super(message);
        this.code = responseCode.getCode();
        this.message = message;
        this.data = null;
    }

    /**
     * 构造函数 - 使用响应码枚举、自定义消息和数据
     *
     * @param responseCode 响应码
     * @param message 错误消息
     * @param data 错误数据
     */
    public BusinessException(ResponseCode responseCode, String message, Object data) {
        super(message);
        this.code = responseCode.getCode();
        this.message = message;
        this.data = data;
    }

    /**
     * 构造函数 - 包装其他异常
     *
     * @param message 错误消息
     * @param cause 异常 cause
     */
    public BusinessException(String message, Throwable cause) {
        super(message, cause);
        this.code = ResponseCode.BUSINESS_ERROR.getCode();
        this.message = message;
        this.data = null;
    }

    /**
     * 构造函数 - 包装其他异常并指定错误码
     *
     * @param code 错误码
     * @param message 错误消息
     * @param cause 异常 cause
     */
    public BusinessException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
        this.data = null;
    }

    /**
     * 构造函数 - 包装其他异常并使用响应码枚举
     *
     * @param responseCode 响应码
     * @param cause 异常 cause
     */
    public BusinessException(ResponseCode responseCode, Throwable cause) {
        super(responseCode.getMessage(), cause);
        this.code = responseCode.getCode();
        this.message = responseCode.getMessage();
        this.data = null;
    }

    /**
     * 构造函数 - 包装其他异常并使用响应码枚举和自定义消息
     *
     * @param responseCode 响应码
     * @param message 错误消息
     * @param cause 异常 cause
     */
    public BusinessException(ResponseCode responseCode, String message, Throwable cause) {
        super(message, cause);
        this.code = responseCode.getCode();
        this.message = message;
        this.data = null;
    }

    // ========== 静态工厂方法 ==========

    /**
     * 创建业务异常
     *
     * @param message 错误消息
     * @return BusinessException
     */
    public static BusinessException of(String message) {
        return new BusinessException(message);
    }

    /**
     * 创建业务异常
     *
     * @param code 错误码
     * @param message 错误消息
     * @return BusinessException
     */
    public static BusinessException of(Integer code, String message) {
        return new BusinessException(code, message);
    }

    /**
     * 创建业务异常
     *
     * @param responseCode 响应码
     * @return BusinessException
     */
    public static BusinessException of(ResponseCode responseCode) {
        return new BusinessException(responseCode);
    }

    /**
     * 创建业务异常
     *
     * @param responseCode 响应码
     * @param message 错误消息
     * @return BusinessException
     */
    public static BusinessException of(ResponseCode responseCode, String message) {
        return new BusinessException(responseCode, message);
    }

    /**
     * 创建业务异常（带数据）
     *
     * @param responseCode 响应码
     * @param message 错误消息
     * @param data 错误数据
     * @return BusinessException
     */
    public static BusinessException of(ResponseCode responseCode, String message, Object data) {
        return new BusinessException(responseCode, message, data);
    }

    /**
     * 包装异常
     *
     * @param cause 异常 cause
     * @return BusinessException
     */
    public static BusinessException wrap(Throwable cause) {
        if (cause instanceof BusinessException) {
            return (BusinessException) cause;
        }
        return new BusinessException("系统异常", cause);
    }

    /**
     * 包装异常
     *
     * @param message 错误消息
     * @param cause 异常 cause
     * @return BusinessException
     */
    public static BusinessException wrap(String message, Throwable cause) {
        if (cause instanceof BusinessException) {
            BusinessException be = (BusinessException) cause;
            return new BusinessException(be.getCode(), message, cause);
        }
        return new BusinessException(message, cause);
    }

    /**
     * 包装异常
     *
     * @param responseCode 响应码
     * @param cause 异常 cause
     * @return BusinessException
     */
    public static BusinessException wrap(ResponseCode responseCode, Throwable cause) {
        if (cause instanceof BusinessException) {
            return (BusinessException) cause;
        }
        return new BusinessException(responseCode, cause);
    }

    @Override
    public String toString() {
        return String.format("BusinessException{code=%d, message='%s', data=%s}", code, message, data);
    }
}
