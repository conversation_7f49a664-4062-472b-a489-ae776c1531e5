package com.bdyl.erp.pisp.common.core.result;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应状态码枚举 定义系统中所有可能的响应状态码
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum ResponseCode {

    // ========== 成功状态码 ==========
    /** 操作成功 */
    SUCCESS(200, "操作成功"),

    // ========== 客户端错误状态码 4xx ==========
    /** 请求参数错误 */
    BAD_REQUEST(400, "请求参数错误"),
    /** 未授权访问 */
    UNAUTHORIZED(401, "未授权访问"),
    /** 禁止访问 */
    FORBIDDEN(403, "禁止访问"),
    /** 资源不存在 */
    NOT_FOUND(404, "资源不存在"),
    /** 请求方法不允许 */
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    /** 资源冲突 */
    CONFLICT(409, "资源冲突"),
    /** 请求参数验证失败 */
    UNPROCESSABLE_ENTITY(422, "请求参数验证失败"),
    /** 请求过于频繁 */
    TOO_MANY_REQUESTS(429, "请求过于频繁"),

    // ========== 服务器错误状态码 5xx ==========
    /** 服务器内部错误 */
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    /** 网关错误 */
    BAD_GATEWAY(502, "网关错误"),
    /** 服务不可用 */
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    /** 网关超时 */
    GATEWAY_TIMEOUT(504, "网关超时"),

    // ========== 业务错误状态码 1xxx ==========
    // 用户相关错误 10xx
    /** 用户不存在 */
    USER_NOT_FOUND(1001, "用户不存在"),
    /** 用户已存在 */
    USER_ALREADY_EXISTS(1002, "用户已存在"),
    /** 用户已被禁用 */
    USER_DISABLED(1003, "用户已被禁用"),
    /** 用户密码错误 */
    USER_PASSWORD_ERROR(1004, "用户密码错误"),
    /** 用户登录已过期 */
    USER_LOGIN_EXPIRED(1005, "用户登录已过期"),

    // 权限相关错误 11xx
    /** 权限不足 */
    PERMISSION_DENIED(1101, "权限不足"),
    /** 角色不存在 */
    ROLE_NOT_FOUND(1102, "角色不存在"),
    /** 角色代码已存在 */
    ROLE_CODE_EXISTS(1103, "角色代码已存在"),
    /** 角色名称已存在 */
    ROLE_NAME_EXISTS(1104, "角色名称已存在"),
    /** 系统角色不能修改 */
    SYSTEM_ROLE_CANNOT_MODIFY(1105, "系统角色不能修改"),
    /** 系统角色不能删除 */
    SYSTEM_ROLE_CANNOT_DELETE(1106, "系统角色不能删除"),
    /** 系统角色不能禁用 */
    SYSTEM_ROLE_CANNOT_DISABLE(1107, "系统角色不能禁用"),
    /** 角色下有用户不能删除 */
    ROLE_HAS_USERS(1108, "角色下有用户，不能删除"),
    /** 菜单不存在 */
    MENU_NOT_FOUND(1109, "菜单不存在"),

    // 权限相关错误 115x-119x
    /** 权限不存在 */
    PERMISSION_NOT_FOUND(1150, "权限不存在"),
    /** 权限代码已存在 */
    PERMISSION_CODE_EXISTS(1151, "权限代码已存在"),
    /** 权限名称已存在 */
    PERMISSION_NAME_EXISTS(1152, "权限名称已存在"),
    /** 父权限不存在 */
    PARENT_PERMISSION_NOT_FOUND(1153, "父权限不存在"),
    /** 系统权限不能修改 */
    SYSTEM_PERMISSION_CANNOT_MODIFY(1154, "系统权限不能修改"),
    /** 系统权限不能删除 */
    SYSTEM_PERMISSION_CANNOT_DELETE(1155, "系统权限不能删除"),
    /** 权限有子权限不能删除 */
    PERMISSION_HAS_CHILDREN(1156, "权限有子权限，不能删除"),
    /** 权限被角色使用不能删除 */
    PERMISSION_IN_USE(1157, "权限被角色使用，不能删除"),
    /** 不允许循环引用 */
    CIRCULAR_REFERENCE_NOT_ALLOWED(1158, "不允许循环引用"),

    // 部门相关错误 11xx
    /** 部门不存在 */
    DEPARTMENT_NOT_FOUND(1110, "部门不存在"),
    /** 部门代码已存在 */
    DEPARTMENT_CODE_EXISTS(1111, "部门代码已存在"),
    /** 部门名称已存在 */
    DEPARTMENT_NAME_EXISTS(1112, "部门名称已存在"),
    /** 父部门不存在 */
    PARENT_DEPARTMENT_NOT_FOUND(1113, "父部门不存在"),
    /** 父部门未启用 */
    PARENT_DEPARTMENT_INACTIVE(1114, "父部门未启用"),
    /** 部门下有子部门不能删除 */
    DEPARTMENT_HAS_CHILDREN(1115, "部门下有子部门，不能删除"),
    /** 部门下有员工不能删除 */
    DEPARTMENT_HAS_EMPLOYEES(1116, "部门下有员工，不能删除"),
    /** 不能移动到子部门 */
    CANNOT_MOVE_TO_CHILD_DEPARTMENT(1117, "不能移动到子部门"),

    // 数据相关错误 12xx
    /** 数据不存在 */
    DATA_NOT_FOUND(1201, "数据不存在"),
    /** 数据已存在 */
    DATA_ALREADY_EXISTS(1202, "数据已存在"),
    /** 数据完整性约束违反 */
    DATA_INTEGRITY_VIOLATION(1203, "数据完整性约束违反"),
    /** 数据版本冲突 */
    DATA_VERSION_CONFLICT(1204, "数据版本冲突"),

    // 业务逻辑错误 13xx
    /** 业务处理失败 */
    BUSINESS_ERROR(1301, "业务处理失败"),
    /** 操作不被允许 */
    OPERATION_NOT_ALLOWED(1302, "操作不被允许"),
    /** 资源被锁定 */
    RESOURCE_LOCKED(1303, "资源被锁定"),
    /** 配额已超限 */
    QUOTA_EXCEEDED(1304, "配额已超限"),

    // 文件相关错误 14xx
    /** 文件不存在 */
    FILE_NOT_FOUND(1401, "文件不存在"),
    /** 文件上传失败 */
    FILE_UPLOAD_FAILED(1402, "文件上传失败"),
    /** 文件类型不支持 */
    FILE_TYPE_NOT_SUPPORTED(1403, "文件类型不支持"),
    /** 文件大小超限 */
    FILE_SIZE_EXCEEDED(1404, "文件大小超限"),

    // 网络相关错误 15xx
    /** 网络连接错误 */
    NETWORK_ERROR(1501, "网络连接错误"),
    /** 请求超时 */
    TIMEOUT_ERROR(1502, "请求超时"),
    /** 外部服务错误 */
    EXTERNAL_SERVICE_ERROR(1503, "外部服务错误"),

    // 配置相关错误 16xx
    /** 配置错误 */
    CONFIG_ERROR(1601, "配置错误"),
    /** 功能未启用 */
    FEATURE_NOT_ENABLED(1602, "功能未启用"),

    // 库存相关错误 20xx
    /** 库存记录不存在 */
    INVENTORY_NOT_FOUND(2001, "库存记录不存在"),
    /** 库存不足 */
    INVENTORY_INSUFFICIENT(2002, "库存不足"),
    /** 库存已被锁定 */
    INVENTORY_LOCKED(2003, "库存已被锁定"),

    // 采购相关错误 21xx
    /** 采购订单不存在 */
    PURCHASE_ORDER_NOT_FOUND(2101, "采购订单不存在"),
    /** 采购订单状态错误 */
    PURCHASE_ORDER_STATUS_ERROR(2102, "采购订单状态错误"),
    /** 供应商不存在 */
    SUPPLIER_NOT_FOUND(2103, "供应商不存在"),

    // 销售相关错误 22xx
    /** 销售订单不存在 */
    SALES_ORDER_NOT_FOUND(2201, "销售订单不存在"),
    /** 销售订单状态错误 */
    SALES_ORDER_STATUS_ERROR(2202, "销售订单状态错误"),
    /** 客户不存在 */
    CUSTOMER_NOT_FOUND(2203, "客户不存在"),

    // 财务相关错误 23xx
    /** 账户不存在 */
    ACCOUNT_NOT_FOUND(2301, "账户不存在"),
    /** 账户余额不足 */
    INSUFFICIENT_BALANCE(2302, "账户余额不足"),
    /** 支付失败 */
    PAYMENT_FAILED(2303, "支付失败"),

    // 零售相关错误 24xx
    /** 门店不存在 */
    STORE_NOT_FOUND(2401, "门店不存在"),
    /** POS机不存在 */
    POS_NOT_FOUND(2402, "POS机不存在"),
    /** 零售订单不存在 */
    RETAIL_ORDER_NOT_FOUND(2403, "零售订单不存在");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态消息
     */
    private final String message;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return ResponseCode
     */
    public static ResponseCode getByCode(Integer code) {
        for (ResponseCode responseCode : values()) {
            if (responseCode.getCode().equals(code)) {
                return responseCode;
            }
        }
        return null;
    }

    /**
     * 判断是否为成功状态码
     *
     * @return boolean
     */
    public boolean isSuccess() {
        return SUCCESS.equals(this);
    }

    /**
     * 判断是否为客户端错误
     *
     * @return boolean
     */
    public boolean isClientError() {
        return code >= 400 && code < 500;
    }

    /**
     * 判断是否为服务器错误
     *
     * @return boolean
     */
    public boolean isServerError() {
        return code >= 500 && code < 600;
    }

    /**
     * 判断是否为业务错误
     *
     * @return boolean
     */
    public boolean isBusinessError() {
        return code >= 1000;
    }
}
