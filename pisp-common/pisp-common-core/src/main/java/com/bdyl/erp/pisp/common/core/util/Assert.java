package com.bdyl.erp.pisp.common.core.util;

import java.util.Collection;
import java.util.Map;

import com.bdyl.erp.pisp.common.core.exception.BusinessException;
import com.bdyl.erp.pisp.common.core.result.ResponseCode;

/**
 * 断言工具类 用于参数验证和业务逻辑断言，失败时抛出BusinessException
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
public final class Assert {

    private Assert() {
        // 工具类，禁止实例化
    }

    /**
     * 断言表达式为真
     *
     * @param expression 断言表达式
     * @param message 错误消息
     */
    public static void isTrue(boolean expression, String message) {
        if (!expression) {
            throw BusinessException.of(message);
        }
    }

    /**
     * 断言表达式为真
     *
     * @param expression 断言表达式
     * @param responseCode 响应状态码
     */
    public static void isTrue(boolean expression, ResponseCode responseCode) {
        if (!expression) {
            throw BusinessException.of(responseCode);
        }
    }

    /**
     * 断言表达式为真
     *
     * @param expression 断言表达式
     * @param responseCode 响应状态码
     * @param message 错误消息
     */
    public static void isTrue(boolean expression, ResponseCode responseCode, String message) {
        if (!expression) {
            throw BusinessException.of(responseCode, message);
        }
    }

    /**
     * 断言表达式为假
     *
     * @param expression 断言表达式
     * @param message 错误消息
     */
    public static void isFalse(boolean expression, String message) {
        if (expression) {
            throw BusinessException.of(message);
        }
    }

    /**
     * 断言表达式为假
     *
     * @param expression 断言表达式
     * @param responseCode 响应状态码
     */
    public static void isFalse(boolean expression, ResponseCode responseCode) {
        if (expression) {
            throw BusinessException.of(responseCode);
        }
    }

    /**
     * 断言对象不为null
     *
     * @param object 断言对象
     * @param message 错误消息
     */
    public static void notNull(Object object, String message) {
        if (object == null) {
            throw BusinessException.of(message);
        }
    }

    /**
     * 断言对象不为null
     *
     * @param object 断言对象
     * @param responseCode 响应状态码
     */
    public static void notNull(Object object, ResponseCode responseCode) {
        if (object == null) {
            throw BusinessException.of(responseCode);
        }
    }

    /**
     * 断言对象不为null
     *
     * @param object 断言对象
     * @param responseCode 响应状态码
     * @param message 错误消息
     */
    public static void notNull(Object object, ResponseCode responseCode, String message) {
        if (object == null) {
            throw BusinessException.of(responseCode, message);
        }
    }

    /**
     * 断言对象为null
     *
     * @param object 断言对象
     * @param message 错误消息
     */
    public static void isNull(Object object, String message) {
        if (object != null) {
            throw BusinessException.of(message);
        }
    }

    /**
     * 断言对象为null
     *
     * @param object 断言对象
     * @param responseCode 响应状态码
     */
    public static void isNull(Object object, ResponseCode responseCode) {
        if (object != null) {
            throw BusinessException.of(responseCode);
        }
    }

    /**
     * 断言字符串不为空
     *
     * @param text 断言字符串
     * @param message 错误消息
     */
    public static void notEmpty(String text, String message) {
        if (ValidationUtils.isEmpty(text)) {
            throw BusinessException.of(message);
        }
    }

    /**
     * 断言字符串不为空
     *
     * @param text 断言字符串
     * @param responseCode 响应状态码
     */
    public static void notEmpty(String text, ResponseCode responseCode) {
        if (ValidationUtils.isEmpty(text)) {
            throw BusinessException.of(responseCode);
        }
    }

    /**
     * 断言字符串不为空
     *
     * @param text 断言字符串
     * @param responseCode 响应状态码
     * @param message 错误消息
     */
    public static void notEmpty(String text, ResponseCode responseCode, String message) {
        if (ValidationUtils.isEmpty(text)) {
            throw BusinessException.of(responseCode, message);
        }
    }

    /**
     * 断言集合不为空
     *
     * @param collection 断言集合
     * @param message 错误消息
     */
    public static void notEmpty(Collection<?> collection, String message) {
        if (collection == null || collection.isEmpty()) {
            throw BusinessException.of(message);
        }
    }

    /**
     * 断言集合不为空
     *
     * @param collection 断言集合
     * @param responseCode 响应状态码
     */
    public static void notEmpty(Collection<?> collection, ResponseCode responseCode) {
        if (collection == null || collection.isEmpty()) {
            throw BusinessException.of(responseCode);
        }
    }

    /**
     * 断言Map不为空
     *
     * @param map 断言Map
     * @param message 错误消息
     */
    public static void notEmpty(Map<?, ?> map, String message) {
        if (map == null || map.isEmpty()) {
            throw BusinessException.of(message);
        }
    }

    /**
     * 断言Map不为空
     *
     * @param map 断言Map
     * @param responseCode 响应状态码
     */
    public static void notEmpty(Map<?, ?> map, ResponseCode responseCode) {
        if (map == null || map.isEmpty()) {
            throw BusinessException.of(responseCode);
        }
    }

    /**
     * 断言数组不为空
     *
     * @param array 断言数组
     * @param message 错误消息
     */
    public static void notEmpty(Object[] array, String message) {
        if (array == null || array.length == 0) {
            throw BusinessException.of(message);
        }
    }

    /**
     * 断言数组不为空
     *
     * @param array 断言数组
     * @param responseCode 响应状态码
     */
    public static void notEmpty(Object[] array, ResponseCode responseCode) {
        if (array == null || array.length == 0) {
            throw BusinessException.of(responseCode);
        }
    }

    /**
     * 断言字符串长度在指定范围内
     *
     * @param text 断言字符串
     * @param minLength 最小长度
     * @param maxLength 最大长度
     * @param message 错误消息
     */
    public static void lengthBetween(String text, int minLength, int maxLength, String message) {
        if (!ValidationUtils.isLengthValid(text, minLength, maxLength)) {
            throw BusinessException.of(message);
        }
    }

    /**
     * 断言数字在指定范围内
     *
     * @param number 断言数字
     * @param min 最小值
     * @param max 最大值
     * @param message 错误消息
     */
    public static void between(Number number, Number min, Number max, String message) {
        if (!ValidationUtils.isNumberInRange(number, min, max)) {
            throw BusinessException.of(message);
        }
    }

    /**
     * 断言数字为正数
     *
     * @param number 断言数字
     * @param message 错误消息
     */
    public static void positive(Number number, String message) {
        if (!ValidationUtils.isPositive(number)) {
            throw BusinessException.of(message);
        }
    }

    /**
     * 断言数字为非负数
     *
     * @param number 断言数字
     * @param message 错误消息
     */
    public static void nonNegative(Number number, String message) {
        if (!ValidationUtils.isNonNegative(number)) {
            throw BusinessException.of(message);
        }
    }

    /**
     * 断言手机号格式正确
     *
     * @param phone 手机号
     * @param message 错误消息
     */
    public static void validPhone(String phone, String message) {
        if (!ValidationUtils.isValidPhone(phone)) {
            throw BusinessException.of(message);
        }
    }

    /**
     * 断言邮箱格式正确
     *
     * @param email 邮箱
     * @param message 错误消息
     */
    public static void validEmail(String email, String message) {
        if (!ValidationUtils.isValidEmail(email)) {
            throw BusinessException.of(message);
        }
    }

    /**
     * 断言身份证号格式正确
     *
     * @param idCard 身份证号
     * @param message 错误消息
     */
    public static void validIdCard(String idCard, String message) {
        if (!ValidationUtils.isValidIdCard(idCard)) {
            throw BusinessException.of(message);
        }
    }

    /**
     * 断言两个对象相等
     *
     * @param obj1 对象1
     * @param obj2 对象2
     * @param message 错误消息
     */
    public static void equals(Object obj1, Object obj2, String message) {
        if (obj1 == null ? obj2 != null : !obj1.equals(obj2)) {
            throw BusinessException.of(message);
        }
    }

    /**
     * 断言两个对象不相等
     *
     * @param obj1 对象1
     * @param obj2 对象2
     * @param message 错误消息
     */
    public static void notEquals(Object obj1, Object obj2, String message) {
        if (obj1 == null ? obj2 == null : obj1.equals(obj2)) {
            throw BusinessException.of(message);
        }
    }

    /**
     * 断言集合包含指定元素
     *
     * @param collection 断言集合
     * @param element 断言元素
     * @param message 错误消息
     */
    public static void contains(Collection<?> collection, Object element, String message) {
        if (collection == null || !collection.contains(element)) {
            throw BusinessException.of(message);
        }
    }

    /**
     * 断言集合不包含指定元素
     *
     * @param collection 断言集合
     * @param element 断言元素
     * @param message 错误消息
     */
    public static void notContains(Collection<?> collection, Object element, String message) {
        if (collection != null && collection.contains(element)) {
            throw BusinessException.of(message);
        }
    }
}
