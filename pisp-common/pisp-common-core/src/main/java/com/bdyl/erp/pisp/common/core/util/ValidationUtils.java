package com.bdyl.erp.pisp.common.core.util;

import java.net.URI;
import java.util.regex.Pattern;

import com.bdyl.erp.pisp.common.core.constant.Constants;

/**
 * 验证工具类 提供常用的数据验证方法
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
public final class ValidationUtils {

    private ValidationUtils() {
        // 工具类，禁止实例化
    }

    // 编译正则表达式模式，提高性能
    /**
     * 手机号正则表达式模式
     */
    private static final Pattern PHONE_PATTERN = Pattern.compile(Constants.PHONE_REGEX);
    /**
     * 邮箱正则表达式模式
     */
    private static final Pattern EMAIL_PATTERN = Pattern.compile(Constants.EMAIL_REGEX);
    /**
     * 身份证号正则表达式模式
     */
    private static final Pattern ID_CARD_PATTERN = Pattern.compile(Constants.ID_CARD_REGEX);
    /**
     * 统一社会信用代码正则表达式模式
     */
    private static final Pattern CREDIT_CODE_PATTERN = Pattern.compile(Constants.CREDIT_CODE_REGEX);

    /**
     * 验证手机号
     *
     * @param phone 手机号
     * @return 是否为有效的手机号
     */
    public static boolean isValidPhone(String phone) {
        return phone != null && PHONE_PATTERN.matcher(phone).matches();
    }

    /**
     * 验证邮箱
     *
     * @param email 邮箱
     * @return 是否为有效的邮箱
     */
    public static boolean isValidEmail(String email) {
        return email != null && EMAIL_PATTERN.matcher(email).matches();
    }

    /**
     * 验证身份证号
     *
     * @param idCard 身份证号
     * @return 是否为有效的身份证号
     */
    public static boolean isValidIdCard(String idCard) {
        if (idCard == null || !ID_CARD_PATTERN.matcher(idCard).matches()) {
            return false;
        }

        // 验证身份证校验位
        return validateIdCardChecksum(idCard);
    }

    /**
     * 验证统一社会信用代码
     *
     * @param creditCode 统一社会信用代码
     * @return 是否为有效的统一社会信用代码
     */
    public static boolean isValidCreditCode(String creditCode) {
        return creditCode != null && CREDIT_CODE_PATTERN.matcher(creditCode).matches();
    }

    /**
     * 验证密码强度
     *
     * @param password 密码
     * @return 是否为有效的密码
     */
    public static boolean isValidPassword(String password) {
        if (password == null) {
            return false;
        }

        int length = password.length();
        return length >= Constants.PASSWORD_MIN_LENGTH && length <= Constants.PASSWORD_MAX_LENGTH;
    }

    /**
     * 验证密码强度（强密码） 至少包含大写字母、小写字母、数字、特殊字符中的三种
     *
     * @param password 密码
     * @return 是否为强密码
     */
    public static boolean isStrongPassword(String password) {
        if (!isValidPassword(password)) {
            return false;
        }

        int typeCount = 0;

        // 检查是否包含小写字母
        if (password.matches(".*[a-z].*")) {
            typeCount++;
        }

        // 检查是否包含大写字母
        if (password.matches(".*[A-Z].*")) {
            typeCount++;
        }

        // 检查是否包含数字
        if (password.matches(".*[0-9].*")) {
            typeCount++;
        }

        // 检查是否包含特殊字符
        if (password.matches(".*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?].*")) {
            typeCount++;
        }

        return typeCount >= 3;
    }

    /**
     * 验证字符串是否为空或null
     *
     * @param str 字符串
     * @return 是否为空或null
     */
    public static boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * 验证字符串是否不为空
     *
     * @param str 字符串
     * @return 是否不为空
     */
    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    /**
     * 验证字符串长度是否在指定范围内
     *
     * @param str 字符串
     * @param minLength 最小长度
     * @param maxLength 最大长度
     * @return 是否在指定范围内
     */
    public static boolean isLengthValid(String str, int minLength, int maxLength) {
        if (str == null) {
            return false;
        }

        int length = str.length();
        return length >= minLength && length <= maxLength;
    }

    /**
     * 验证数字是否在指定范围内
     *
     * @param number 数字
     * @param min 最小值
     * @param max 最大值
     * @return 是否在指定范围内
     */
    public static boolean isNumberInRange(Number number, Number min, Number max) {
        if (number == null) {
            return false;
        }

        double value = number.doubleValue();
        double minValue = min != null ? min.doubleValue() : Double.MIN_VALUE;
        double maxValue = max != null ? max.doubleValue() : Double.MAX_VALUE;

        return value >= minValue && value <= maxValue;
    }

    /**
     * 验证是否为正数
     *
     * @param number 数字
     * @return 是否为正数
     */
    public static boolean isPositive(Number number) {
        return number != null && number.doubleValue() > 0;
    }

    /**
     * 验证是否为非负数
     *
     * @param number 数字
     * @return 是否为非负数
     */
    public static boolean isNonNegative(Number number) {
        return number != null && number.doubleValue() >= 0;
    }

    /**
     * 验证URL格式
     *
     * @param url URL
     * @return 是否为有效的URL格式
     */
    public static boolean isValidUrl(String url) {
        if (isEmpty(url)) {
            return false;
        }

        try {
            new URI(url).toURL();
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 验证IP地址格式
     *
     * @param ip IP地址
     * @return 是否为有效的IP地址格式
     */
    public static boolean isValidIp(String ip) {
        if (isEmpty(ip)) {
            return false;
        }

        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }

        try {
            for (String part : parts) {
                int num = Integer.parseInt(part);
                if (num < 0 || num > 255) {
                    return false;
                }
            }
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 验证身份证校验位
     */
    private static boolean validateIdCardChecksum(String idCard) {
        if (idCard.length() != 18) {
            return true; // 15位身份证不验证校验位
        }

        // 权重因子
        int[] weights = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
        // 校验码
        char[] checkCodes = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};

        int sum = 0;
        for (int i = 0; i < 17; i++) {
            sum += Character.getNumericValue(idCard.charAt(i)) * weights[i];
        }

        int checkIndex = sum % 11;
        char expectedCheckCode = checkCodes[checkIndex];
        char actualCheckCode = idCard.charAt(17);

        return expectedCheckCode == actualCheckCode;
    }
}
