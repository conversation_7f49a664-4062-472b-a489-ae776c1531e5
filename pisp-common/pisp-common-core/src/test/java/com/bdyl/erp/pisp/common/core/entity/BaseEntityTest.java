package com.bdyl.erp.pisp.common.core.entity;

import java.time.LocalDateTime;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * BaseEntity测试类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
class BaseEntityTest {

    /**
     * 测试实体类
     */
    @Data
    @ToString(callSuper = true)
    @EqualsAndHashCode(callSuper = true)
    static class TestEntity extends BaseEntity {
        /**
         * 实体名称
         */
        private String name;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    @Test
    void testBaseEntityFields() {
        TestEntity entity = new TestEntity();
        entity.setName("测试实体");

        LocalDateTime now = LocalDateTime.now();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        entity.setCreatorId(1L);
        entity.setUpdaterId(1L);
        entity.setAdditionalInfo("{\"key\":\"value\"}");
        entity.setRemark("测试备注");

        assertEquals("测试实体", entity.getName());
        assertEquals(now, entity.getCreateTime());
        assertEquals(now, entity.getUpdateTime());
        assertEquals(1L, entity.getCreatorId());
        assertEquals(1L, entity.getUpdaterId());
        assertEquals("{\"key\":\"value\"}", entity.getAdditionalInfo());
        assertEquals("测试备注", entity.getRemark());
    }

    @Test
    void testBaseEntitySerialization() {
        TestEntity entity = new TestEntity();
        entity.setName("测试实体");

        // 测试序列化接口
        assertNotNull(entity);
        assertTrue(entity instanceof java.io.Serializable);
    }

    @Test
    void testLombokGeneration() {
        TestEntity entity1 = new TestEntity();
        entity1.setName("测试1");
        entity1.setCreatorId(1L);

        TestEntity entity2 = new TestEntity();
        entity2.setName("测试1");
        entity2.setCreatorId(1L);

        // 测试equals和hashCode（由Lombok生成）
        assertEquals(entity1, entity2);
        assertEquals(entity1.hashCode(), entity2.hashCode());

        // 测试toString（由Lombok生成）
        String toString = entity1.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("TestEntity"));
    }
}
