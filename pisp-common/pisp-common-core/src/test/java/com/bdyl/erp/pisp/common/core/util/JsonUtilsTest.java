package com.bdyl.erp.pisp.common.core.util;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNotSame;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * JsonUtils测试类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
class JsonUtilsTest {

    static class TestObject {
        /**
         * 名称
         */
        private String name;
        /**
         * 年龄
         */
        private Integer age;
        /**
         * 创建时间
         */
        private LocalDateTime createTime;

        /**
         * 无参构造方法
         */
        TestObject() {}

        /**
         * 有参构造方法
         *
         * @param name 名称
         * @param age 年龄
         */
        TestObject(String name, Integer age) {
            this.name = name;
            this.age = age;
            this.createTime = LocalDateTime.now();
        }

        // getters and setters
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Integer getAge() {
            return age;
        }

        public void setAge(Integer age) {
            this.age = age;
        }

        public LocalDateTime getCreateTime() {
            return createTime;
        }

        public void setCreateTime(LocalDateTime createTime) {
            this.createTime = createTime;
        }
    }

    @Test
    void testToJson() {
        TestObject obj = new TestObject("张三", 25);
        String json = JsonUtils.toJson(obj);

        assertNotNull(json);
        assertTrue(json.contains("张三"));
        assertTrue(json.contains("25"));
    }

    @Test
    void testToJsonWithNull() {
        String json = JsonUtils.toJson(null);
        assertNull(json);
    }

    @Test
    void testToPrettyJson() {
        TestObject obj = new TestObject("张三", 25);
        String json = JsonUtils.toPrettyJson(obj);

        assertNotNull(json);
        assertTrue(json.contains("张三"));
        assertTrue(json.contains("25"));
        assertTrue(json.contains("\n")); // 格式化后应该包含换行符
    }

    @Test
    void testFromJson() {
        String json = "{\"name\":\"张三\",\"age\":25}";
        TestObject obj = JsonUtils.fromJson(json, TestObject.class);

        assertNotNull(obj);
        assertEquals("张三", obj.getName());
        assertEquals(25, obj.getAge());
    }

    @Test
    void testFromJsonWithNull() {
        TestObject obj = JsonUtils.fromJson(null, TestObject.class);
        assertNull(obj);

        obj = JsonUtils.fromJson("", TestObject.class);
        assertNull(obj);

        obj = JsonUtils.fromJson("   ", TestObject.class);
        assertNull(obj);
    }

    @Test
    void testFromJsonWithTypeReference() {
        String json = "[{\"name\":\"张三\",\"age\":25},{\"name\":\"李四\",\"age\":30}]";
        List<TestObject> list = JsonUtils.fromJson(json, new TypeReference<List<TestObject>>() {});

        assertNotNull(list);
        assertEquals(2, list.size());
        assertEquals("张三", list.get(0).getName());
        assertEquals("李四", list.get(1).getName());
    }

    @Test
    void testFromJsonToMap() {
        String json = "{\"name\":\"张三\",\"age\":25}";
        Map<String, Object> map = JsonUtils.fromJsonToMap(json);

        assertNotNull(map);
        assertEquals("张三", map.get("name"));
        assertEquals(25, map.get("age"));
    }

    @Test
    void testToMap() {
        TestObject obj = new TestObject("张三", 25);
        Map<String, Object> map = JsonUtils.toMap(obj);

        assertNotNull(map);
        assertEquals("张三", map.get("name"));
        assertEquals(25, map.get("age"));
    }

    @Test
    void testFromMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("name", "张三");
        map.put("age", 25);

        TestObject obj = JsonUtils.fromMap(map, TestObject.class);

        assertNotNull(obj);
        assertEquals("张三", obj.getName());
        assertEquals(25, obj.getAge());
    }

    @Test
    void testDeepCopy() {
        TestObject original = new TestObject("张三", 25);
        TestObject copy = JsonUtils.deepCopy(original, TestObject.class);

        assertNotNull(copy);
        assertNotSame(original, copy);
        assertEquals(original.getName(), copy.getName());
        assertEquals(original.getAge(), copy.getAge());
    }

    @Test
    void testIsValidJson() {
        assertTrue(JsonUtils.isValidJson("{\"name\":\"张三\"}"));
        assertTrue(JsonUtils.isValidJson("[1,2,3]"));
        assertTrue(JsonUtils.isValidJson("\"string\""));
        assertTrue(JsonUtils.isValidJson("123"));
        assertTrue(JsonUtils.isValidJson("true"));

        assertFalse(JsonUtils.isValidJson("{name:张三}"));
        assertFalse(JsonUtils.isValidJson("invalid"));
        assertFalse(JsonUtils.isValidJson(null));
        assertFalse(JsonUtils.isValidJson(""));
    }

    @Test
    void testGetObjectMapper() {
        assertNotNull(JsonUtils.getObjectMapper());
    }
}
