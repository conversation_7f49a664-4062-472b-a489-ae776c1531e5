<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.bdyl.erp.pisp</groupId>
    <artifactId>pisp-common</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>
  <artifactId>pisp-common-redis</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <name>PISP Redis configuration and tool components</name>
  <description>PISP系统Redis缓存配置和工具组件</description>
  <dependencies>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-json</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-redis</artifactId>
    </dependency>
  </dependencies>
</project>
