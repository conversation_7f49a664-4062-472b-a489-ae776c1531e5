package com.bdyl.erp.pisp.common.redis.config;

import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Import;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * Redis自动配置类
 *
 * 自动配置Redis相关组件： - 当RedisTemplate类存在时自动激活 - 导入RedisConfig配置类 - 提供开箱即用的Redis配置
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@AutoConfiguration
@ConditionalOnClass(RedisTemplate.class)
@Import(RedisConfig.class)
public class RedisAutoConfiguration {
    // 自动配置类，通过@Import导入具体配置
}
