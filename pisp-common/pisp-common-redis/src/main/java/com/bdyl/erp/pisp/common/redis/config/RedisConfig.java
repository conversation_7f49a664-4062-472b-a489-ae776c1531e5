package com.bdyl.erp.pisp.common.redis.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;

import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

/**
 * Redis配置类
 *
 * 提供Redis相关的Bean配置，包括： - RedisTemplate配置，支持对象序列化 - StringRedisTemplate配置，用于字符串操作 - 自定义ObjectMapper配置，优化JSON序列化
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Configuration
@ConditionalOnClass(RedisTemplate.class)
public class RedisConfig {

    /**
     * RedisTemplate配置
     *
     * 配置Redis模板，支持对象的序列化和反序列化： - Key使用String序列化器 - Value使用Jackson JSON序列化器 - 支持复杂对象的存储和读取
     *
     * @param connectionFactory Redis连接工厂
     * @param customizerProvider Jackson2ObjectMapperBuilderCustomizer提供者
     * @return 配置好的RedisTemplate实例
     */
    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory,
        ObjectProvider<Jackson2ObjectMapperBuilderCustomizer> customizerProvider) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        // 配置序列化器
        StringRedisSerializer stringSerializer = new StringRedisSerializer();
        GenericJackson2JsonRedisSerializer jsonSerializer =
            new GenericJackson2JsonRedisSerializer(objectMapper(customizerProvider));

        // key采用String的序列化方式
        template.setKeySerializer(stringSerializer);
        template.setHashKeySerializer(stringSerializer);

        // value采用jackson的序列化方式
        template.setValueSerializer(jsonSerializer);
        template.setHashValueSerializer(jsonSerializer);

        template.afterPropertiesSet();
        return template;
    }

    /**
     * StringRedisTemplate配置
     *
     * 配置字符串Redis模板，用于简单的字符串操作： - 适用于缓存简单字符串数据 - 性能优于对象序列化 - 适合计数器、标志位等场景
     *
     * @param connectionFactory Redis连接工厂
     * @return 配置好的StringRedisTemplate实例
     */
    @Bean
    public StringRedisTemplate stringRedisTemplate(RedisConnectionFactory connectionFactory) {
        StringRedisTemplate template = new StringRedisTemplate();
        template.setConnectionFactory(connectionFactory);
        return template;
    }

    /**
     * ObjectMapper配置
     *
     * 创建用于Redis序列化的ObjectMapper： - 支持所有字段的可见性 - 启用默认类型信息，支持多态序列化 - 集成Spring Boot的Jackson自定义配置
     *
     * @param customizerProvider Jackson2ObjectMapperBuilderCustomizer提供者
     * @return 配置好的ObjectMapper实例
     */
    private ObjectMapper objectMapper(ObjectProvider<Jackson2ObjectMapperBuilderCustomizer> customizerProvider) {
        var builder = new Jackson2ObjectMapperBuilder();
        customizerProvider.stream().forEach(c -> c.customize(builder));
        var mapper = builder.build();
        mapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        mapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);
        return mapper;
    }
}
