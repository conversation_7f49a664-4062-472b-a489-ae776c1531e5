<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.bdyl.erp.pisp</groupId>
        <artifactId>pisp-common</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>pisp-common-rocketmq</artifactId>
    <name>PISP RocketMQ message and Event class module</name>
    <description>PISP系统RocketMQ消息队列配置和工具组件</description>
</project>