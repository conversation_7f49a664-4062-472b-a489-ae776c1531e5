package com.bdyl.erp.pisp.common.security.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

import com.bdyl.erp.pisp.common.security.jwt.JwtAccessDeniedHandler;
import com.bdyl.erp.pisp.common.security.jwt.JwtAuthenticationEntryPoint;
import com.bdyl.erp.pisp.common.security.jwt.JwtAuthenticationFilter;
import com.bdyl.erp.pisp.common.security.jwt.JwtTokenUtil;

/**
 * PISP安全模块自动配置类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Configuration
public class PispSecurityAutoConfiguration {

    /**
     * 密码编码器
     *
     * @return BCrypt密码编码器
     */
    @Bean
    @ConditionalOnMissingBean
    public PasswordEncoder passwordEncoder() {
        log.info("初始化BCrypt密码编码器");
        return new BCryptPasswordEncoder();
    }

    /**
     * JWT Token工具类
     *
     * @return JWT Token工具类
     */
    @Bean
    @ConditionalOnMissingBean
    public JwtTokenUtil jwtTokenUtil() {
        log.info("初始化JWT Token工具类");
        return new JwtTokenUtil();
    }

    /**
     * JWT认证过滤器
     *
     * @param jwtTokenUtil JWT Token工具类
     * @return JWT认证过滤器
     */
    @Bean
    @ConditionalOnMissingBean
    public JwtAuthenticationFilter jwtAuthenticationFilter(JwtTokenUtil jwtTokenUtil) {
        log.info("初始化JWT认证过滤器");
        return new JwtAuthenticationFilter(jwtTokenUtil);
    }

    /**
     * JWT认证入口点
     *
     * @param objectMapper JSON对象映射器
     * @return JWT认证入口点
     */
    @Bean
    @ConditionalOnMissingBean
    public JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint(ObjectMapper objectMapper) {
        log.info("初始化JWT认证入口点");
        return new JwtAuthenticationEntryPoint(objectMapper);
    }

    /**
     * JWT访问拒绝处理器
     *
     * @param objectMapper JSON对象映射器
     * @return JWT访问拒绝处理器
     */
    @Bean
    @ConditionalOnMissingBean
    public JwtAccessDeniedHandler jwtAccessDeniedHandler(ObjectMapper objectMapper) {
        log.info("初始化JWT访问拒绝处理器");
        return new JwtAccessDeniedHandler(objectMapper);
    }

}
