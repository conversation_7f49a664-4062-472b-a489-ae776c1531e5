package com.bdyl.erp.pisp.common.security.context;

import java.io.Serializable;
import java.util.Objects;

/**
 * 用户上下文信息
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
public class UserContext implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 请求追踪ID
     */
    private String traceId;

    /**
     * 用户真实姓名
     */
    private String realName;

    /**
     * 用户角色
     */
    private String roles;

    /**
     * 用户权限
     */
    private String permissions;

    /**
     * 部门ID
     */
    private Long departmentId;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 默认构造函数
     */
    public UserContext() {}

    /**
     * 构造函数
     *
     * @param userId 用户ID
     * @param username 用户名
     * @param tenantId 租户ID
     * @param traceId 请求追踪ID
     */
    public UserContext(Long userId, String username, Long tenantId, String traceId) {
        this.userId = userId;
        this.username = username;
        this.tenantId = tenantId;
        this.traceId = traceId;
    }

    /**
     * 完整构造函数
     *
     * @param userId 用户ID
     * @param username 用户名
     * @param realName 真实姓名
     * @param tenantId 租户ID
     * @param traceId 请求追踪ID
     * @param roles 角色
     * @param permissions 权限
     * @param departmentId 部门ID
     * @param departmentName 部门名称
     */
    public UserContext(Long userId, String username, String realName, Long tenantId, String traceId, String roles,
        String permissions, Long departmentId, String departmentName) {
        this.userId = userId;
        this.username = username;
        this.realName = realName;
        this.tenantId = tenantId;
        this.traceId = traceId;
        this.roles = roles;
        this.permissions = permissions;
        this.departmentId = departmentId;
        this.departmentName = departmentName;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getRoles() {
        return roles;
    }

    public void setRoles(String roles) {
        this.roles = roles;
    }

    public String getPermissions() {
        return permissions;
    }

    public void setPermissions(String permissions) {
        this.permissions = permissions;
    }

    public Long getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    /**
     * 检查是否为系统用户
     *
     * @return 是否为系统用户
     */
    public boolean isSystemUser() {
        return userId != null && userId.equals(0L);
    }

    /**
     * 检查是否已认证
     *
     * @return 是否已认证
     */
    public boolean isAuthenticated() {
        return userId != null && userId > 0L && username != null;
    }

    /**
     * 创建系统用户上下文
     *
     * @return 系统用户上下文
     */
    public static UserContext createSystemContext() {
        UserContext context = new UserContext();
        context.setUserId(0L);
        context.setUsername("system");
        context.setRealName("系统用户");
        return context;
    }

    /**
     * 创建匿名用户上下文
     *
     * @return 匿名用户上下文
     */
    public static UserContext createAnonymousContext() {
        UserContext context = new UserContext();
        context.setUserId(-1L);
        context.setUsername("anonymous");
        context.setRealName("匿名用户");
        return context;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        UserContext that = (UserContext) o;
        return Objects.equals(userId, that.userId) && Objects.equals(username, that.username)
            && Objects.equals(tenantId, that.tenantId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(userId, username, tenantId);
    }

    @Override
    public String toString() {
        return "UserContext{" + "userId=" + userId + ", username='" + username + '\'' + ", realName='" + realName + '\''
            + ", tenantId=" + tenantId + ", traceId='" + traceId + '\'' + ", departmentId=" + departmentId
            + ", departmentName='" + departmentName + '\'' + '}';
    }
}
