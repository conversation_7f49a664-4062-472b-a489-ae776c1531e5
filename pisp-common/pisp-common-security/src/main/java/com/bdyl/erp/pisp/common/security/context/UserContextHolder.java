package com.bdyl.erp.pisp.common.security.context;

/**
 * 用户上下文持有者 用于在当前线程中存储用户信息
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
public final class UserContextHolder {

    /**
     * 用户上下文 ThreadLocal
     */
    private static final ThreadLocal<UserContext> CONTEXT_HOLDER = new ThreadLocal<>();

    private UserContextHolder() {
        // 工具类，禁止实例化
    }

    /**
     * 设置用户上下文
     *
     * @param userContext 用户上下文
     */
    public static void setContext(UserContext userContext) {
        CONTEXT_HOLDER.set(userContext);
    }

    /**
     * 获取用户上下文
     *
     * @return 用户上下文，如果未设置则返回null
     */
    public static UserContext getContext() {
        return CONTEXT_HOLDER.get();
    }

    /**
     * 获取用户上下文，如果为空则返回默认上下文
     *
     * @return 用户上下文
     */
    public static UserContext getContextOrDefault() {
        UserContext context = CONTEXT_HOLDER.get();
        return context != null ? context : UserContext.createAnonymousContext();
    }

    /**
     * 设置当前用户ID
     *
     * @param userId 用户ID
     */
    public static void setCurrentUserId(Long userId) {
        UserContext context = getOrCreateContext();
        context.setUserId(userId);
        CONTEXT_HOLDER.set(context);
    }

    /**
     * 获取当前用户ID
     *
     * @return 当前用户ID，如果未设置则返回0L
     */
    public static Long getCurrentUserId() {
        UserContext context = CONTEXT_HOLDER.get();
        return context != null && context.getUserId() != null ? context.getUserId() : 0L;
    }

    /**
     * 设置当前用户名
     *
     * @param username 用户名
     */
    public static void setCurrentUsername(String username) {
        UserContext context = getOrCreateContext();
        context.setUsername(username);
        CONTEXT_HOLDER.set(context);
    }

    /**
     * 获取当前用户名
     *
     * @return 当前用户名
     */
    public static String getCurrentUsername() {
        UserContext context = CONTEXT_HOLDER.get();
        return context != null ? context.getUsername() : null;
    }

    /**
     * 设置当前租户ID
     *
     * @param tenantId 租户ID
     */
    public static void setCurrentTenantId(Long tenantId) {
        UserContext context = getOrCreateContext();
        context.setTenantId(tenantId);
        CONTEXT_HOLDER.set(context);
    }

    /**
     * 获取当前租户ID
     *
     * @return 当前租户ID
     */
    public static Long getCurrentTenantId() {
        UserContext context = CONTEXT_HOLDER.get();
        return context != null ? context.getTenantId() : null;
    }

    /**
     * 设置当前请求追踪ID
     *
     * @param traceId 请求追踪ID
     */
    public static void setCurrentTraceId(String traceId) {
        UserContext context = getOrCreateContext();
        context.setTraceId(traceId);
        CONTEXT_HOLDER.set(context);
    }

    /**
     * 获取当前请求追踪ID
     *
     * @return 当前请求追踪ID
     */
    public static String getCurrentTraceId() {
        UserContext context = CONTEXT_HOLDER.get();
        return context != null ? context.getTraceId() : null;
    }

    /**
     * 设置用户上下文信息
     *
     * @param userId 用户ID
     * @param username 用户名
     * @param tenantId 租户ID
     * @param traceId 请求追踪ID
     */
    public static void setUserContext(Long userId, String username, Long tenantId, String traceId) {
        UserContext context = new UserContext(userId, username, tenantId, traceId);
        CONTEXT_HOLDER.set(context);
    }

    /**
     * 设置完整的用户上下文信息
     *
     * @param userContext 用户上下文对象
     */
    public static void setUserContext(UserContext userContext) {
        CONTEXT_HOLDER.set(userContext);
    }

    /**
     * 清除所有上下文信息
     */
    public static void clear() {
        CONTEXT_HOLDER.remove();
    }

    /**
     * 获取用户上下文信息
     *
     * @return 用户上下文信息
     */
    public static UserContext getUserContext() {
        return getContext();
    }

    /**
     * 获取或创建用户上下文
     *
     * @return 用户上下文
     */
    private static UserContext getOrCreateContext() {
        UserContext context = CONTEXT_HOLDER.get();
        if (context == null) {
            context = new UserContext();
            CONTEXT_HOLDER.set(context);
        }
        return context;
    }

}
