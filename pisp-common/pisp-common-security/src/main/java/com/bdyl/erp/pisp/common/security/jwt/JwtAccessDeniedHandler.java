package com.bdyl.erp.pisp.common.security.jwt;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.http.MediaType;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

/**
 * JWT访问拒绝处理器 处理权限不足的请求
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtAccessDeniedHandler implements AccessDeniedHandler {

    /**
     * JSON对象映射器
     */
    private final ObjectMapper objectMapper;

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response,
        AccessDeniedException accessDeniedException) throws IOException {

        log.warn("权限不足的请求访问: {} {}, 错误: {}", request.getMethod(), request.getRequestURI(),
            accessDeniedException.getMessage());

        response.setStatus(HttpServletResponse.SC_FORBIDDEN);
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());

        Map<String, Object> result = new HashMap<>();
        result.put("code", 403);
        result.put("message", "权限不足，拒绝访问");
        result.put("data", null);
        result.put("timestamp", System.currentTimeMillis());
        result.put("path", request.getRequestURI());

        response.getWriter().write(objectMapper.writeValueAsString(result));
    }
}
