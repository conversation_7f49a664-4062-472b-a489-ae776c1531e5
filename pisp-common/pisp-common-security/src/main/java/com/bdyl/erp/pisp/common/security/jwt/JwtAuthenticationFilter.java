package com.bdyl.erp.pisp.common.security.jwt;

import java.io.IOException;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.lang.NonNull;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import com.bdyl.erp.pisp.common.security.context.UserContext;
import com.bdyl.erp.pisp.common.security.context.UserContextHolder;
import com.bdyl.erp.pisp.common.security.user.PispUserDetails;

/**
 * JWT认证过滤器
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtAuthenticationFilter extends OncePerRequestFilter {
    /**
     * JWT Token工具类
     */
    private final JwtTokenUtil jwtTokenUtil;

    /**
     * Authorization请求头名称
     */
    private static final String AUTHORIZATION_HEADER = "Authorization";

    /**
     * Bearer Token前缀
     */
    private static final String BEARER_PREFIX = "Bearer ";

    /**
     * 过滤器处理方法
     *
     * @param request HTTP请求
     * @param response HTTP响应
     * @param filterChain 过滤器链
     * @throws ServletException Servlet异常
     * @throws IOException IO异常
     */
    @Override
    protected void doFilterInternal(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
        @NonNull FilterChain filterChain) throws ServletException, IOException {

        try {
            // 从请求中获取JWT Token
            String token = getTokenFromRequest(request);

            if (StringUtils.hasText(token) && jwtTokenUtil.validateToken(token)) {
                // 从Token中获取用户详情
                PispUserDetails userDetails = jwtTokenUtil.getUserDetailsFromToken(token);

                if (userDetails != null) {
                    // 设置Spring Security认证信息
                    UsernamePasswordAuthenticationToken authentication =
                        new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
                    authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                    SecurityContextHolder.getContext().setAuthentication(authentication);

                    // 设置用户上下文
                    setUserContext(userDetails, request);

                    log.debug("设置用户认证信息: {}", userDetails.getUsername());
                }
            }
        } catch (Exception e) {
            log.error("JWT认证过滤器处理失败", e);
            // 清除认证信息
            SecurityContextHolder.clearContext();
            UserContextHolder.clear();
        }

        filterChain.doFilter(request, response);
    }

    /**
     * 从请求中获取Token
     *
     * @param request HTTP请求
     * @return JWT Token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader(AUTHORIZATION_HEADER);
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith(BEARER_PREFIX)) {
            return bearerToken.substring(BEARER_PREFIX.length());
        }
        return null;
    }

    /**
     * 设置用户上下文
     *
     * @param userDetails 用户详情
     * @param request HTTP请求
     */
    private void setUserContext(PispUserDetails userDetails, HttpServletRequest request) {
        UserContext context = new UserContext();
        context.setUserId(userDetails.getUserId());
        context.setUsername(userDetails.getUsername());
        context.setRealName(userDetails.getRealName());
        context.setTenantId(userDetails.getTenantId());
        context.setDepartmentId(userDetails.getDepartmentId());
        context.setDepartmentName(userDetails.getDepartmentName());

        // 设置角色和权限
        if (userDetails.getRoles() != null) {
            context.setRoles(String.join(",", userDetails.getRoles()));
        }
        if (userDetails.getPermissions() != null) {
            context.setPermissions(String.join(",", userDetails.getPermissions()));
        }

        // 设置请求追踪ID
        String traceId = getTraceIdFromRequest(request);
        context.setTraceId(traceId);

        UserContextHolder.setContext(context);
    }

    /**
     * 从请求中获取追踪ID
     *
     * @param request HTTP请求
     * @return 追踪ID
     */
    private String getTraceIdFromRequest(HttpServletRequest request) {
        // 尝试从请求头获取追踪ID
        String traceId = request.getHeader("X-Trace-Id");
        if (!StringUtils.hasText(traceId)) {
            traceId = request.getHeader("X-Request-Id");
        }
        if (!StringUtils.hasText(traceId)) {
            // 生成新的追踪ID
            traceId = java.util.UUID.randomUUID().toString().replace("-", "");
        }
        return traceId;
    }

    @Override
    public void destroy() {
        // 清理资源
        super.destroy();
    }
}
