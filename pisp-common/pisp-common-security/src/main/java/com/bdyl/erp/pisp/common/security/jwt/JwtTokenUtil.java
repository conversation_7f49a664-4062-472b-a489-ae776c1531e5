package com.bdyl.erp.pisp.common.security.jwt;

import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import javax.crypto.SecretKey;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.UnsupportedJwtException;
import io.jsonwebtoken.security.Keys;
import io.jsonwebtoken.security.SignatureException;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.bdyl.erp.pisp.common.security.user.PispUserDetails;

/**
 * JWT Token工具类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Component
public class JwtTokenUtil {

    /**
     * JWT密钥
     */
    @Value("${pisp.security.jwt.secret:pisp-default-secret-key-for-jwt-token-generation-and-validation}")
    private String secret;

    /**
     * JWT过期时间（秒）
     */
    @Value("${pisp.security.jwt.expiration:86400}")
    private Long expiration;

    /**
     * JWT刷新Token过期时间（秒）
     */
    @Value("${pisp.security.jwt.refresh-expiration:604800}")
    private Long refreshExpiration;

    /**
     * JWT发行者
     */
    @Value("${pisp.security.jwt.issuer:pisp-system}")
    private String issuer;

    /**
     * 用户ID声明键
     */
    private static final String CLAIM_KEY_USER_ID = "userId";

    /**
     * 用户名声明键
     */
    private static final String CLAIM_KEY_USERNAME = "username";

    /**
     * 真实姓名声明键
     */
    private static final String CLAIM_KEY_REAL_NAME = "realName";

    /**
     * 租户ID声明键
     */
    private static final String CLAIM_KEY_TENANT_ID = "tenantId";

    /**
     * 角色声明键
     */
    private static final String CLAIM_KEY_ROLES = "roles";

    /**
     * 权限声明键
     */
    private static final String CLAIM_KEY_PERMISSIONS = "permissions";

    /**
     * 是否为管理员声明键
     */
    private static final String CLAIM_KEY_IS_ADMIN = "isAdmin";

    /**
     * Token类型声明键
     */
    private static final String CLAIM_KEY_TOKEN_TYPE = "tokenType";

    /**
     * 访问Token类型
     */
    private static final String TOKEN_TYPE_ACCESS = "access";

    /**
     * 刷新Token类型
     */
    private static final String TOKEN_TYPE_REFRESH = "refresh";

    /**
     * 生成访问Token
     *
     * @param userDetails 用户详情
     * @return JWT Token
     */
    public String generateAccessToken(PispUserDetails userDetails) {
        Map<String, Object> claims = createClaims(userDetails);
        claims.put(CLAIM_KEY_TOKEN_TYPE, TOKEN_TYPE_ACCESS);
        return generateToken(claims, userDetails.getUsername(), expiration);
    }

    /**
     * 生成刷新Token
     *
     * @param userDetails 用户详情
     * @return JWT刷新Token
     */
    public String generateRefreshToken(PispUserDetails userDetails) {
        Map<String, Object> claims = new HashMap<>();
        claims.put(CLAIM_KEY_USER_ID, userDetails.getUserId());
        claims.put(CLAIM_KEY_USERNAME, userDetails.getUsername());
        claims.put(CLAIM_KEY_TOKEN_TYPE, TOKEN_TYPE_REFRESH);
        return generateToken(claims, userDetails.getUsername(), refreshExpiration);
    }

    /**
     * 从Token中获取用户名
     *
     * @param token JWT Token
     * @return 用户名
     */
    public String getUsernameFromToken(String token) {
        return getClaimFromToken(token, Claims::getSubject);
    }

    /**
     * 从Token中获取用户ID
     *
     * @param token JWT Token
     * @return 用户ID
     */
    public Long getUserIdFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims != null ? claims.get(CLAIM_KEY_USER_ID, Long.class) : null;
    }

    /**
     * 从Token中获取租户ID
     *
     * @param token JWT Token
     * @return 租户ID
     */
    public Long getTenantIdFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims != null ? claims.get(CLAIM_KEY_TENANT_ID, Long.class) : null;
    }

    /**
     * 从Token中获取过期时间
     *
     * @param token JWT Token
     * @return 过期时间
     */
    public Date getExpirationDateFromToken(String token) {
        return getClaimFromToken(token, Claims::getExpiration);
    }

    /**
     * 从Token中获取用户详情
     *
     * @param token JWT Token
     * @return 用户详情
     */
    @SuppressWarnings("unchecked")
    public PispUserDetails getUserDetailsFromToken(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            if (claims == null) {
                return null;
            }

            PispUserDetails userDetails = new PispUserDetails();
            userDetails.setUserId(claims.get(CLAIM_KEY_USER_ID, Long.class));
            userDetails.setUsername(claims.get(CLAIM_KEY_USERNAME, String.class));
            userDetails.setRealName(claims.get(CLAIM_KEY_REAL_NAME, String.class));
            userDetails.setTenantId(claims.get(CLAIM_KEY_TENANT_ID, Long.class));
            userDetails.setIsAdmin(claims.get(CLAIM_KEY_IS_ADMIN, Boolean.class));

            // 处理角色和权限
            Object rolesObj = claims.get(CLAIM_KEY_ROLES);
            if (rolesObj instanceof Set) {
                userDetails.setRoles((Set<String>) rolesObj);
            }

            Object permissionsObj = claims.get(CLAIM_KEY_PERMISSIONS);
            if (permissionsObj instanceof Set) {
                userDetails.setPermissions((Set<String>) permissionsObj);
            }

            return userDetails;
        } catch (Exception e) {
            log.error("解析Token中的用户详情失败", e);
            return null;
        }
    }

    /**
     * 验证Token是否有效
     *
     * @param token JWT Token
     * @param userDetails 用户详情
     * @return 是否有效
     */
    public Boolean validateToken(String token, PispUserDetails userDetails) {
        final String username = getUsernameFromToken(token);
        return (username.equals(userDetails.getUsername()) && !isTokenExpired(token));
    }

    /**
     * 验证Token是否有效（仅验证Token本身）
     *
     * @param token JWT Token
     * @return 是否有效
     */
    public Boolean validateToken(String token) {
        try {
            getClaimsFromToken(token);
            return !isTokenExpired(token);
        } catch (Exception e) {
            log.debug("Token验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查Token是否过期
     *
     * @param token JWT Token
     * @return 是否过期
     */
    public Boolean isTokenExpired(String token) {
        final Date expirationDate = getExpirationDateFromToken(token);
        return expirationDate.before(new Date());
    }

    /**
     * 检查是否为刷新Token
     *
     * @param token JWT Token
     * @return 是否为刷新Token
     */
    public Boolean isRefreshToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims != null && TOKEN_TYPE_REFRESH.equals(claims.get(CLAIM_KEY_TOKEN_TYPE));
    }

    /**
     * 刷新Token
     *
     * @param token 原Token
     * @return 新Token
     */
    public String refreshToken(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            if (claims == null) {
                return null;
            }

            // 创建新的claims map，避免直接修改原始claims
            Map<String, Object> newClaims = new HashMap<>(claims);
            newClaims.put("iat", new Date());
            return generateToken(newClaims, claims.getSubject(), expiration);
        } catch (Exception e) {
            log.error("刷新Token失败", e);
            return null;
        }
    }

    /**
     * 生成Token
     *
     * @param claims 声明
     * @param subject 主题
     * @param expirationSeconds 过期时间（秒）
     * @return JWT Token
     */
    private String generateToken(Map<String, Object> claims, String subject, Long expirationSeconds) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expirationSeconds * 1000);

        return Jwts.builder().claims(claims).subject(subject).issuer(issuer).issuedAt(now).expiration(expiryDate)
            .signWith(getSigningKey()).compact();
    }

    /**
     * 创建用户声明
     *
     * @param userDetails 用户详情
     * @return 声明Map
     */
    private Map<String, Object> createClaims(PispUserDetails userDetails) {
        Map<String, Object> claims = new HashMap<>();
        claims.put(CLAIM_KEY_USER_ID, userDetails.getUserId());
        claims.put(CLAIM_KEY_USERNAME, userDetails.getUsername());
        claims.put(CLAIM_KEY_REAL_NAME, userDetails.getRealName());
        claims.put(CLAIM_KEY_TENANT_ID, userDetails.getTenantId());
        claims.put(CLAIM_KEY_ROLES, userDetails.getRoles());
        claims.put(CLAIM_KEY_PERMISSIONS, userDetails.getPermissions());
        claims.put(CLAIM_KEY_IS_ADMIN, userDetails.getIsAdmin());
        return claims;
    }

    /**
     * 从Token中获取声明
     *
     * @param token JWT Token
     * @param claimsResolver 声明解析器
     * @param <T> 返回类型
     * @return 声明值
     */
    private <T> T getClaimFromToken(String token, java.util.function.Function<Claims, T> claimsResolver) {
        final Claims claims = getClaimsFromToken(token);
        return claims != null ? claimsResolver.apply(claims) : null;
    }

    /**
     * 从Token中获取所有声明
     *
     * @param token JWT Token
     * @return 声明
     */
    private Claims getClaimsFromToken(String token) {
        try {
            return Jwts.parser().verifyWith(getSigningKey()).build().parseSignedClaims(token).getPayload();
        } catch (ExpiredJwtException e) {
            log.debug("Token已过期: {}", e.getMessage());
            throw e;
        } catch (UnsupportedJwtException e) {
            log.error("不支持的JWT Token: {}", e.getMessage());
        } catch (MalformedJwtException e) {
            log.error("JWT Token格式错误: {}", e.getMessage());
        } catch (SignatureException e) {
            log.error("JWT Token签名验证失败: {}", e.getMessage());
        } catch (IllegalArgumentException e) {
            log.error("JWT Token参数错误: {}", e.getMessage());
        } catch (Exception e) {
            log.error("解析JWT Token失败", e);
        }
        return null;
    }

    /**
     * 获取签名密钥
     *
     * @return 签名密钥
     */
    private SecretKey getSigningKey() {
        byte[] keyBytes = secret.getBytes(StandardCharsets.UTF_8);
        return Keys.hmacShaKeyFor(keyBytes);
    }
}
