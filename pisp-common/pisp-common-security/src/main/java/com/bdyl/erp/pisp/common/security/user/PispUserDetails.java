package com.bdyl.erp.pisp.common.security.user;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;

import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

/**
 * PISP系统认证用户详情 实现Spring Security的UserDetails接口
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
public class PispUserDetails implements UserDetails {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    @JsonIgnore
    private String password;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 部门ID
     */
    private Long departmentId;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 用户状态（1-启用，0-禁用）
     */
    private Integer status;

    /**
     * 角色列表
     */
    private Set<String> roles;

    /**
     * 权限列表
     */
    private Set<String> permissions;

    /**
     * 是否为超级管理员
     */
    private Boolean isAdmin;

    /**
     * 构造函数
     *
     * @param userId 用户ID
     * @param username 用户名
     * @param password 密码
     * @param realName 真实姓名
     * @param tenantId 租户ID
     * @param status 用户状态
     * @param roles 角色列表
     * @param permissions 权限列表
     */
    public PispUserDetails(Long userId, String username, String password, String realName, Long tenantId,
        Integer status, Set<String> roles, Set<String> permissions) {
        this.userId = userId;
        this.username = username;
        this.password = password;
        this.realName = realName;
        this.tenantId = tenantId;
        this.status = status;
        this.roles = roles;
        this.permissions = permissions;
        this.isAdmin = false;
    }

    /**
     * 获取用户权限集合
     *
     * @return 权限集合
     */
    @Override
    @JsonIgnore
    public Collection<? extends GrantedAuthority> getAuthorities() {
        // 合并角色和权限
        Set<String> allAuthorities = permissions != null ? permissions : Set.of();
        if (roles != null) {
            // 角色添加ROLE_前缀
            Set<String> roleAuthorities = roles.stream().map(role -> "ROLE_" + role).collect(Collectors.toSet());
            allAuthorities = allAuthorities.stream().collect(Collectors.toSet());
            allAuthorities.addAll(roleAuthorities);
        }

        return allAuthorities.stream().map(SimpleGrantedAuthority::new).collect(Collectors.toList());
    }

    /**
     * 账户是否未过期
     *
     * @return true-未过期
     */
    @Override
    @JsonIgnore
    public boolean isAccountNonExpired() {
        return true;
    }

    /**
     * 账户是否未锁定
     *
     * @return true-未锁定
     */
    @Override
    @JsonIgnore
    public boolean isAccountNonLocked() {
        return true;
    }

    /**
     * 凭证是否未过期
     *
     * @return true-未过期
     */
    @Override
    @JsonIgnore
    public boolean isCredentialsNonExpired() {
        return true;
    }

    /**
     * 账户是否启用
     *
     * @return true-启用
     */
    @Override
    @JsonIgnore
    public boolean isEnabled() {
        return status != null && status == 1;
    }

    /**
     * 检查是否有指定角色
     *
     * @param role 角色名称
     * @return 是否有该角色
     */
    public boolean hasRole(String role) {
        return roles != null && roles.contains(role);
    }

    /**
     * 检查是否有指定权限
     *
     * @param permission 权限名称
     * @return 是否有该权限
     */
    public boolean hasPermission(String permission) {
        return permissions != null && permissions.contains(permission);
    }

    /**
     * 检查是否有任意一个角色
     *
     * @param roleList 角色列表
     * @return 是否有任意一个角色
     */
    public boolean hasAnyRole(List<String> roleList) {
        if (roles == null || roleList == null) {
            return false;
        }
        return roleList.stream().anyMatch(roles::contains);
    }

    /**
     * 检查是否有任意一个权限
     *
     * @param permissionList 权限列表
     * @return 是否有任意一个权限
     */
    public boolean hasAnyPermission(List<String> permissionList) {
        if (permissions == null || permissionList == null) {
            return false;
        }
        return permissionList.stream().anyMatch(permissions::contains);
    }

    /**
     * 创建系统用户
     *
     * @return 系统用户详情
     */
    public static PispUserDetails createSystemUser() {
        PispUserDetails systemUser = new PispUserDetails();
        systemUser.setUserId(0L);
        systemUser.setUsername("system");
        systemUser.setRealName("系统用户");
        systemUser.setStatus(1);
        systemUser.setIsAdmin(true);
        systemUser.setRoles(Set.of("SYSTEM"));
        systemUser.setPermissions(Set.of("*"));
        return systemUser;
    }

    /**
     * 创建匿名用户
     *
     * @return 匿名用户详情
     */
    public static PispUserDetails createAnonymousUser() {
        PispUserDetails anonymousUser = new PispUserDetails();
        anonymousUser.setUserId(-1L);
        anonymousUser.setUsername("anonymous");
        anonymousUser.setRealName("匿名用户");
        anonymousUser.setStatus(0);
        anonymousUser.setIsAdmin(false);
        anonymousUser.setRoles(Set.of("ANONYMOUS"));
        anonymousUser.setPermissions(Set.of());
        return anonymousUser;
    }
}
