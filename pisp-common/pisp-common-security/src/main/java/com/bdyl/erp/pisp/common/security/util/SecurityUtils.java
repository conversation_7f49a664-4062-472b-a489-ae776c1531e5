package com.bdyl.erp.pisp.common.security.util;

import java.util.Collection;
import java.util.Optional;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;

import com.bdyl.erp.pisp.common.security.context.UserContextHolder;

/**
 * 安全工具类 提供安全相关的工具方法
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
public final class SecurityUtils {

    private SecurityUtils() {
        // 工具类，禁止实例化
    }

    /**
     * 获取当前认证信息
     *
     * @return 认证信息
     */
    public static Optional<Authentication> getCurrentAuthentication() {
        return Optional.ofNullable(SecurityContextHolder.getContext().getAuthentication());
    }

    /**
     * 获取当前用户名
     *
     * @return 当前用户名
     */
    public static Optional<String> getCurrentUsername() {
        return getCurrentAuthentication().map(Authentication::getName);
    }

    /**
     * 获取当前用户详情
     *
     * @return 当前用户详情
     */
    public static Optional<UserDetails> getCurrentUserDetails() {
        return getCurrentAuthentication().map(Authentication::getPrincipal).filter(UserDetails.class::isInstance)
            .map(UserDetails.class::cast);
    }

    /**
     * 获取当前用户权限
     *
     * @return 当前用户权限
     */
    public static Collection<? extends GrantedAuthority> getCurrentAuthorities() {
        return getCurrentAuthentication().map(Authentication::getAuthorities).orElse(null);
    }

    /**
     * 检查当前用户是否有指定权限
     *
     * @param authority 权限
     * @return 是否有权限
     */
    public static boolean hasAuthority(String authority) {
        Collection<? extends GrantedAuthority> authorities = getCurrentAuthorities();
        if (authorities == null) {
            return false;
        }

        return authorities.stream().anyMatch(grantedAuthority -> authority.equals(grantedAuthority.getAuthority()));
    }

    /**
     * 检查当前用户是否有任意一个指定权限
     *
     * @param authorities 权限列表
     * @return 是否有权限
     */
    public static boolean hasAnyAuthority(String... authorities) {
        if (authorities == null || authorities.length == 0) {
            return false;
        }

        for (String authority : authorities) {
            if (hasAuthority(authority)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查当前用户是否有所有指定权限
     *
     * @param authorities 权限列表
     * @return 是否有权限
     */
    public static boolean hasAllAuthorities(String... authorities) {
        if (authorities == null || authorities.length == 0) {
            return true;
        }

        for (String authority : authorities) {
            if (!hasAuthority(authority)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查当前用户是否有指定角色
     *
     * @param role 角色（不需要ROLE_前缀）
     * @return 是否有角色
     */
    public static boolean hasRole(String role) {
        return hasAuthority("ROLE_" + role);
    }

    /**
     * 检查当前用户是否有任意一个指定角色
     *
     * @param roles 角色列表（不需要ROLE_前缀）
     * @return 是否有角色
     */
    public static boolean hasAnyRole(String... roles) {
        if (roles == null || roles.length == 0) {
            return false;
        }

        for (String role : roles) {
            if (hasRole(role)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查当前用户是否已认证
     *
     * @return 是否已认证
     */
    public static boolean isAuthenticated() {
        return getCurrentAuthentication().map(Authentication::isAuthenticated).orElse(false);
    }

    /**
     * 检查当前用户是否为匿名用户
     *
     * @return 是否为匿名用户
     */
    public static boolean isAnonymous() {
        return !isAuthenticated();
    }

    /**
     * 获取当前用户ID（从UserContextHolder）
     *
     * @return 当前用户ID
     */
    public static Long getCurrentUserId() {
        return UserContextHolder.getCurrentUserId();
    }

    /**
     * 获取当前租户ID（从UserContextHolder）
     *
     * @return 当前租户ID
     */
    public static Long getCurrentTenantId() {
        return UserContextHolder.getCurrentTenantId();
    }

    /**
     * 获取当前请求追踪ID（从UserContextHolder）
     *
     * @return 当前请求追踪ID
     */
    public static String getCurrentTraceId() {
        return UserContextHolder.getCurrentTraceId();
    }

    /**
     * 检查是否为系统用户
     *
     * @return 是否为系统用户
     */
    public static boolean isSystemUser() {
        Long userId = getCurrentUserId();
        return userId != null && userId.equals(0L);
    }

    /**
     * 检查是否为超级管理员
     *
     * @return 是否为超级管理员
     */
    public static boolean isSuperAdmin() {
        return hasRole("SUPER_ADMIN");
    }

    /**
     * 检查是否为管理员
     *
     * @return 是否为管理员
     */
    public static boolean isAdmin() {
        return hasAnyRole("SUPER_ADMIN", "ADMIN");
    }
}
