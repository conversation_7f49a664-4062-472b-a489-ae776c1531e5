package com.bdyl.erp.pisp.common.security.context;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

/**
 * UserContextHolder测试类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
class UserContextHolderTest {

    @AfterEach
    void tearDown() {
        // 每个测试后清理上下文
        UserContextHolder.clear();
    }

    @Test
    void testSetAndGetUserId() {
        // 测试设置和获取用户ID
        UserContextHolder.setCurrentUserId(100L);
        assertEquals(100L, UserContextHolder.getCurrentUserId());

        // 测试清除用户ID
        UserContextHolder.clear();
        assertEquals(0L, UserContextHolder.getCurrentUserId());
    }

    @Test
    void testSetAndGetUsername() {
        // 测试设置和获取用户名
        UserContextHolder.setCurrentUsername("testuser");
        assertEquals("testuser", UserContextHolder.getCurrentUsername());

        // 测试清除用户名
        UserContextHolder.clear();
        assertNull(UserContextHolder.getCurrentUsername());
    }

    @Test
    void testSetAndGetTenantId() {
        // 测试设置和获取租户ID
        UserContextHolder.setCurrentTenantId(200L);
        assertEquals(200L, UserContextHolder.getCurrentTenantId());

        // 测试清除租户ID
        UserContextHolder.clear();
        assertNull(UserContextHolder.getCurrentTenantId());
    }

    @Test
    void testSetAndGetTraceId() {
        // 测试设置和获取追踪ID
        UserContextHolder.setCurrentTraceId("trace-123");
        assertEquals("trace-123", UserContextHolder.getCurrentTraceId());

        // 测试清除追踪ID
        UserContextHolder.clear();
        assertNull(UserContextHolder.getCurrentTraceId());
    }

    @Test
    void testSetUserContext() {
        // 测试批量设置用户上下文
        UserContextHolder.setUserContext(100L, "testuser", 200L, "trace-123");

        assertEquals(100L, UserContextHolder.getCurrentUserId());
        assertEquals("testuser", UserContextHolder.getCurrentUsername());
        assertEquals(200L, UserContextHolder.getCurrentTenantId());
        assertEquals("trace-123", UserContextHolder.getCurrentTraceId());
    }

    @Test
    void testGetUserContext() {
        // 设置用户上下文
        UserContextHolder.setUserContext(100L, "testuser", 200L, "trace-123");

        // 获取用户上下文对象
        UserContext context = UserContextHolder.getUserContext();
        assertNotNull(context);
        assertEquals(100L, context.getUserId());
        assertEquals("testuser", context.getUsername());
        assertEquals(200L, context.getTenantId());
        assertEquals("trace-123", context.getTraceId());
    }

    @Test
    void testClearAll() {
        // 设置所有上下文信息
        UserContextHolder.setUserContext(100L, "testuser", 200L, "trace-123");

        // 清除所有上下文
        UserContextHolder.clear();

        assertEquals(0L, UserContextHolder.getCurrentUserId());
        assertNull(UserContextHolder.getCurrentUsername());
        assertNull(UserContextHolder.getCurrentTenantId());
        assertNull(UserContextHolder.getCurrentTraceId());
    }

    @Test
    void testNullValues() {
        // 测试null值处理
        UserContextHolder.setCurrentUserId(null);
        assertEquals(0L, UserContextHolder.getCurrentUserId());

        UserContextHolder.setCurrentUsername(null);
        assertNull(UserContextHolder.getCurrentUsername());

        UserContextHolder.setCurrentTenantId(null);
        assertNull(UserContextHolder.getCurrentTenantId());

        UserContextHolder.setCurrentTraceId(null);
        assertNull(UserContextHolder.getCurrentTraceId());
    }

    @Test
    void testDefaultValues() {
        // 测试默认值
        assertEquals(0L, UserContextHolder.getCurrentUserId());
        assertNull(UserContextHolder.getCurrentUsername());
        assertNull(UserContextHolder.getCurrentTenantId());
        assertNull(UserContextHolder.getCurrentTraceId());
    }

    @Test
    void testUserContextToString() {
        // 测试UserContext的toString方法
        UserContextHolder.setUserContext(100L, "testuser", 200L, "trace-123");
        UserContext context = UserContextHolder.getUserContext();

        String contextString = context.toString();
        assertNotNull(contextString);
        // 只检查包含关键信息即可，不检查具体格式
        assertEquals(true, contextString.contains("userId=100"));
        assertEquals(true, contextString.contains("username='testuser'"));
        assertEquals(true, contextString.contains("tenantId=200"));
        assertEquals(true, contextString.contains("traceId='trace-123'"));
    }
}
