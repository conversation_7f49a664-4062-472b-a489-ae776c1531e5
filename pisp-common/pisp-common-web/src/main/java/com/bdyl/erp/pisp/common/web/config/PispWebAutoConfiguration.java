package com.bdyl.erp.pisp.common.web.config;

import java.time.format.DateTimeFormatter;

import lombok.extern.slf4j.Slf4j;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.format.datetime.standard.DateTimeFormatterRegistrar;
import org.springframework.lang.NonNull;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.bdyl.erp.pisp.common.web.exception.GlobalExceptionHandler;
import com.bdyl.erp.pisp.common.web.handler.PispMetaObjectHandler;

/**
 * PISP Web模块自动配置类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Configuration(proxyBeanMethods = false)
public class PispWebAutoConfiguration implements WebMvcConfigurer {

    /**
     * 全局异常处理器
     *
     * @return 全局异常处理器
     */
    @Bean
    @ConditionalOnMissingBean
    public GlobalExceptionHandler globalExceptionHandler() {
        log.info("初始化全局异常处理器");
        return new GlobalExceptionHandler();
    }

    /**
     * MyBatis-Plus自动填充处理器
     *
     * @return MyBatis-Plus自动填充处理器
     */
    @Bean
    @ConditionalOnMissingBean
    public PispMetaObjectHandler pispMetaObjectHandler() {
        log.info("初始化MyBatis-Plus自动填充处理器");
        return new PispMetaObjectHandler();
    }

    @Override
    public void addFormatters(@NonNull FormatterRegistry registry) {
        WebMvcConfigurer.super.addFormatters(registry);
        DateTimeFormatterRegistrar registrar = new DateTimeFormatterRegistrar();
        registrar.setDateTimeFormatter(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        registrar.setTimeFormatter(DateTimeFormatter.ofPattern("HH:mm:ss"));
        registrar.setDateFormatter(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        registrar.registerFormatters(registry);
    }
}
