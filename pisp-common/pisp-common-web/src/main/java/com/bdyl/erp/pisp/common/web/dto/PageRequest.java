package com.bdyl.erp.pisp.common.web.dto;

import java.io.Serializable;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;

import lombok.Data;
import lombok.experimental.Accessors;

import com.bdyl.erp.pisp.common.core.constant.Constants;

/**
 * 分页请求基础类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
public class PageRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页码，从1开始
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = Constants.DEFAULT_PAGE_NUM;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = Constants.MAX_PAGE_SIZE, message = "每页大小不能超过" + Constants.MAX_PAGE_SIZE)
    private Integer pageSize = Constants.DEFAULT_PAGE_SIZE;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序方向：asc/desc
     */
    private String orderDirection = "desc";

    /**
     * 是否查询总数
     */
    private Boolean searchCount = true;

    /**
     * 获取偏移量
     *
     * @return 偏移量
     */
    public long getOffset() {
        return (long) (pageNum - 1) * pageSize;
    }

    /**
     * 获取限制数量
     *
     * @return 限制数量
     */
    public long getLimit() {
        return pageSize;
    }

    /**
     * 是否升序排序
     *
     * @return 是否升序
     */
    public boolean isAsc() {
        return "asc".equalsIgnoreCase(orderDirection);
    }

    /**
     * 是否降序排序
     *
     * @return 是否降序
     */
    public boolean isDesc() {
        return "desc".equalsIgnoreCase(orderDirection);
    }
}
