package com.bdyl.erp.pisp.common.web.handler;

import java.time.LocalDateTime;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;

import org.springframework.stereotype.Component;

import com.bdyl.erp.pisp.common.security.context.UserContextHolder;

/**
 * MyBatis-Plus 自动填充处理器 自动填充BaseEntity中的审计字段
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Component
public class PispMetaObjectHandler implements com.baomidou.mybatisplus.core.handlers.MetaObjectHandler {

    /**
     * 创建时间字段名
     */
    private static final String CREATE_TIME = "createTime";

    /**
     * 更新时间字段名
     */
    private static final String UPDATE_TIME = "updateTime";

    /**
     * 创建人ID字段名
     */
    private static final String CREATOR_ID = "creatorId";

    /**
     * 更新人ID字段名
     */
    private static final String UPDATER_ID = "updaterId";

    /**
     * 插入时自动填充
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        log.debug("开始插入填充...");

        // 填充创建时间
        this.strictInsertFill(metaObject, CREATE_TIME, LocalDateTime.class, LocalDateTime.now());

        // 填充更新时间
        this.strictInsertFill(metaObject, UPDATE_TIME, LocalDateTime.class, LocalDateTime.now());

        // 填充创建人ID
        Long currentUserId = getCurrentUserId();
        this.strictInsertFill(metaObject, CREATOR_ID, Long.class, currentUserId);

        // 填充更新人ID
        this.strictInsertFill(metaObject, UPDATER_ID, Long.class, currentUserId);

        log.debug("插入填充完成");
    }

    /**
     * 更新时自动填充
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        log.debug("开始更新填充...");

        // 填充更新时间
        this.strictUpdateFill(metaObject, UPDATE_TIME, LocalDateTime.class, LocalDateTime.now());

        // 填充更新人ID
        Long currentUserId = getCurrentUserId();
        this.strictUpdateFill(metaObject, UPDATER_ID, Long.class, currentUserId);

        log.debug("更新填充完成");
    }

    /**
     * 获取当前用户ID 从UserContextHolder中获取当前用户ID
     *
     * @return 当前用户ID
     */
    private Long getCurrentUserId() {
        try {
            // 从UserContextHolder中获取用户ID
            return UserContextHolder.getCurrentUserId();
        } catch (Exception e) {
            log.debug("无法获取当前用户ID，使用系统默认用户ID");
            return 0L; // 系统用户ID
        }
    }
}
