package com.bdyl.erp.pisp.common.web.result;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 分页响应结果类
 *
 * @param <T> 数据类型
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class PageResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 数据列表
     */
    private List<T> records;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 当前页码
     */
    private Long current;

    /**
     * 每页大小
     */
    private Long size;

    /**
     * 总页数
     */
    private Long pages;

    /**
     * 是否有上一页
     */
    private Boolean hasPrevious;

    /**
     * 是否有下一页
     */
    private Boolean hasNext;

    /**
     * 是否为第一页
     */
    private Boolean isFirst;

    /**
     * 是否为最后一页
     */
    private Boolean isLast;

    /**
     * 构造空的分页结果
     *
     * @param <T> 数据类型
     * @return 空的分页结果
     */
    public static <T> PageResult<T> empty() {
        return new PageResult<T>().setRecords(Collections.emptyList()).setTotal(0L).setCurrent(1L).setSize(0L)
            .setPages(0L).setHasPrevious(false).setHasNext(false).setIsFirst(true).setIsLast(true);
    }

    /**
     * 构造空的分页结果（指定页码和页大小）
     *
     * @param <T> 数据类型
     * @param current 当前页码
     * @param size 页大小
     * @return 空的分页结果
     */
    public static <T> PageResult<T> empty(long current, long size) {
        return new PageResult<T>().setRecords(Collections.emptyList()).setTotal(0L).setCurrent(current).setSize(size)
            .setPages(0L).setHasPrevious(false).setHasNext(false).setIsFirst(current <= 1).setIsLast(true);
    }

    /**
     * 从MyBatis-Plus的IPage构造分页结果
     *
     * @param <T> 数据类型
     * @param page MyBatis-Plus分页对象
     * @return 分页结果
     */
    public static <T> PageResult<T> of(IPage<T> page) {
        return new PageResult<T>().setRecords(page.getRecords()).setTotal(page.getTotal()).setCurrent(page.getCurrent())
            .setSize(page.getSize()).setPages(page.getPages()).setHasPrevious(page.getCurrent() > 1)
            .setHasNext(page.getCurrent() < page.getPages()).setIsFirst(page.getCurrent() <= 1)
            .setIsLast(page.getCurrent() >= page.getPages());
    }

    /**
     * 构造分页结果
     *
     * @param <T> 数据类型
     * @param records 数据列表
     * @param total 总记录数
     * @param current 当前页码
     * @param size 页大小
     * @return 分页结果
     */
    public static <T> PageResult<T> of(List<T> records, long total, long current, long size) {
        long pages = size > 0 ? (total + size - 1) / size : 0;

        return new PageResult<T>().setRecords(records).setTotal(total).setCurrent(current).setSize(size).setPages(pages)
            .setHasPrevious(current > 1).setHasNext(current < pages).setIsFirst(current <= 1)
            .setIsLast(current >= pages);
    }

    /**
     * 转换数据类型
     *
     * @param <R> 新的数据类型
     * @param newRecords 新的数据列表
     * @return 转换后的分页结果
     */
    public <R> PageResult<R> map(List<R> newRecords) {
        return new PageResult<R>().setRecords(newRecords).setTotal(this.total).setCurrent(this.current)
            .setSize(this.size).setPages(this.pages).setHasPrevious(this.hasPrevious).setHasNext(this.hasNext)
            .setIsFirst(this.isFirst).setIsLast(this.isLast);
    }
}
