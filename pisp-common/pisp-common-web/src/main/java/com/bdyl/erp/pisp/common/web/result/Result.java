package com.bdyl.erp.pisp.common.web.result;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import com.bdyl.erp.pisp.common.core.result.ResponseCode;

/**
 * 统一响应结果类 用于封装所有API接口的响应数据
 *
 * @param <T> 响应数据类型
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Result<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 响应状态码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 请求追踪ID
     */
    private String traceId;

    /**
     * 构造成功响应（无数据）
     *
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> Result<T> success() {
        return new Result<T>().setCode(ResponseCode.SUCCESS.getCode()).setMessage(ResponseCode.SUCCESS.getMessage())
            .setTimestamp(System.currentTimeMillis());
    }

    /**
     * 构造成功响应（带数据）
     *
     * @param <T> 数据类型
     * @param data 响应数据
     * @return 成功响应
     */
    public static <T> Result<T> success(T data) {
        return new Result<T>().setCode(ResponseCode.SUCCESS.getCode()).setMessage(ResponseCode.SUCCESS.getMessage())
            .setData(data).setTimestamp(System.currentTimeMillis());
    }

    /**
     * 构造成功响应（自定义消息）
     *
     * @param <T> 数据类型
     * @param message 响应消息
     * @param data 响应数据
     * @return 成功响应
     */
    public static <T> Result<T> success(String message, T data) {
        return new Result<T>().setCode(ResponseCode.SUCCESS.getCode()).setMessage(message).setData(data)
            .setTimestamp(System.currentTimeMillis());
    }

    /**
     * 构造失败响应
     *
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> Result<T> error() {
        return new Result<T>().setCode(ResponseCode.INTERNAL_SERVER_ERROR.getCode())
            .setMessage(ResponseCode.INTERNAL_SERVER_ERROR.getMessage()).setTimestamp(System.currentTimeMillis());
    }

    /**
     * 构造失败响应（自定义消息）
     *
     * @param <T> 数据类型
     * @param message 错误消息
     * @return 失败响应
     */
    public static <T> Result<T> error(String message) {
        return new Result<T>().setCode(ResponseCode.INTERNAL_SERVER_ERROR.getCode()).setMessage(message)
            .setTimestamp(System.currentTimeMillis());
    }

    /**
     * 构造失败响应（自定义状态码和消息）
     *
     * @param <T> 数据类型
     * @param code 状态码
     * @param message 错误消息
     * @return 失败响应
     */
    public static <T> Result<T> error(Integer code, String message) {
        return new Result<T>().setCode(code).setMessage(message).setTimestamp(System.currentTimeMillis());
    }

    /**
     * 构造失败响应（使用响应码枚举）
     *
     * @param <T> 数据类型
     * @param responseCode 响应码枚举
     * @return 失败响应
     */
    public static <T> Result<T> error(ResponseCode responseCode) {
        return new Result<T>().setCode(responseCode.getCode()).setMessage(responseCode.getMessage())
            .setTimestamp(System.currentTimeMillis());
    }

    /**
     * 构造失败响应（使用响应码枚举和自定义消息）
     *
     * @param <T> 数据类型
     * @param responseCode 响应码枚举
     * @param message 自定义消息
     * @return 失败响应
     */
    public static <T> Result<T> error(ResponseCode responseCode, String message) {
        return new Result<T>().setCode(responseCode.getCode()).setMessage(message)
            .setTimestamp(System.currentTimeMillis());
    }

    /**
     * 判断是否成功
     *
     * @return 是否成功
     */
    public boolean isSuccess() {
        return ResponseCode.SUCCESS.getCode().equals(this.code);
    }

    /**
     * 判断是否失败
     *
     * @return 是否失败
     */
    public boolean isError() {
        return !isSuccess();
    }
}
