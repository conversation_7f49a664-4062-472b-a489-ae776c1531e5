package com.bdyl.erp.pisp.common.web.handler;

import org.apache.ibatis.reflection.MetaObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.mock;

/**
 * PispMetaObjectHandler测试类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
class PispMetaObjectHandlerTest {
    /**
     * 测试对象
     */
    private PispMetaObjectHandler handler;
    /**
     * MetaObject对象
     */
    private MetaObject metaObject;

    @BeforeEach
    void setUp() {
        handler = new PispMetaObjectHandler();
        metaObject = mock(MetaObject.class);
    }

    @Test
    void testHandlerCreation() {
        // 测试处理器创建
        PispMetaObjectHandler newHandler = new PispMetaObjectHandler();
        assertNotNull(newHandler);
    }
}
