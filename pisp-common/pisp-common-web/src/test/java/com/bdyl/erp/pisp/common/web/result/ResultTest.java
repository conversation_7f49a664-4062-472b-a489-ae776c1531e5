package com.bdyl.erp.pisp.common.web.result;

import org.junit.jupiter.api.Test;

import com.bdyl.erp.pisp.common.core.result.ResponseCode;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Result测试类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
class ResultTest {

    @Test
    void testSuccessResult() {
        Result<String> result = Result.success();

        assertTrue(result.isSuccess());
        assertFalse(result.isError());
        assertEquals(ResponseCode.SUCCESS.getCode(), result.getCode());
        assertEquals(ResponseCode.SUCCESS.getMessage(), result.getMessage());
        assertNull(result.getData());
        assertNotNull(result.getTimestamp());
    }

    @Test
    void testSuccessResultWithData() {
        String data = "测试数据";
        Result<String> result = Result.success(data);

        assertTrue(result.isSuccess());
        assertEquals(ResponseCode.SUCCESS.getCode(), result.getCode());
        assertEquals(ResponseCode.SUCCESS.getMessage(), result.getMessage());
        assertEquals(data, result.getData());
        assertNotNull(result.getTimestamp());
    }

    @Test
    void testSuccessResultWithCustomMessage() {
        String message = "自定义成功消息";
        String data = "测试数据";
        Result<String> result = Result.success(message, data);

        assertTrue(result.isSuccess());
        assertEquals(ResponseCode.SUCCESS.getCode(), result.getCode());
        assertEquals(message, result.getMessage());
        assertEquals(data, result.getData());
    }

    @Test
    void testErrorResult() {
        Result<String> result = Result.error();

        assertFalse(result.isSuccess());
        assertTrue(result.isError());
        assertEquals(ResponseCode.INTERNAL_SERVER_ERROR.getCode(), result.getCode());
        assertEquals(ResponseCode.INTERNAL_SERVER_ERROR.getMessage(), result.getMessage());
        assertNull(result.getData());
        assertNotNull(result.getTimestamp());
    }

    @Test
    void testErrorResultWithMessage() {
        String message = "自定义错误消息";
        Result<String> result = Result.error(message);

        assertTrue(result.isError());
        assertEquals(ResponseCode.INTERNAL_SERVER_ERROR.getCode(), result.getCode());
        assertEquals(message, result.getMessage());
    }

    @Test
    void testErrorResultWithCodeAndMessage() {
        Integer code = 1001;
        String message = "业务错误";
        Result<String> result = Result.error(code, message);

        assertTrue(result.isError());
        assertEquals(code, result.getCode());
        assertEquals(message, result.getMessage());
    }

    @Test
    void testErrorResultWithResponseCode() {
        Result<String> result = Result.error(ResponseCode.USER_NOT_FOUND);

        assertTrue(result.isError());
        assertEquals(ResponseCode.USER_NOT_FOUND.getCode(), result.getCode());
        assertEquals(ResponseCode.USER_NOT_FOUND.getMessage(), result.getMessage());
    }

    @Test
    void testChainedCalls() {
        Result<String> result = Result.<String>success().setData("测试数据").setTraceId("trace-123");

        assertTrue(result.isSuccess());
        assertEquals("测试数据", result.getData());
        assertEquals("trace-123", result.getTraceId());
    }

    @Test
    void testSerialization() {
        Result<String> result = Result.success("测试数据");

        // 测试序列化接口
        assertNotNull(result);
        assertTrue(result instanceof java.io.Serializable);
    }
}
