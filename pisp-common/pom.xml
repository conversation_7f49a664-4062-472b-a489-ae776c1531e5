<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.bdyl.erp.pisp</groupId>
        <artifactId>pisp-root</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>pisp-common</artifactId>
    <packaging>pom</packaging>
    <name>PISP Common module</name>
    <description>PISP系统公共组件模块</description>

    <modules>
        <module>pisp-common-core</module>
        <module>pisp-common-security</module>
        <module>pisp-common-redis</module>
        <module>pisp-common-rocketmq</module>
        <module>pisp-common-web</module>
    </modules>
</project>