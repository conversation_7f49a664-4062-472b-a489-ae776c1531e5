server:
  port: 9999

spring:
  application:
    name: pisp-gateway
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: pisp-system
      config:
        server-addr: localhost:8848
        namespace: pisp-system
        file-extension: yml
        group: GATEWAY_GROUP

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
