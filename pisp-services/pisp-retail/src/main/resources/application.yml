server:
  port: 8009

spring:
  application:
    name: pisp-retail-service
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: pisp-system
      config:
        server-addr: localhost:8848
        namespace: pisp-system
        file-extension: yml
        group: DEFAULT_GROUP

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
