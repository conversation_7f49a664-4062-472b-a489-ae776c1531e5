# PISP 用户管理服务

## 项目概述

PISP用户管理服务是PISP ERP系统的核心模块之一，负责用户、角色、权限和部门的管理。该服务基于Spring Boot 3.4.7和MyBatis-Plus 3.5.12构建，使用PostgreSQL作为数据库，支持微服务架构。

## 技术栈

- **Java**: 21
- **Spring Boot**: 3.4.7
- **Spring Cloud**: 2024.0.0
- **MyBatis-Plus**: 3.5.12
- **数据库**: PostgreSQL 17
- **缓存**: Redis
- **服务注册**: Nacos
- **数据库迁移**: Liquibase
- **构建工具**: Maven 3.9+

## 功能特性

### 用户管理
- 用户创建、更新、删除
- 用户状态管理（激活、停用、锁定）
- 密码管理（修改密码、重置密码）
- 用户信息查询和分页
- 批量操作支持

### 角色管理
- 角色创建和管理
- 角色权限分配
- 用户角色关联

### 权限管理
- 权限定义和管理
- 基于角色的权限控制
- 权限继承和层级管理

### 部门管理
- 部门层级结构
- 用户部门关联
- 部门查询和管理

## 项目结构

```
pisp-user/
├── src/main/java/com/bdyl/erp/pisp/user/
│   ├── config/                 # 配置类
│   │   ├── AsyncConfig.java
│   │   ├── DatabaseConfig.java
│   │   ├── RedisConfig.java
│   │   └── WebConfig.java
│   ├── controller/             # 控制器层
│   │   └── UserController.java
│   ├── dto/                    # 数据传输对象
│   │   ├── request/
│   │   │   ├── PasswordChangeRequest.java
│   │   │   ├── UserCreateRequest.java
│   │   │   ├── UserQueryRequest.java
│   │   │   └── UserUpdateRequest.java
│   │   └── response/
│   │       └── UserResponse.java
│   ├── entity/                 # 实体类
│   │   ├── Department.java
│   │   ├── Permission.java
│   │   ├── Role.java
│   │   ├── RolePermission.java
│   │   ├── User.java
│   │   └── UserRole.java
│   ├── enums/                  # 枚举类
│   │   └── UserStatus.java
│   ├── mapper/                 # 数据访问层
│   │   └── UserMapper.java
│   ├── service/                # 服务层
│   │   ├── impl/
│   │   │   └── UserServiceImpl.java
│   │   └── UserService.java
│   └── UserServiceApplication.java  # 启动类
├── src/main/resources/
│   ├── db/changelog/           # 数据库迁移脚本
│   │   ├── 001-create-user-tables.xml
│   │   ├── 002-init-user-data.xml
│   │   └── db.changelog-master.xml
│   ├── application.yml         # 主配置文件
│   ├── application-dev.yml     # 开发环境配置
│   ├── application-prod.yml    # 生产环境配置
│   └── application-test.yml    # 测试环境配置
└── src/test/java/              # 测试代码
    ├── controller/
    │   └── UserControllerTest.java
    ├── integration/
    │   └── UserServiceIntegrationTest.java
    └── service/
        ├── UserServiceSimpleTest.java
        └── UserServiceTest.java
```

## 数据库设计

### 核心表结构

1. **sys_users** - 用户表
   - 用户基本信息（用户名、邮箱、手机号、真实姓名等）
   - 密码哈希存储
   - 用户状态管理
   - 登录信息记录

2. **sys_departments** - 部门表
   - 部门层级结构
   - 部门基本信息
   - 部门负责人

3. **sys_roles** - 角色表
   - 角色定义
   - 角色状态管理

4. **sys_permissions** - 权限表
   - 权限定义
   - 权限类型（菜单、API等）
   - 权限层级

5. **sys_user_roles** - 用户角色关联表
6. **sys_role_permissions** - 角色权限关联表

## API接口

### 用户管理接口

- `POST /api/users` - 创建用户
- `PUT /api/users/{userId}` - 更新用户
- `DELETE /api/users/{userId}` - 删除用户
- `DELETE /api/users/batch` - 批量删除用户
- `GET /api/users/{userId}` - 获取用户详情
- `GET /api/users` - 分页查询用户列表
- `GET /api/users/department/{departmentId}` - 根据部门查询用户

### 密码管理接口

- `PUT /api/users/password/change` - 修改密码
- `PUT /api/users/{userId}/password/reset` - 重置密码

### 用户状态管理接口

- `PUT /api/users/{userId}/activate` - 激活用户
- `PUT /api/users/{userId}/deactivate` - 停用用户
- `PUT /api/users/{userId}/lock` - 锁定用户
- `PUT /api/users/status/batch` - 批量更新用户状态

### 角色管理接口

- `PUT /api/users/{userId}/roles` - 分配角色给用户
- `DELETE /api/users/{userId}/roles` - 移除用户角色

### 数据验证接口

- `GET /api/users/check/username` - 检查用户名是否存在
- `GET /api/users/check/email` - 检查邮箱是否存在
- `GET /api/users/check/phone` - 检查手机号是否存在

## 配置说明

### 应用配置

主要配置项包括：
- 数据源配置（PostgreSQL）
- Redis配置
- MyBatis-Plus配置
- Liquibase配置
- 服务注册配置（Nacos）
- 日志配置

### 自定义配置

```yaml
pisp:
  user:
    password:
      default: "123456"        # 默认密码
      strength: 10             # 密码加密强度
      expire-days: 90          # 密码过期天数
    login:
      max-fail-count: 5        # 最大失败次数
      lock-minutes: 30         # 锁定时间
    cache:
      user-info-ttl: 3600      # 用户信息缓存时间
      permission-ttl: 1800     # 权限信息缓存时间
```

## 运行说明

### 环境要求

- JDK 21+
- Maven 3.9+
- PostgreSQL 17+
- Redis 6+
- Nacos 2.4+

### 启动步骤

1. 启动PostgreSQL数据库
2. 启动Redis服务
3. 启动Nacos服务
4. 配置数据库连接信息
5. 运行应用：`mvn spring-boot:run`

### 数据库初始化

应用启动时会自动执行Liquibase迁移脚本，创建必要的表结构和初始数据：
- 默认管理员用户：admin/admin123
- 基础部门结构
- 基础角色和权限

## 测试

### 单元测试

```bash
# 运行所有测试
mvn test

# 运行特定测试
mvn test -Dtest=UserServiceSimpleTest
```

### 集成测试

```bash
# 运行集成测试
mvn test -Dtest=UserServiceIntegrationTest
```

## 开发规范

### 代码规范

- 使用Lombok简化Bean定义
- 使用MyBatis-Plus QueryWrapper构建查询条件
- 统一异常处理
- 统一响应格式

### 数据库规范

- 使用Liquibase管理数据库变更
- 统一字段命名规范
- 软删除支持
- 乐观锁支持

## 监控和运维

### 健康检查

- `/actuator/health` - 应用健康状态
- `/actuator/info` - 应用信息
- `/actuator/metrics` - 应用指标

### 日志

- 支持不同环境的日志级别配置
- 结构化日志输出
- 日志文件轮转

## 版本历史

- v1.0.0 - 初始版本，包含基础用户管理功能

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码
4. 创建Pull Request

## 许可证

本项目采用MIT许可证。
