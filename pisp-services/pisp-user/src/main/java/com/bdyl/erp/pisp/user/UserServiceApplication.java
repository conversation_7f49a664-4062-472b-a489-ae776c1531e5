package com.bdyl.erp.pisp.user;

import org.mybatis.spring.annotation.MapperScan;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 用户服务启动类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@SpringBootApplication(scanBasePackages = {"com.bdyl.erp.pisp.user", "com.bdyl.erp.pisp.common"})
@EnableDiscoveryClient
@EnableTransactionManagement
@MapperScan("com.bdyl.erp.pisp.user.mapper")
public class UserServiceApplication {
    /**
     * 主函数
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        SpringApplication.run(UserServiceApplication.class, args);
    }
}
