package com.bdyl.erp.pisp.user.config;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import com.bdyl.erp.pisp.common.security.jwt.JwtAccessDeniedHandler;
import com.bdyl.erp.pisp.common.security.jwt.JwtAuthenticationEntryPoint;
import com.bdyl.erp.pisp.common.security.jwt.JwtAuthenticationFilter;

/**
 * Spring Security配置类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
public class SecurityConfig {

    /**
     * JWT认证入口点
     */
    @Autowired
    private JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;

    /**
     * JWT访问拒绝处理器
     */
    @Autowired
    private JwtAccessDeniedHandler jwtAccessDeniedHandler;

    /**
     * JWT认证过滤器
     */
    @Autowired
    private JwtAuthenticationFilter jwtAuthenticationFilter;

    /**
     * 认证管理器
     *
     * @param config 认证配置
     * @return 认证管理器
     * @throws Exception 异常
     */
    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }

    /**
     * 安全过滤器链配置
     *
     * @param http HTTP安全配置
     * @return 安全过滤器链
     * @throws Exception 异常
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        log.info("配置Spring Security过滤器链");

        http
            // 禁用CSRF
            .csrf(AbstractHttpConfigurer::disable)

            // 禁用CORS（由网关处理）
            .cors(AbstractHttpConfigurer::disable)

            // 会话管理：无状态
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))

            // 异常处理
            .exceptionHandling(exceptions -> exceptions.authenticationEntryPoint(jwtAuthenticationEntryPoint)
                .accessDeniedHandler(jwtAccessDeniedHandler))

            // 请求授权配置
            .authorizeHttpRequests(authz -> authz
                // 公开接口
                .requestMatchers("/api/auth/login", "/api/auth/refresh", "/api/auth/validate", "/actuator/**",
                    "/swagger-ui/**", "/v3/api-docs/**", "/swagger-resources/**", "/webjars/**", "/favicon.ico",
                    "/error")
                .permitAll()

                // 健康检查接口
                .requestMatchers("/health", "/info", "/metrics").permitAll()

                // 其他所有请求需要认证
                .anyRequest().authenticated())

            // 添加JWT过滤器
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        log.info("Spring Security配置完成");
        return http.build();
    }
}
