package com.bdyl.erp.pisp.user.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import com.bdyl.erp.pisp.common.core.config.PispCoreAutoConfiguration;
import com.bdyl.erp.pisp.common.security.config.PispSecurityAutoConfiguration;
import com.bdyl.erp.pisp.common.web.config.PispWebAutoConfiguration;

/**
 * 用户管理模块配置类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Configuration
@Import({PispCoreAutoConfiguration.class, PispSecurityAutoConfiguration.class, PispWebAutoConfiguration.class})
public class UserModuleConfig {

}
