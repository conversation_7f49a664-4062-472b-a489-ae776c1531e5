package com.bdyl.erp.pisp.user.controller;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bdyl.erp.pisp.common.security.context.UserContextHolder;
import com.bdyl.erp.pisp.common.web.result.Result;
import com.bdyl.erp.pisp.user.dto.request.LoginRequest;
import com.bdyl.erp.pisp.user.dto.request.RefreshTokenRequest;
import com.bdyl.erp.pisp.user.dto.response.LoginResponse;
import com.bdyl.erp.pisp.user.service.AuthService;

/**
 * 认证控制器
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/auth")
@Validated
public class AuthController {

    /**
     * 认证服务
     */
    @Autowired
    private AuthService authService;

    /**
     * 用户登录
     *
     * @param request 登录请求
     * @param httpRequest HTTP请求
     * @return 登录响应
     */
    @PostMapping("/login")
    public Result<LoginResponse> login(@Valid @RequestBody LoginRequest request, HttpServletRequest httpRequest) {
        log.info("用户登录请求: username={}", request.getUsername());

        // 设置登录IP和用户代理
        request.setLoginIp(getClientIpAddress(httpRequest));
        request.setUserAgent(httpRequest.getHeader("User-Agent"));

        LoginResponse response = authService.login(request);
        return Result.success(response);
    }

    /**
     * 用户注销
     *
     * @return 操作结果
     */
    @PostMapping("/logout")
    @PreAuthorize("isAuthenticated()")
    public Result<Void> logout() {
        Long userId = UserContextHolder.getCurrentUserId();
        log.info("用户注销请求: userId={}", userId);

        authService.logout(userId);
        return Result.success();
    }

    /**
     * 刷新令牌
     *
     * @param request 刷新令牌请求
     * @return 登录响应
     */
    @PostMapping("/refresh")
    public Result<LoginResponse> refreshToken(@Valid @RequestBody RefreshTokenRequest request) {
        log.debug("刷新令牌请求");

        LoginResponse response = authService.refreshToken(request);
        return Result.success(response);
    }

    /**
     * 获取当前用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/me")
    @PreAuthorize("isAuthenticated()")
    public Result<LoginResponse.UserInfo> getCurrentUser() {
        LoginResponse.UserInfo userInfo = authService.getCurrentUserInfo();
        return Result.success(userInfo);
    }

    /**
     * 验证令牌
     *
     * @param httpRequest HTTP请求
     * @return 验证结果
     */
    @GetMapping("/validate")
    public Result<Boolean> validateToken(HttpServletRequest httpRequest) {
        String token = getTokenFromRequest(httpRequest);
        boolean isValid = authService.validateToken(token);
        return Result.success(isValid);
    }

    /**
     * 获取客户端IP地址
     *
     * @param request HTTP请求
     * @return IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.hasText(xForwardedFor) && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (StringUtils.hasText(xRealIp) && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        String xForwardedProto = request.getHeader("X-Forwarded-Proto");
        if (StringUtils.hasText(xForwardedProto)) {
            String xForwardedHost = request.getHeader("X-Forwarded-Host");
            if (StringUtils.hasText(xForwardedHost)) {
                return xForwardedHost;
            }
        }

        return request.getRemoteAddr();
    }

    /**
     * 从请求中获取令牌
     *
     * @param request HTTP请求
     * @return 令牌
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
}
