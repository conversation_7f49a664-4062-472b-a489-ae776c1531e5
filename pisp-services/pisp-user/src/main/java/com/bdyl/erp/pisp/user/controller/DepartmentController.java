package com.bdyl.erp.pisp.user.controller;

import java.util.List;

import jakarta.validation.Valid;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bdyl.erp.pisp.common.web.result.PageResult;
import com.bdyl.erp.pisp.common.web.result.Result;
import com.bdyl.erp.pisp.user.dto.request.DepartmentCreateRequest;
import com.bdyl.erp.pisp.user.dto.request.DepartmentQueryRequest;
import com.bdyl.erp.pisp.user.dto.request.DepartmentUpdateRequest;
import com.bdyl.erp.pisp.user.dto.response.DepartmentResponse;
import com.bdyl.erp.pisp.user.service.DepartmentService;

/**
 * 部门管理控制器
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/departments")
public class DepartmentController {

    /**
     * 部门服务
     */
    @Autowired
    private DepartmentService departmentService;

    /**
     * 创建部门
     *
     * @param request 部门创建请求
     * @return 部门响应
     */
    @PostMapping
    public Result<DepartmentResponse> createDepartment(@Valid @RequestBody DepartmentCreateRequest request) {
        log.info("创建部门请求: {}", request);
        DepartmentResponse response = departmentService.createDepartment(request);
        log.info("部门创建成功，部门ID: {}", response.getId());
        return Result.success(response);
    }

    /**
     * 更新部门
     *
     * @param id 部门ID
     * @param request 部门更新请求
     * @return 部门响应
     */
    @PutMapping("/{id}")
    public Result<DepartmentResponse> updateDepartment(@PathVariable Long id,
        @Valid @RequestBody DepartmentUpdateRequest request) {
        log.info("更新部门请求，部门ID: {}, 请求参数: {}", id, request);
        request.setId(id);
        DepartmentResponse response = departmentService.updateDepartment(request);
        log.info("部门更新成功，部门ID: {}", id);
        return Result.success(response);
    }

    /**
     * 删除部门
     *
     * @param id 部门ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteDepartment(@PathVariable Long id) {
        log.info("删除部门请求，部门ID: {}", id);
        departmentService.deleteDepartment(id);
        log.info("部门删除成功，部门ID: {}", id);
        return Result.success();
    }

    /**
     * 批量删除部门
     *
     * @param ids 部门ID列表
     * @return 删除结果
     */
    @DeleteMapping("/batch")
    public Result<Void> batchDeleteDepartments(@RequestParam List<Long> ids) {
        log.info("批量删除部门请求，部门IDs: {}", ids);
        departmentService.batchDeleteDepartments(ids);
        log.info("批量删除部门成功，数量: {}", ids.size());
        return Result.success();
    }

    /**
     * 根据ID查询部门详情
     *
     * @param id 部门ID
     * @return 部门响应
     */
    @GetMapping("/{id}")
    public Result<DepartmentResponse> getDepartmentById(@PathVariable Long id) {
        log.info("查询部门详情，部门ID: {}", id);
        DepartmentResponse response = departmentService.getDepartmentById(id);
        return Result.success(response);
    }

    /**
     * 分页查询部门列表
     *
     * @param request 查询请求
     * @return 分页部门响应
     */
    @GetMapping
    public Result<PageResult<DepartmentResponse>> getDepartmentPage(@Valid DepartmentQueryRequest request) {
        log.info("分页查询部门列表: {}", request);
        var pageResult = departmentService.getDepartmentPage(request);
        return Result.success(PageResult.of(pageResult));
    }

    /**
     * 查询部门树结构
     *
     * @return 部门树列表
     */
    @GetMapping("/tree")
    public Result<List<DepartmentResponse>> getDepartmentTree() {
        log.info("查询部门树结构");
        List<DepartmentResponse> tree = departmentService.getDepartmentTree();
        return Result.success(tree);
    }

    /**
     * 查询部门树结构（包含员工数量）
     *
     * @return 部门树列表
     */
    @GetMapping("/tree/with-employee-count")
    public Result<List<DepartmentResponse>> getDepartmentTreeWithEmployeeCount() {
        log.info("查询部门树结构（包含员工数量）");
        List<DepartmentResponse> tree = departmentService.getDepartmentTreeWithEmployeeCount();
        return Result.success(tree);
    }

    /**
     * 根据父部门ID查询子部门列表
     *
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    @GetMapping("/children/{parentId}")
    public Result<List<DepartmentResponse>> getChildDepartments(@PathVariable Long parentId) {
        log.info("查询子部门列表，父部门ID: {}", parentId);
        List<DepartmentResponse> children = departmentService.getChildDepartments(parentId);
        return Result.success(children);
    }

    /**
     * 查询所有启用的部门
     *
     * @return 部门列表
     */
    @GetMapping("/active")
    public Result<List<DepartmentResponse>> getActiveDepartments() {
        log.info("查询所有启用的部门");
        List<DepartmentResponse> departments = departmentService.getActiveDepartments();
        return Result.success(departments);
    }

    /**
     * 启用部门
     *
     * @param id 部门ID
     * @return 操作结果
     */
    @PutMapping("/{id}/activate")
    public Result<Void> activateDepartment(@PathVariable Long id) {
        log.info("启用部门，部门ID: {}", id);
        departmentService.activateDepartment(id);
        log.info("部门启用成功，部门ID: {}", id);
        return Result.success();
    }

    /**
     * 禁用部门
     *
     * @param id 部门ID
     * @return 操作结果
     */
    @PutMapping("/{id}/deactivate")
    public Result<Void> deactivateDepartment(@PathVariable Long id) {
        log.info("禁用部门，部门ID: {}", id);
        departmentService.deactivateDepartment(id);
        log.info("部门禁用成功，部门ID: {}", id);
        return Result.success();
    }

    /**
     * 批量更新部门状态
     *
     * @param ids 部门ID列表
     * @param status 新状态
     * @return 操作结果
     */
    @PutMapping("/batch/status")
    public Result<Void> batchUpdateDepartmentStatus(@RequestParam List<Long> ids, @RequestParam String status) {
        log.info("批量更新部门状态，部门IDs: {}, 状态: {}", ids, status);
        departmentService.batchUpdateDepartmentStatus(ids, status);
        log.info("批量更新部门状态成功，数量: {}", ids.size());
        return Result.success();
    }

    /**
     * 设置部门负责人
     *
     * @param id 部门ID
     * @param leaderId 负责人ID
     * @return 操作结果
     */
    @PutMapping("/{id}/leader")
    public Result<Void> setDepartmentLeader(@PathVariable Long id, @RequestParam Long leaderId) {
        log.info("设置部门负责人，部门ID: {}, 负责人ID: {}", id, leaderId);
        departmentService.setDepartmentLeader(id, leaderId);
        log.info("部门负责人设置成功，部门ID: {}, 负责人ID: {}", id, leaderId);
        return Result.success();
    }

    /**
     * 移动部门到新的父部门
     *
     * @param id 部门ID
     * @param newParentId 新父部门ID
     * @return 操作结果
     */
    @PutMapping("/{id}/move")
    public Result<Void> moveDepartment(@PathVariable Long id, @RequestParam(required = false) Long newParentId) {
        log.info("移动部门，部门ID: {}, 新父部门ID: {}", id, newParentId);
        departmentService.moveDepartment(id, newParentId);
        log.info("部门移动成功，部门ID: {}, 新父部门ID: {}", id, newParentId);
        return Result.success();
    }

    /**
     * 获取部门路径
     *
     * @param id 部门ID
     * @return 部门路径
     */
    @GetMapping("/{id}/path")
    public Result<String> getDepartmentPath(@PathVariable Long id) {
        log.info("获取部门路径，部门ID: {}", id);
        String path = departmentService.getDepartmentPath(id);
        return Result.success(path);
    }

    /**
     * 获取部门员工数量
     *
     * @param id 部门ID
     * @return 员工数量
     */
    @GetMapping("/{id}/employee-count")
    public Result<Integer> getDepartmentEmployeeCount(@PathVariable Long id) {
        log.info("获取部门员工数量，部门ID: {}", id);
        int count = departmentService.getDepartmentEmployeeCount(id);
        return Result.success(count);
    }

    /**
     * 根据部门ID查询所有子部门ID（递归）
     *
     * @param id 部门ID
     * @return 子部门ID列表
     */
    @GetMapping("/{id}/child-ids")
    public Result<List<Long>> getChildDepartmentIds(@PathVariable Long id) {
        log.info("查询子部门ID列表，部门ID: {}", id);
        List<Long> childIds = departmentService.getChildDepartmentIds(id);
        return Result.success(childIds);
    }
}
