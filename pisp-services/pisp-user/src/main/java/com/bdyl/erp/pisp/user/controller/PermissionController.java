package com.bdyl.erp.pisp.user.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bdyl.erp.pisp.common.web.result.PageResult;
import com.bdyl.erp.pisp.common.web.result.Result;
import com.bdyl.erp.pisp.user.dto.request.PermissionCreateRequest;
import com.bdyl.erp.pisp.user.dto.request.PermissionQueryRequest;
import com.bdyl.erp.pisp.user.dto.request.PermissionUpdateRequest;
import com.bdyl.erp.pisp.user.dto.response.PermissionResponse;
import com.bdyl.erp.pisp.user.service.PermissionService;

/**
 * 权限管理控制器
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/permissions")
@Validated
@RequiredArgsConstructor
public class PermissionController {

    /**
     * 权限服务
     */
    private final PermissionService permissionService;

    /**
     * 创建权限
     *
     * @param request 权限创建请求
     * @return 权限响应
     */
    @PostMapping
    public Result<PermissionResponse> createPermission(@Validated @RequestBody PermissionCreateRequest request) {
        log.info("创建权限: permissionName={}, permissionCode={}", request.getPermissionName(),
            request.getPermissionCode());
        PermissionResponse response = permissionService.createPermission(request);
        return Result.success(response);
    }

    /**
     * 更新权限
     *
     * @param id 权限ID
     * @param request 权限更新请求
     * @return 权限响应
     */
    @PutMapping("/{id}")
    public Result<PermissionResponse> updatePermission(@PathVariable Long id,
        @Validated @RequestBody PermissionUpdateRequest request) {
        log.info("更新权限: permissionId={}, permissionName={}", id, request.getPermissionName());
        request.setId(id);
        PermissionResponse response = permissionService.updatePermission(request);
        return Result.success(response);
    }

    /**
     * 删除权限
     *
     * @param id 权限ID
     * @return 成功信息
     */
    @DeleteMapping("/{id}")
    public Result<Void> deletePermission(@PathVariable Long id) {
        log.info("删除权限: permissionId={}", id);
        permissionService.deletePermission(id);
        return Result.success();
    }

    /**
     * 批量删除权限
     *
     * @param ids 权限ID列表
     * @return 成功信息
     */
    @DeleteMapping("/batch")
    public Result<Void> batchDeletePermissions(@RequestParam List<Long> ids) {
        log.info("批量删除权限: permissionIds={}", ids);
        permissionService.batchDeletePermissions(ids);
        return Result.success();
    }

    /**
     * 根据ID查询权限详情
     *
     * @param id 权限ID
     * @return 权限响应
     */
    @GetMapping("/{id}")
    public Result<PermissionResponse> getPermissionById(@PathVariable Long id) {
        log.info("查询权限详情: permissionId={}", id);
        PermissionResponse response = permissionService.getPermissionById(id);
        return Result.success(response);
    }

    /**
     * 根据权限代码查询权限
     *
     * @param code 权限代码
     * @return 权限响应
     */
    @GetMapping("/code/{code}")
    public Result<PermissionResponse> getPermissionByCode(@PathVariable String code) {
        log.info("根据代码查询权限: permissionCode={}", code);
        PermissionResponse response = permissionService.getPermissionByCode(code);
        return Result.success(response);
    }

    /**
     * 分页查询权限列表
     *
     * @param request 权限查询请求
     * @return 权限列表
     */
    @GetMapping
    public Result<PageResult<PermissionResponse>> getPermissionPage(PermissionQueryRequest request) {
        log.info("分页查询权限: request={}", request);
        PageResult<PermissionResponse> pageResult = permissionService.getPermissionPage(request);
        return Result.success(pageResult);
    }

    /**
     * 查询权限树结构
     *
     * @return 权限树列表
     */
    @GetMapping("/tree")
    public Result<List<PermissionResponse>> getPermissionTree() {
        log.info("查询权限树结构");
        List<PermissionResponse> tree = permissionService.getPermissionTree();
        return Result.success(tree);
    }

    /**
     * 根据父权限ID查询子权限列表
     *
     * @param parentId 父权限ID
     * @return 子权限列表
     */
    @GetMapping("/children/{parentId}")
    public Result<List<PermissionResponse>> getChildPermissions(@PathVariable Long parentId) {
        log.info("查询子权限: parentId={}", parentId);
        List<PermissionResponse> children = permissionService.getChildPermissions(parentId);
        return Result.success(children);
    }

    /**
     * 根据角色ID查询权限列表
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    @GetMapping("/role/{roleId}")
    public Result<List<PermissionResponse>> getPermissionsByRoleId(@PathVariable Long roleId) {
        log.info("根据角色ID查询权限: roleId={}", roleId);
        List<PermissionResponse> permissions = permissionService.getPermissionsByRoleId(roleId);
        return Result.success(permissions);
    }

    /**
     * 根据用户ID查询权限列表
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    @GetMapping("/user/{userId}")
    public Result<List<PermissionResponse>> getPermissionsByUserId(@PathVariable Long userId) {
        log.info("根据用户ID查询权限: userId={}", userId);
        List<PermissionResponse> permissions = permissionService.getPermissionsByUserId(userId);
        return Result.success(permissions);
    }

    /**
     * 检查权限代码是否存在
     *
     * @param permissionCode 权限代码
     * @param excludeId 排除的权限ID
     * @return 是否存在
     */
    @GetMapping("/check-code")
    public Result<Boolean> checkPermissionCodeExists(@RequestParam String permissionCode,
        @RequestParam(required = false) Long excludeId) {
        log.info("检查权限代码: permissionCode={}, excludeId={}", permissionCode, excludeId);
        boolean exists = permissionService.isPermissionCodeExists(permissionCode, excludeId);
        return Result.success(exists);
    }

    /**
     * 检查权限名称是否存在
     *
     * @param permissionName 权限名称
     * @param parentId 父权限ID
     * @param excludeId 排除的权限ID
     * @return 是否存在
     */
    @GetMapping("/check-name")
    public Result<Boolean> checkPermissionNameExists(@RequestParam String permissionName,
        @RequestParam(required = false) Long parentId, @RequestParam(required = false) Long excludeId) {
        log.info("检查权限名称: permissionName={}, parentId={}, excludeId={}", permissionName, parentId, excludeId);
        boolean exists = permissionService.isPermissionNameExists(permissionName, parentId, excludeId);
        return Result.success(exists);
    }

    /**
     * 查询菜单权限
     *
     * @return 菜单权限列表
     */
    @GetMapping("/menus")
    public Result<List<PermissionResponse>> getMenuPermissions() {
        log.info("查询菜单权限");
        PermissionQueryRequest request = new PermissionQueryRequest();
        request.setPermissionType("MENU");
        PageResult<PermissionResponse> pageResult = permissionService.getPermissionPage(request);
        return Result.success(pageResult.getRecords());
    }
}
