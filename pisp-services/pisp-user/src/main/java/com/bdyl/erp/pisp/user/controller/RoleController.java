package com.bdyl.erp.pisp.user.controller;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bdyl.erp.pisp.common.web.result.Result;
import com.bdyl.erp.pisp.user.dto.request.RoleCreateRequest;
import com.bdyl.erp.pisp.user.dto.request.RoleQueryRequest;
import com.bdyl.erp.pisp.user.dto.request.RoleUpdateRequest;
import com.bdyl.erp.pisp.user.dto.response.RoleResponse;
import com.bdyl.erp.pisp.user.service.RoleService;

/**
 * 角色管理控制器
 *
 * 提供角色的CRUD操作，包括： - 角色创建、更新、删除 - 角色查询、分页查询 - 角色状态管理 - 角色权限分配
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/roles")
public class RoleController {

    /**
     * 角色服务
     */
    @Autowired
    private RoleService roleService;

    /**
     * 创建角色
     *
     * @param request 角色创建请求
     * @return 创建的角色信息
     */
    @PostMapping
    public Result<RoleResponse> createRole(@Validated @RequestBody RoleCreateRequest request) {
        log.info("创建角色请求: {}", request.getRoleName());
        RoleResponse response = roleService.createRole(request);
        return Result.success(response);
    }

    /**
     * 更新角色
     *
     * @param roleId 角色ID
     * @param request 角色更新请求
     * @return 更新后的角色信息
     */
    @PutMapping("/{roleId}")
    public Result<RoleResponse> updateRole(@PathVariable Long roleId,
        @Validated @RequestBody RoleUpdateRequest request) {
        log.info("更新角色请求: roleId={}", roleId);
        request.setId(roleId);
        RoleResponse response = roleService.updateRole(request);
        return Result.success(response);
    }

    /**
     * 根据ID获取角色
     *
     * @param roleId 角色ID
     * @return 角色信息
     */
    @GetMapping("/{roleId}")
    public Result<RoleResponse> getRoleById(@PathVariable Long roleId) {
        log.info("获取角色请求: roleId={}", roleId);
        RoleResponse response = roleService.getRoleById(roleId);
        return Result.success(response);
    }

    /**
     * 根据角色代码获取角色
     *
     * @param roleCode 角色代码
     * @return 角色信息
     */
    @GetMapping("/code/{roleCode}")
    public Result<RoleResponse> getRoleByCode(@PathVariable String roleCode) {
        log.info("根据代码获取角色请求: roleCode={}", roleCode);
        RoleResponse response = roleService.getRoleByCode(roleCode);
        return Result.success(response);
    }

    /**
     * 删除角色
     *
     * @param roleId 角色ID
     * @return 操作结果
     */
    @DeleteMapping("/{roleId}")
    public Result<Void> deleteRole(@PathVariable Long roleId) {
        log.info("删除角色请求: roleId={}", roleId);
        roleService.deleteRole(roleId);
        return Result.success();
    }

    /**
     * 批量删除角色
     *
     * @param roleIds 角色ID列表
     * @return 操作结果
     */
    @DeleteMapping("/batch")
    public Result<Void> batchDeleteRoles(@RequestBody List<Long> roleIds) {
        log.info("批量删除角色请求: roleIds={}", roleIds);
        roleService.batchDeleteRoles(roleIds);
        return Result.success();
    }

    /**
     * 分页查询角色列表
     *
     * @param request 查询条件
     * @return 分页角色列表
     */
    @GetMapping
    public Result<IPage<RoleResponse>> getRolePage(RoleQueryRequest request) {
        log.info("分页查询角色请求: pageNum={}, pageSize={}", request.getPageNum(), request.getPageSize());
        IPage<RoleResponse> page = roleService.getRolePage(request);
        return Result.success(page);
    }

    /**
     * 获取所有启用的角色
     *
     * @return 角色列表
     */
    @GetMapping("/active")
    public Result<List<RoleResponse>> getActiveRoles() {
        log.info("获取所有启用角色请求");
        List<RoleResponse> roles = roleService.getActiveRoles();
        return Result.success(roles);
    }

    /**
     * 启用角色
     *
     * @param roleId 角色ID
     * @return 操作结果
     */
    @PutMapping("/{roleId}/activate")
    public Result<Void> activateRole(@PathVariable Long roleId) {
        log.info("启用角色请求: roleId={}", roleId);
        roleService.activateRole(roleId);
        return Result.success();
    }

    /**
     * 禁用角色
     *
     * @param roleId 角色ID
     * @return 操作结果
     */
    @PutMapping("/{roleId}/deactivate")
    public Result<Void> deactivateRole(@PathVariable Long roleId) {
        log.info("禁用角色请求: roleId={}", roleId);
        roleService.deactivateRole(roleId);
        return Result.success();
    }

    /**
     * 批量更新角色状态
     *
     * @param roleIds 角色ID列表
     * @param status 新状态
     * @return 操作结果
     */
    @PutMapping("/status/batch")
    public Result<Void> batchUpdateRoleStatus(@RequestBody List<Long> roleIds, @RequestParam String status) {
        log.info("批量更新角色状态请求: roleIds={}, status={}", roleIds, status);
        roleService.batchUpdateRoleStatus(roleIds, status);
        return Result.success();
    }

    /**
     * 为角色分配权限
     *
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @return 操作结果
     */
    @PutMapping("/{roleId}/permissions")
    public Result<Void> assignPermissionsToRole(@PathVariable Long roleId, @RequestBody List<Long> permissionIds) {
        log.info("为角色分配权限请求: roleId={}, permissionIds={}", roleId, permissionIds);
        roleService.assignPermissionsToRole(roleId, permissionIds);
        return Result.success();
    }

    /**
     * 移除角色权限
     *
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @return 操作结果
     */
    @DeleteMapping("/{roleId}/permissions")
    public Result<Void> removePermissionsFromRole(@PathVariable Long roleId, @RequestBody List<Long> permissionIds) {
        log.info("移除角色权限请求: roleId={}, permissionIds={}", roleId, permissionIds);
        roleService.removePermissionsFromRole(roleId, permissionIds);
        return Result.success();
    }

    /**
     * 根据用户ID获取角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    @GetMapping("/user/{userId}")
    public Result<List<RoleResponse>> getRolesByUserId(@PathVariable Long userId) {
        log.info("根据用户ID获取角色请求: userId={}", userId);
        List<RoleResponse> roles = roleService.getRolesByUserId(userId);
        return Result.success(roles);
    }

    /**
     * 检查角色名称是否存在
     *
     * @param roleName 角色名称
     * @param excludeId 排除的角色ID
     * @return 是否存在
     */
    @GetMapping("/check/name")
    public Result<Boolean> checkRoleNameExists(@RequestParam String roleName,
        @RequestParam(required = false) Long excludeId) {
        boolean exists = roleService.isRoleNameExists(roleName, excludeId);
        return Result.success(exists);
    }

    /**
     * 检查角色代码是否存在
     *
     * @param roleCode 角色代码
     * @param excludeId 排除的角色ID
     * @return 是否存在
     */
    @GetMapping("/check/code")
    public Result<Boolean> checkRoleCodeExists(@RequestParam String roleCode,
        @RequestParam(required = false) Long excludeId) {
        boolean exists = roleService.isRoleCodeExists(roleCode, excludeId);
        return Result.success(exists);
    }

    /**
     * 获取角色的用户数量
     *
     * @param roleId 角色ID
     * @return 用户数量
     */
    @GetMapping("/{roleId}/user-count")
    public Result<Integer> getRoleUserCount(@PathVariable Long roleId) {
        int count = roleService.getRoleUserCount(roleId);
        return Result.success(count);
    }
}
