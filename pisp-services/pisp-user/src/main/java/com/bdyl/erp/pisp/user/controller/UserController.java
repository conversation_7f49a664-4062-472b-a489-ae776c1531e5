package com.bdyl.erp.pisp.user.controller;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bdyl.erp.pisp.common.web.result.Result;
import com.bdyl.erp.pisp.user.dto.request.PasswordChangeRequest;
import com.bdyl.erp.pisp.user.dto.request.UserCreateRequest;
import com.bdyl.erp.pisp.user.dto.request.UserQueryRequest;
import com.bdyl.erp.pisp.user.dto.request.UserUpdateRequest;
import com.bdyl.erp.pisp.user.dto.response.UserResponse;
import com.bdyl.erp.pisp.user.service.UserService;

/**
 * 用户管理控制器
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/users")
@Validated
public class UserController {

    /**
     * 用户服务
     */
    @Autowired
    private UserService userService;

    /**
     * 创建用户
     *
     * @param request 用户创建请求
     * @return 用户响应
     */
    @PostMapping
    public Result<UserResponse> createUser(@Validated @RequestBody UserCreateRequest request) {
        log.info("创建用户请求: {}", request.getUsername());
        UserResponse response = userService.createUser(request);
        return Result.success(response);
    }

    /**
     * 更新用户
     *
     * @param userId 用户ID
     * @param request 用户更新请求
     * @return 用户响应
     */
    @PutMapping("/{userId}")
    public Result<UserResponse> updateUser(@PathVariable Long userId,
        @Validated @RequestBody UserUpdateRequest request) {
        log.info("更新用户请求: userId={}", userId);
        request.setId(userId);
        UserResponse response = userService.updateUser(request);
        return Result.success(response);
    }

    /**
     * 删除用户
     *
     * @param userId 用户ID
     * @return 操作结果
     */
    @DeleteMapping("/{userId}")
    public Result<Void> deleteUser(@PathVariable Long userId) {
        log.info("删除用户请求: userId={}", userId);
        userService.deleteUser(userId);
        return Result.success();
    }

    /**
     * 批量删除用户
     *
     * @param userIds 用户ID列表
     * @return 操作结果
     */
    @DeleteMapping("/batch")
    public Result<Void> batchDeleteUsers(@RequestBody List<Long> userIds) {
        log.info("批量删除用户请求: userIds={}", userIds);
        userService.batchDeleteUsers(userIds);
        return Result.success();
    }

    /**
     * 根据ID查询用户详情
     *
     * @param userId 用户ID
     * @return 用户响应
     */
    @GetMapping("/{userId}")
    public Result<UserResponse> getUserById(@PathVariable Long userId) {
        UserResponse response = userService.getUserById(userId);
        return Result.success(response);
    }

    /**
     * 分页查询用户列表
     *
     * @param request 查询请求
     * @return 分页用户响应
     */
    @GetMapping
    public Result<IPage<UserResponse>> getUserPage(UserQueryRequest request) {
        IPage<UserResponse> page = userService.getUserPage(request);
        return Result.success(page);
    }

    /**
     * 根据部门ID查询用户列表
     *
     * @param departmentId 部门ID
     * @return 用户响应列表
     */
    @GetMapping("/department/{departmentId}")
    public Result<List<UserResponse>> getUsersByDepartmentId(@PathVariable Long departmentId) {
        List<UserResponse> users = userService.getUsersByDepartmentId(departmentId);
        return Result.success(users);
    }

    /**
     * 修改密码
     *
     * @param request 密码修改请求
     * @return 操作结果
     */
    @PutMapping("/password/change")
    public Result<Void> changePassword(@Validated @RequestBody PasswordChangeRequest request) {
        log.info("修改密码请求: userId={}", request.getUserId());
        userService.changePassword(request);
        return Result.success();
    }

    /**
     * 重置密码
     *
     * @param userId 用户ID
     * @param newPassword 新密码
     * @return 操作结果
     */
    @PutMapping("/{userId}/password/reset")
    public Result<Void> resetPassword(@PathVariable Long userId, @RequestParam String newPassword) {
        log.info("重置密码请求: userId={}", userId);
        userService.resetPassword(userId, newPassword);
        return Result.success();
    }

    /**
     * 激活用户
     *
     * @param userId 用户ID
     * @return 操作结果
     */
    @PutMapping("/{userId}/activate")
    public Result<Void> activateUser(@PathVariable Long userId) {
        log.info("激活用户请求: userId={}", userId);
        userService.activateUser(userId);
        return Result.success();
    }

    /**
     * 停用用户
     *
     * @param userId 用户ID
     * @return 操作结果
     */
    @PutMapping("/{userId}/deactivate")
    public Result<Void> deactivateUser(@PathVariable Long userId) {
        log.info("停用用户请求: userId={}", userId);
        userService.deactivateUser(userId);
        return Result.success();
    }

    /**
     * 锁定用户
     *
     * @param userId 用户ID
     * @return 操作结果
     */
    @PutMapping("/{userId}/lock")
    public Result<Void> lockUser(@PathVariable Long userId) {
        log.info("锁定用户请求: userId={}", userId);
        userService.lockUser(userId);
        return Result.success();
    }

    /**
     * 批量更新用户状态
     *
     * @param userIds 用户ID列表
     * @param status 新状态
     * @return 操作结果
     */
    @PutMapping("/status/batch")
    public Result<Void> batchUpdateUserStatus(@RequestBody List<Long> userIds, @RequestParam String status) {
        log.info("批量更新用户状态请求: userIds={}, status={}", userIds, status);
        userService.batchUpdateUserStatus(userIds, status);
        return Result.success();
    }

    /**
     * 分配角色给用户
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 操作结果
     */
    @PutMapping("/{userId}/roles")
    public Result<Void> assignRolesToUser(@PathVariable Long userId, @RequestBody List<Long> roleIds) {
        log.info("分配角色给用户请求: userId={}, roleIds={}", userId, roleIds);
        userService.assignRolesToUser(userId, roleIds);
        return Result.success();
    }

    /**
     * 移除用户角色
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 操作结果
     */
    @DeleteMapping("/{userId}/roles")
    public Result<Void> removeRolesFromUser(@PathVariable Long userId, @RequestBody List<Long> roleIds) {
        log.info("移除用户角色请求: userId={}, roleIds={}", userId, roleIds);
        userService.removeRolesFromUser(userId, roleIds);
        return Result.success();
    }

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    @GetMapping("/check/username")
    public Result<Boolean> checkUsernameExists(@RequestParam String username,
        @RequestParam(required = false) Long excludeId) {
        boolean exists = userService.isUsernameExists(username, excludeId);
        return Result.success(exists);
    }

    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    @GetMapping("/check/email")
    public Result<Boolean> checkEmailExists(@RequestParam String email,
        @RequestParam(required = false) Long excludeId) {
        boolean exists = userService.isEmailExists(email, excludeId);
        return Result.success(exists);
    }

    /**
     * 检查手机号是否存在
     *
     * @param phone 手机号
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    @GetMapping("/check/phone")
    public Result<Boolean> checkPhoneExists(@RequestParam String phone,
        @RequestParam(required = false) Long excludeId) {
        boolean exists = userService.isPhoneExists(phone, excludeId);
        return Result.success(exists);
    }
}
