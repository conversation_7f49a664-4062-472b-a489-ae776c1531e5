package com.bdyl.erp.pisp.user.dto.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

import com.bdyl.erp.pisp.common.web.dto.PageRequest;
import com.bdyl.erp.pisp.user.enums.DepartmentStatus;

/**
 * 部门查询请求DTO
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DepartmentQueryRequest extends PageRequest {

    /**
     * 部门名称（模糊查询）
     */
    private String deptName;

    /**
     * 部门代码（模糊查询）
     */
    private String deptCode;

    /**
     * 父部门ID
     */
    private Long parentId;

    /**
     * 部门状态
     */
    private DepartmentStatus status;

    /**
     * 负责人ID
     */
    private Long leaderId;

    /**
     * 负责人姓名（模糊查询）
     */
    private String leaderName;

    /**
     * 是否查询根部门
     */
    private Boolean isRoot;

    /**
     * 部门层级
     */
    private Integer deptLevel;

    /**
     * 创建时间开始
     */
    private String createTimeStart;

    /**
     * 创建时间结束
     */
    private String createTimeEnd;
}
