package com.bdyl.erp.pisp.user.dto.request;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

import lombok.Data;

import com.bdyl.erp.pisp.user.enums.DepartmentStatus;

/**
 * 部门更新请求DTO
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
public class DepartmentUpdateRequest {

    /**
     * 部门ID
     */
    @NotNull(message = "部门ID不能为空")
    private Long id;

    /**
     * 部门名称
     */
    @Size(min = 2, max = 100, message = "部门名称长度必须在2-100个字符之间")
    private String deptName;

    /**
     * 父部门ID（根部门为null）
     */
    private Long parentId;

    /**
     * 部门描述
     */
    @Size(max = 500, message = "部门描述长度不能超过500个字符")
    private String description;

    /**
     * 部门负责人ID
     */
    private Long leaderId;

    /**
     * 联系电话
     */
    @Pattern(regexp = "^[0-9-+()\\s]*$", message = "联系电话格式不正确")
    @Size(max = 20, message = "联系电话长度不能超过20个字符")
    private String phone;

    /**
     * 邮箱
     */
    @Pattern(regexp = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    private String email;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

    /**
     * 部门状态
     */
    private DepartmentStatus status;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    /**
     * 版本号（乐观锁）
     */
    @NotNull(message = "版本号不能为空")
    private Integer version;
}
