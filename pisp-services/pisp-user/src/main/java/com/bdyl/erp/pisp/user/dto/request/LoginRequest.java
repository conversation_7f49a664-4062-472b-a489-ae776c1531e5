package com.bdyl.erp.pisp.user.dto.request;

import jakarta.validation.constraints.NotBlank;

import lombok.Data;

/**
 * 登录请求DTO
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
public class LoginRequest {

    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    private String username;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 验证码
     */
    private String captcha;

    /**
     * 验证码键
     */
    private String captchaKey;

    /**
     * 记住我
     */
    private Boolean rememberMe = false;

    /**
     * 登录IP
     */
    private String loginIp;

    /**
     * 用户代理
     */
    private String userAgent;
}
