package com.bdyl.erp.pisp.user.dto.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

import com.bdyl.erp.pisp.common.web.dto.PageRequest;

/**
 * 权限查询请求DTO
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PermissionQueryRequest extends PageRequest {

    /**
     * 权限名称（模糊查询）
     */
    private String permissionName;

    /**
     * 权限代码（模糊查询）
     */
    private String permissionCode;

    /**
     * 权限类型
     */
    private String permissionType;

    /**
     * 父权限ID
     */
    private Long parentId;

    /**
     * 是否系统权限
     */
    private Boolean isSystem;
}
