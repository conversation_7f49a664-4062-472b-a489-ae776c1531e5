package com.bdyl.erp.pisp.user.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.PositiveOrZero;
import jakarta.validation.constraints.Size;

import lombok.Data;

/**
 * 权限更新请求DTO
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
public class PermissionUpdateRequest {

    /**
     * 权限ID
     */
    @NotNull(message = "权限ID不能为空")
    @Positive(message = "权限ID必须为正整数")
    private Long id;

    /**
     * 权限名称
     */
    @NotBlank(message = "权限名称不能为空")
    @Size(max = 50, message = "权限名称长度不能超过50个字符")
    private String permissionName;

    /**
     * 权限代码
     */
    @NotBlank(message = "权限代码不能为空")
    @Size(max = 100, message = "权限代码长度不能超过100个字符")
    @Pattern(regexp = "^[a-z]+:[a-z]+$", message = "权限代码格式应为：模块:操作，如user:read")
    private String permissionCode;

    /**
     * 权限描述
     */
    @Size(max = 200, message = "权限描述长度不能超过200个字符")
    private String description;

    /**
     * 权限类型（MENU-菜单，BUTTON-按钮，API-接口）
     */
    @NotBlank(message = "权限类型不能为空")
    @Pattern(regexp = "^(MENU|BUTTON|API)$", message = "权限类型必须是MENU、BUTTON或API")
    private String permissionType;

    /**
     * 父权限ID
     */
    private Long parentId;

    /**
     * 权限路径
     */
    @Size(max = 200, message = "权限路径长度不能超过200个字符")
    private String permissionPath;

    /**
     * 排序顺序
     */
    @NotNull(message = "排序顺序不能为空")
    @PositiveOrZero(message = "排序顺序必须为非负整数")
    private Integer sortOrder;

    /**
     * 是否系统权限
     */
    private Boolean isSystem;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    /**
     * 版本号（乐观锁）
     */
    @NotNull(message = "版本号不能为空")
    @PositiveOrZero(message = "版本号必须为非负整数")
    private Integer version;
}
