package com.bdyl.erp.pisp.user.dto.request;

import java.util.List;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

import lombok.Data;

import com.bdyl.erp.pisp.user.enums.RoleStatus;

/**
 * 角色创建请求DTO
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
public class RoleCreateRequest {

    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空")
    @Size(max = 50, message = "角色名称长度不能超过50个字符")
    private String roleName;

    /**
     * 角色代码
     */
    @NotBlank(message = "角色代码不能为空")
    @Size(max = 50, message = "角色代码长度不能超过50个字符")
    @Pattern(regexp = "^[A-Z0-9_]+$", message = "角色代码只能包含大写字母、数字和下划线")
    private String roleCode;

    /**
     * 角色描述
     */
    @Size(max = 200, message = "角色描述长度不能超过200个字符")
    private String description;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

    /**
     * 角色状态
     */
    @NotNull(message = "角色状态不能为空")
    private RoleStatus status;

    /**
     * 权限ID列表
     */
    private List<Long> permissionIds;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}
