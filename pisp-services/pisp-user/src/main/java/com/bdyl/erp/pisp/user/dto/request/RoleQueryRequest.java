package com.bdyl.erp.pisp.user.dto.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

import com.bdyl.erp.pisp.common.web.dto.PageRequest;

/**
 * 角色查询请求DTO
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoleQueryRequest extends PageRequest {

    /**
     * 角色名称（模糊查询）
     */
    private String roleName;

    /**
     * 角色代码（模糊查询）
     */
    private String roleCode;

    /**
     * 角色状态
     */
    private String status;

    /**
     * 是否系统角色
     */
    private Boolean isSystem;
}
