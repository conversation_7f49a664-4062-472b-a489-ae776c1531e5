package com.bdyl.erp.pisp.user.dto.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

import com.bdyl.erp.pisp.common.web.dto.PageRequest;
import com.bdyl.erp.pisp.user.enums.UserStatus;

/**
 * 用户查询请求DTO
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserQueryRequest extends PageRequest {

    /**
     * 用户名（模糊查询）
     */
    private String username;

    /**
     * 真实姓名（模糊查询）
     */
    private String realName;

    /**
     * 邮箱（模糊查询）
     */
    private String email;

    /**
     * 手机号（模糊查询）
     */
    private String phone;

    /**
     * 部门ID
     */
    private Long departmentId;

    /**
     * 用户状态
     */
    private UserStatus status;

    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * 创建时间开始
     */
    private String createTimeStart;

    /**
     * 创建时间结束
     */
    private String createTimeEnd;
}
