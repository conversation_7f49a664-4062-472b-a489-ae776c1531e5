package com.bdyl.erp.pisp.user.dto.response;

import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;

import com.bdyl.erp.pisp.user.enums.DepartmentStatus;

/**
 * 部门响应DTO
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
public class DepartmentResponse {

    /**
     * 部门ID
     */
    private Long id;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 部门代码
     */
    private String deptCode;

    /**
     * 父部门ID
     */
    private Long parentId;

    /**
     * 部门层级
     */
    private Integer deptLevel;

    /**
     * 部门路径
     */
    private String deptPath;

    /**
     * 部门描述
     */
    private String description;

    /**
     * 部门负责人ID
     */
    private Long leaderId;

    /**
     * 部门负责人姓名
     */
    private String leaderName;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

    /**
     * 部门状态
     */
    private DepartmentStatus status;

    /**
     * 子部门列表
     */
    private List<DepartmentResponse> children;

    /**
     * 员工数量
     */
    private Integer employeeCount;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 版本号
     */
    private Integer version;
}
