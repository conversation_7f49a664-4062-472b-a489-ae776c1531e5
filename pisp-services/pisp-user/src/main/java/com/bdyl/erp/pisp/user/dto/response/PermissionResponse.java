package com.bdyl.erp.pisp.user.dto.response;

import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;

/**
 * 权限响应DTO
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
public class PermissionResponse {

    /**
     * 权限ID
     */
    private Long id;

    /**
     * 权限名称
     */
    private String permissionName;

    /**
     * 权限代码
     */
    private String permissionCode;

    /**
     * 权限描述
     */
    private String description;

    /**
     * 权限类型（MENU-菜单，BUTTON-按钮，API-接口）
     */
    private String permissionType;

    /**
     * 父权限ID
     */
    private Long parentId;

    /**
     * 权限路径
     */
    private String permissionPath;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

    /**
     * 是否系统权限
     */
    private Boolean isSystem;

    /**
     * 子权限列表
     */
    private List<PermissionResponse> children;

    /**
     * 角色数量
     */
    private Integer roleCount;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 版本号
     */
    private Integer version;
}
