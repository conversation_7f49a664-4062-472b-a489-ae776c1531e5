package com.bdyl.erp.pisp.user.dto.response;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

import lombok.Data;

import com.bdyl.erp.pisp.user.enums.RoleStatus;

/**
 * 角色响应DTO
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
public class RoleResponse {

    /**
     * 角色ID
     */
    private Long id;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色代码
     */
    private String roleCode;

    /**
     * 角色描述
     */
    private String description;

    /**
     * 是否系统角色
     */
    private Boolean isSystem;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

    /**
     * 角色状态
     */
    private RoleStatus status;

    /**
     * 权限列表
     */
    private List<PermissionResponse> permissions;

    /**
     * 权限代码集合
     */
    private Set<String> permissionCodes;

    /**
     * 用户数量
     */
    private Integer userCount;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 版本号
     */
    private Integer version;
}
