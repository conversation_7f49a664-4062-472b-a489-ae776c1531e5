package com.bdyl.erp.pisp.user.entity;

import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;
import lombok.EqualsAndHashCode;

import com.bdyl.erp.pisp.common.core.entity.BaseEntity;
import com.bdyl.erp.pisp.user.enums.DepartmentStatus;

/**
 * 部门实体类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_departments")
public class Department extends BaseEntity {

    /**
     * 部门ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 部门名称
     */
    @TableField("dept_name")
    private String deptName;

    /**
     * 部门代码
     */
    @TableField("dept_code")
    private String deptCode;

    /**
     * 父部门ID
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 部门层级
     */
    @TableField("dept_level")
    private Integer deptLevel;

    /**
     * 部门路径
     */
    @TableField("dept_path")
    private String deptPath;

    /**
     * 部门描述
     */
    @TableField("description")
    private String description;

    /**
     * 部门负责人ID
     */
    @TableField("leader_id")
    private Long leaderId;

    /**
     * 联系电话
     */
    @TableField("phone")
    private String phone;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 排序顺序
     */
    @TableField("sort_order")
    private Integer sortOrder = 0;

    /**
     * 部门状态
     */
    @TableField("status")
    private DepartmentStatus status;

    /**
     * 版本号（乐观锁）
     */
    @Version
    private Integer version;

    /**
     * 删除标记
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 部门负责人（关联查询）
     */
    @TableField(exist = false)
    private User leader;

    /**
     * 子部门列表（关联查询）
     */
    @TableField(exist = false)
    private List<Department> children;

    /**
     * 部门员工列表（关联查询）
     */
    @TableField(exist = false)
    private List<User> employees;

    // 业务方法

    /**
     * 启用部门
     */
    public void activate() {
        this.status = DepartmentStatus.ACTIVE;
    }

    /**
     * 禁用部门
     */
    public void deactivate() {
        this.status = DepartmentStatus.INACTIVE;
    }

    /**
     * 判断部门是否启用
     *
     * @return true如果部门是启用状态
     */
    public boolean isActive() {
        return DepartmentStatus.ACTIVE.equals(this.status);
    }

    /**
     * 判断是否为根部门
     *
     * @return true如果是根部门
     */
    public boolean isRootDepartment() {
        return this.parentId == null || this.parentId == 0;
    }

    /**
     * 设置部门负责人
     *
     * @param user 用户对象
     */
    public void setLeader(User user) {
        if (user != null) {
            this.leaderId = user.getId();
            this.leader = user;
        }
    }

    /**
     * 添加子部门
     *
     * @param child 子部门
     */
    public void addChild(Department child) {
        if (children != null && child != null) {
            child.setParentId(this.id);
            child.setDeptLevel((this.deptLevel == null ? 0 : this.deptLevel) + 1);
            children.add(child);
        }
    }

    /**
     * 移除子部门
     *
     * @param child 子部门
     */
    public void removeChild(Department child) {
        if (children != null && child != null) {
            children.remove(child);
        }
    }

    /**
     * 判断是否有子部门
     *
     * @return true如果有子部门
     */
    public boolean hasChildren() {
        return children != null && !children.isEmpty();
    }

    /**
     * 获取员工数量
     *
     * @return 员工数量
     */
    public int getEmployeeCount() {
        return employees == null ? 0 : employees.size();
    }
}
