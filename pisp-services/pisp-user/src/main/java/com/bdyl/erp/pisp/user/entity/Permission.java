package com.bdyl.erp.pisp.user.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;
import lombok.EqualsAndHashCode;

import com.bdyl.erp.pisp.common.core.entity.BaseEntity;

/**
 * 权限实体类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_permissions")
public class Permission extends BaseEntity {

    /**
     * 权限ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 权限名称
     */
    @TableField("permission_name")
    private String permissionName;

    /**
     * 权限代码
     */
    @TableField("permission_code")
    private String permissionCode;

    /**
     * 权限描述
     */
    @TableField("description")
    private String description;

    /**
     * 权限类型（MENU-菜单，BUTTON-按钮，API-接口）
     */
    @TableField("permission_type")
    private String permissionType;

    /**
     * 父权限ID
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 权限路径
     */
    @TableField("permission_path")
    private String permissionPath;

    /**
     * 排序顺序
     */
    @TableField("sort_order")
    private Integer sortOrder = 0;

    /**
     * 是否系统权限
     */
    @TableField("is_system")
    private Boolean isSystem = false;

    /**
     * 版本号（乐观锁）
     */
    @Version
    private Integer version;

    /**
     * 删除标记
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    // 业务方法

    /**
     * 判断是否为根权限
     *
     * @return true如果是根权限
     */
    public boolean isRootPermission() {
        return this.parentId == null || this.parentId == 0;
    }

    /**
     * 判断是否为系统权限
     *
     * @return true如果是系统权限
     */
    public boolean isSystemPermission() {
        return Boolean.TRUE.equals(this.isSystem);
    }

    /**
     * 判断是否为菜单权限
     *
     * @return true如果是菜单权限
     */
    public boolean isMenuPermission() {
        return "MENU".equals(this.permissionType);
    }

    /**
     * 判断是否为按钮权限
     *
     * @return true如果是按钮权限
     */
    public boolean isButtonPermission() {
        return "BUTTON".equals(this.permissionType);
    }

    /**
     * 判断是否为API权限
     *
     * @return true如果是API权限
     */
    public boolean isApiPermission() {
        return "API".equals(this.permissionType);
    }
}
