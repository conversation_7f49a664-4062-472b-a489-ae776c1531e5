package com.bdyl.erp.pisp.user.entity;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;
import lombok.EqualsAndHashCode;

import com.bdyl.erp.pisp.common.core.entity.BaseEntity;
import com.bdyl.erp.pisp.user.enums.RoleStatus;

/**
 * 角色实体类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_roles")
public class Role extends BaseEntity {

    /**
     * 角色ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 角色名称
     */
    @TableField("role_name")
    private String roleName;

    /**
     * 角色代码
     */
    @TableField("role_code")
    private String roleCode;

    /**
     * 角色描述
     */
    @TableField("description")
    private String description;

    /**
     * 是否系统角色
     */
    @TableField("is_system")
    private Boolean isSystem = false;

    /**
     * 排序顺序
     */
    @TableField("sort_order")
    private Integer sortOrder = 0;

    /**
     * 角色状态
     */
    @TableField("status")
    private RoleStatus status;

    /**
     * 版本号（乐观锁）
     */
    @Version
    private Integer version;

    /**
     * 删除标记
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 权限列表（关联查询）
     */
    @TableField(exist = false)
    private List<Permission> permissions;

    // 业务方法

    /**
     * 启用角色
     */
    public void activate() {
        this.status = RoleStatus.ACTIVE;
    }

    /**
     * 禁用角色
     */
    public void deactivate() {
        this.status = RoleStatus.INACTIVE;
    }

    /**
     * 判断角色是否启用
     *
     * @return true如果角色是启用状态
     */
    public boolean isActive() {
        return RoleStatus.ACTIVE.equals(this.status);
    }

    /**
     * 判断是否为系统角色
     *
     * @return true如果是系统角色
     */
    public boolean isSystemRole() {
        return Boolean.TRUE.equals(this.isSystem);
    }

    /**
     * 获取权限代码集合
     *
     * @return 权限代码集合
     */
    public Set<String> getPermissionCodes() {
        if (permissions == null || permissions.isEmpty()) {
            return Set.of();
        }

        return permissions.stream().map(Permission::getPermissionCode).collect(Collectors.toSet());
    }

    /**
     * 判断角色是否拥有指定权限
     *
     * @param permissionCode 权限代码
     * @return true如果拥有权限
     */
    public boolean hasPermission(String permissionCode) {
        return getPermissionCodes().contains(permissionCode);
    }

    /**
     * 添加权限
     *
     * @param permission 权限对象
     */
    public void addPermission(Permission permission) {
        if (permissions != null && !permissions.contains(permission)) {
            permissions.add(permission);
        }
    }

    /**
     * 移除权限
     *
     * @param permission 权限对象
     */
    public void removePermission(Permission permission) {
        if (permissions != null) {
            permissions.remove(permission);
        }
    }
}
