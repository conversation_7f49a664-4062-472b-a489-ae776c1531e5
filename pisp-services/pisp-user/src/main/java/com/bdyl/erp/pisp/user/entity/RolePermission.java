package com.bdyl.erp.pisp.user.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;
import lombok.EqualsAndHashCode;

import com.bdyl.erp.pisp.common.core.entity.BaseEntity;

/**
 * 角色权限关联实体类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_role_permissions")
public class RolePermission extends BaseEntity {

    /**
     * 关联ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 角色ID
     */
    @TableField("role_id")
    private Long roleId;

    /**
     * 权限ID
     */
    @TableField("permission_id")
    private Long permissionId;

    /**
     * 分配时间
     */
    @TableField("assigned_at")
    private LocalDateTime assignedAt;

    /**
     * 分配人ID
     */
    @TableField("assigned_by")
    private Long assignedBy;

    /**
     * 版本号（乐观锁）
     */
    @Version
    private Integer version;

    /**
     * 删除标记
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 角色信息（关联查询）
     */
    @TableField(exist = false)
    private Role role;

    /**
     * 权限信息（关联查询）
     */
    @TableField(exist = false)
    private Permission permission;

    /**
     * 分配人信息（关联查询）
     */
    @TableField(exist = false)
    private User assignedByUser;

    // 业务方法

    /**
     * 创建角色权限关联
     *
     * @param roleId 角色ID
     * @param permissionId 权限ID
     * @param assignedBy 分配人ID
     * @return 角色权限关联对象
     */
    public static RolePermission create(Long roleId, Long permissionId, Long assignedBy) {
        RolePermission rolePermission = new RolePermission();
        rolePermission.setRoleId(roleId);
        rolePermission.setPermissionId(permissionId);
        rolePermission.setAssignedBy(assignedBy);
        rolePermission.setAssignedAt(LocalDateTime.now());
        return rolePermission;
    }
}
