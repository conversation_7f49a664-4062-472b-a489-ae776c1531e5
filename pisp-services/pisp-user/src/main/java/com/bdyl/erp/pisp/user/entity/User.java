package com.bdyl.erp.pisp.user.entity;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

import com.bdyl.erp.pisp.common.core.entity.BaseEntity;
import com.bdyl.erp.pisp.user.enums.UserStatus;

/**
 * 用户实体类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_users")
public class User extends BaseEntity {

    /**
     * 用户ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户名
     */
    @TableField("username")
    private String username;

    /**
     * 密码
     */
    @JsonIgnore
    @TableField("password_hash")
    private String password;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;

    /**
     * 真实姓名
     */
    @TableField("real_name")
    private String realName;

    /**
     * 头像URL
     */
    @TableField("avatar_url")
    private String avatarUrl;

    /**
     * 用户状态
     */
    @TableField("status")
    private UserStatus status;

    /**
     * 部门ID
     */
    @TableField("department_id")
    private Long departmentId;

    /**
     * 最后登录时间
     */
    @TableField("last_login_time")
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    @TableField("last_login_ip")
    private String lastLoginIp;

    /**
     * 登录次数
     */
    @TableField("login_count")
    private Integer loginCount = 0;

    /**
     * 版本号（乐观锁）
     */
    @Version
    private Integer version;

    /**
     * 删除标记
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 部门信息（关联查询）
     */
    @TableField(exist = false)
    private Department department;

    /**
     * 角色列表（关联查询）
     */
    @TableField(exist = false)
    private List<Role> roles;

    // 业务方法

    /**
     * 激活用户
     */
    public void activate() {
        this.status = UserStatus.ACTIVE;
    }

    /**
     * 停用用户
     */
    public void deactivate() {
        this.status = UserStatus.INACTIVE;
    }

    /**
     * 锁定用户
     */
    public void lock() {
        this.status = UserStatus.LOCKED;
    }

    /**
     * 暂停用户
     */
    public void suspend() {
        this.status = UserStatus.SUSPENDED;
    }

    /**
     * 判断用户是否激活
     *
     * @return true如果用户是激活状态
     */
    public boolean isActive() {
        return UserStatus.ACTIVE.equals(this.status);
    }

    /**
     * 判断用户是否被锁定
     *
     * @return true如果用户被锁定
     */
    public boolean isLocked() {
        return UserStatus.LOCKED.equals(this.status);
    }

    /**
     * 更新登录信息
     *
     * @param loginIp 登录IP
     */
    public void updateLoginInfo(String loginIp) {
        this.lastLoginTime = LocalDateTime.now();
        this.lastLoginIp = loginIp;
        this.loginCount = (this.loginCount == null ? 0 : this.loginCount) + 1;
    }

    /**
     * 获取用户权限集合
     *
     * @return 权限代码集合
     */
    public Set<String> getPermissions() {
        if (roles == null || roles.isEmpty()) {
            return Set.of();
        }

        return roles.stream().filter(role -> role.getStatus().isActive())
            .flatMap(role -> role.getPermissions().stream()).map(Permission::getPermissionCode)
            .collect(Collectors.toSet());
    }

    /**
     * 获取用户角色代码集合
     *
     * @return 角色代码集合
     */
    public Set<String> getRoleCodes() {
        if (roles == null || roles.isEmpty()) {
            return Set.of();
        }

        return roles.stream().filter(role -> role.getStatus().isActive()).map(Role::getRoleCode)
            .collect(Collectors.toSet());
    }

    /**
     * 判断用户是否拥有指定权限
     *
     * @param permissionCode 权限代码
     * @return true如果拥有权限
     */
    public boolean hasPermission(String permissionCode) {
        return getPermissions().contains(permissionCode);
    }

    /**
     * 判断用户是否拥有指定角色
     *
     * @param roleCode 角色代码
     * @return true如果拥有角色
     */
    public boolean hasRole(String roleCode) {
        return getRoleCodes().contains(roleCode);
    }
}
