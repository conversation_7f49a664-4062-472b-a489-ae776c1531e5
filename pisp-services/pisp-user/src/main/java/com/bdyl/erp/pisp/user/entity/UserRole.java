package com.bdyl.erp.pisp.user.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;
import lombok.EqualsAndHashCode;

import com.bdyl.erp.pisp.common.core.entity.BaseEntity;

/**
 * 用户角色关联实体类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user_roles")
public class UserRole extends BaseEntity {

    /**
     * 关联ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 角色ID
     */
    @TableField("role_id")
    private Long roleId;

    /**
     * 分配时间
     */
    @TableField("assigned_at")
    private LocalDateTime assignedAt;

    /**
     * 分配人ID
     */
    @TableField("assigned_by")
    private Long assignedBy;

    /**
     * 版本号（乐观锁）
     */
    @Version
    private Integer version;

    /**
     * 删除标记
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 用户信息（关联查询）
     */
    @TableField(exist = false)
    private User user;

    /**
     * 角色信息（关联查询）
     */
    @TableField(exist = false)
    private Role role;

    /**
     * 分配人信息（关联查询）
     */
    @TableField(exist = false)
    private User assignedByUser;

    // 业务方法

    /**
     * 创建用户角色关联
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     * @param assignedBy 分配人ID
     * @return 用户角色关联对象
     */
    public static UserRole create(Long userId, Long roleId, Long assignedBy) {
        UserRole userRole = new UserRole();
        userRole.setUserId(userId);
        userRole.setRoleId(roleId);
        userRole.setAssignedBy(assignedBy);
        userRole.setAssignedAt(LocalDateTime.now());
        return userRole;
    }
}
