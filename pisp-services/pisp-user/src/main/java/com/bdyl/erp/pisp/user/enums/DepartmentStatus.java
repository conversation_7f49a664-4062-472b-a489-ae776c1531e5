package com.bdyl.erp.pisp.user.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 部门状态枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
public enum DepartmentStatus {

    /**
     * 启用状态
     */
    ACTIVE("ACTIVE", "启用"),

    /**
     * 禁用状态
     */
    INACTIVE("INACTIVE", "禁用");

    /**
     * 代码
     */
    @EnumValue
    @JsonValue
    private final String code;

    /**
     * 描述
     */
    private final String description;

    DepartmentStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 状态代码
     * @return 部门状态枚举
     */
    public static DepartmentStatus fromCode(String code) {
        for (DepartmentStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的部门状态代码: " + code);
    }

    /**
     * 判断是否为启用状态
     *
     * @return true如果是启用状态
     */
    public boolean isActive() {
        return this == ACTIVE;
    }
}
