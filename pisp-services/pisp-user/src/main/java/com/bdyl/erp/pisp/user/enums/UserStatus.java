package com.bdyl.erp.pisp.user.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 用户状态枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
public enum UserStatus {

    /**
     * 激活状态
     */
    ACTIVE("ACTIVE", "激活"),

    /**
     * 非激活状态
     */
    INACTIVE("INACTIVE", "非激活"),

    /**
     * 锁定状态
     */
    LOCKED("LOCKED", "锁定"),

    /**
     * 暂停状态
     */
    SUSPENDED("SUSPENDED", "暂停");

    /**
     * 代码
     */
    @EnumValue
    @JsonValue
    private final String code;

    /**
     * 描述
     */
    private final String description;

    UserStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 状态代码
     * @return 用户状态枚举
     */
    public static UserStatus fromCode(String code) {
        for (UserStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的用户状态代码: " + code);
    }

    /**
     * 判断是否为激活状态
     *
     * @return true如果是激活状态
     */
    public boolean isActive() {
        return this == ACTIVE;
    }

    /**
     * 判断是否为锁定状态
     *
     * @return true如果是锁定状态
     */
    public boolean isLocked() {
        return this == LOCKED;
    }
}
