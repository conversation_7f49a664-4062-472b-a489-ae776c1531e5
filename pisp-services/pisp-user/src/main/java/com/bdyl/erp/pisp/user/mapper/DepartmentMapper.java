package com.bdyl.erp.pisp.user.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.bdyl.erp.pisp.user.entity.Department;

/**
 * 部门数据访问接口
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Mapper
public interface DepartmentMapper extends BaseMapper<Department> {

    /**
     * 根据部门代码查询部门
     *
     * @param deptCode 部门代码
     * @return 部门信息
     */
    Department selectByDeptCode(@Param("deptCode") String deptCode);

    /**
     * 根据部门ID查询部门（包含负责人信息）
     *
     * @param deptId 部门ID
     * @return 部门信息
     */
    Department selectByIdWithLeader(@Param("deptId") Long deptId);

    /**
     * 分页查询部门列表（包含负责人信息）
     *
     * @param page 分页参数
     * @param deptName 部门名称（模糊查询）
     * @param deptCode 部门代码（模糊查询）
     * @param parentId 父部门ID
     * @param status 部门状态
     * @return 分页部门列表
     */
    IPage<Department> selectDepartmentPageWithLeader(Page<Department> page, @Param("deptName") String deptName,
        @Param("deptCode") String deptCode, @Param("parentId") Long parentId, @Param("status") String status);

    /**
     * 根据父部门ID查询子部门列表
     *
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    List<Department> selectByParentId(@Param("parentId") Long parentId);

    /**
     * 查询部门树结构
     *
     * @return 部门树列表
     */
    List<Department> selectDepartmentTree();

    /**
     * 查询部门树结构（包含员工数量）
     *
     * @return 部门树列表
     */
    List<Department> selectDepartmentTreeWithEmployeeCount();

    /**
     * 检查部门代码是否存在
     *
     * @param deptCode 部门代码
     * @param excludeId 排除的部门ID（用于更新时检查）
     * @return 存在数量
     */
    int checkDeptCodeExists(@Param("deptCode") String deptCode, @Param("excludeId") Long excludeId);

    /**
     * 检查部门名称是否存在
     *
     * @param deptName 部门名称
     * @param parentId 父部门ID
     * @param excludeId 排除的部门ID（用于更新时检查）
     * @return 存在数量
     */
    int checkDeptNameExists(@Param("deptName") String deptName, @Param("parentId") Long parentId,
        @Param("excludeId") Long excludeId);

    /**
     * 批量更新部门状态
     *
     * @param deptIds 部门ID列表
     * @param status 新状态
     * @param updaterId 更新人ID
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("deptIds") List<Long> deptIds, @Param("status") String status,
        @Param("updaterId") Long updaterId);

    /**
     * 查询所有启用的部门
     *
     * @return 部门列表
     */
    List<Department> selectActiveDepartments();

    /**
     * 根据部门ID查询员工数量
     *
     * @param deptId 部门ID
     * @return 员工数量
     */
    int countEmployeesByDeptId(@Param("deptId") Long deptId);

    /**
     * 根据部门ID查询所有子部门ID（递归）
     *
     * @param deptId 部门ID
     * @return 子部门ID列表
     */
    List<Long> selectChildDeptIds(@Param("deptId") Long deptId);

    /**
     * 更新部门路径
     *
     * @param deptId 部门ID
     * @param deptPath 部门路径
     * @return 更新数量
     */
    int updateDeptPath(@Param("deptId") Long deptId, @Param("deptPath") String deptPath);
}
