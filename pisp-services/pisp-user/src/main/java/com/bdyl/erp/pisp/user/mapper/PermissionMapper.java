package com.bdyl.erp.pisp.user.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.bdyl.erp.pisp.user.entity.Permission;

/**
 * 权限数据访问接口
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Mapper
public interface PermissionMapper extends BaseMapper<Permission> {

    /**
     * 根据权限代码查询权限
     *
     * @param permissionCode 权限代码
     * @return 权限信息
     */
    Permission selectByPermissionCode(@Param("permissionCode") String permissionCode);

    /**
     * 根据父权限ID查询子权限列表
     *
     * @param parentId 父权限ID
     * @return 子权限列表
     */
    List<Permission> selectByParentId(@Param("parentId") Long parentId);

    /**
     * 查询权限树结构
     *
     * @return 权限树列表
     */
    List<Permission> selectPermissionTree();

    /**
     * 根据角色ID查询权限列表
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    List<Permission> selectByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据用户ID查询权限列表
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    List<Permission> selectByUserId(@Param("userId") Long userId);

    /**
     * 检查权限代码是否存在
     *
     * @param permissionCode 权限代码
     * @param excludeId 排除的权限ID（用于更新时检查）
     * @return 存在数量
     */
    int checkPermissionCodeExists(@Param("permissionCode") String permissionCode, @Param("excludeId") Long excludeId);

    /**
     * 检查权限名称是否存在
     *
     * @param permissionName 权限名称
     * @param parentId 父权限ID
     * @param excludeId 排除的权限ID（用于更新时检查）
     * @return 存在数量
     */
    int checkPermissionNameExists(@Param("permissionName") String permissionName, @Param("parentId") Long parentId,
        @Param("excludeId") Long excludeId);

    /**
     * 根据权限类型查询权限列表
     *
     * @param permissionType 权限类型
     * @return 权限列表
     */
    List<Permission> selectByPermissionType(@Param("permissionType") String permissionType);

    /**
     * 查询所有菜单权限（树形结构）
     *
     * @return 菜单权限列表
     */
    List<Permission> selectMenuPermissions();

    /**
     * 根据权限ID列表查询角色数量
     *
     * @param permissionIds 权限ID列表
     * @return 角色数量
     */
    int countRolesByPermissionIds(@Param("permissionIds") List<Long> permissionIds);
}
