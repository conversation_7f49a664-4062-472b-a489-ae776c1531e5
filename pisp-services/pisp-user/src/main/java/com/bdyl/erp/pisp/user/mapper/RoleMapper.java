package com.bdyl.erp.pisp.user.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.bdyl.erp.pisp.user.entity.Permission;
import com.bdyl.erp.pisp.user.entity.Role;

/**
 * 角色数据访问接口
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Mapper
public interface RoleMapper extends BaseMapper<Role> {

    /**
     * 根据角色代码查询角色
     *
     * @param roleCode 角色代码
     * @return 角色信息
     */
    Role selectByRoleCode(@Param("roleCode") String roleCode);

    /**
     * 根据角色ID查询角色（包含权限信息）
     *
     * @param roleId 角色ID
     * @return 角色信息
     */
    Role selectByIdWithPermissions(@Param("roleId") Long roleId);

    /**
     * 分页查询角色列表
     *
     * @param page 分页参数
     * @param roleName 角色名称（模糊查询）
     * @param roleCode 角色代码（模糊查询）
     * @param status 角色状态
     * @param isSystem 是否系统角色
     * @return 分页角色列表
     */
    IPage<Role> selectRolePage(Page<Role> page, @Param("roleName") String roleName, @Param("roleCode") String roleCode,
        @Param("status") String status, @Param("isSystem") Boolean isSystem);

    /**
     * 根据角色ID查询权限列表
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    List<Permission> selectPermissionsByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据用户ID查询角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<Role> selectByUserId(@Param("userId") Long userId);

    /**
     * 检查角色代码是否存在
     *
     * @param roleCode 角色代码
     * @param excludeId 排除的角色ID（用于更新时检查）
     * @return 是否存在
     */
    boolean checkRoleCodeExists(@Param("roleCode") String roleCode, @Param("excludeId") Long excludeId);

    /**
     * 检查角色名称是否存在
     *
     * @param roleName 角色名称
     * @param excludeId 排除的角色ID（用于更新时检查）
     * @return 是否存在
     */
    boolean checkRoleNameExists(@Param("roleName") String roleName, @Param("excludeId") Long excludeId);

    /**
     * 批量更新角色状态
     *
     * @param roleIds 角色ID列表
     * @param status 新状态
     * @param updaterId 更新人ID
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("roleIds") List<Long> roleIds, @Param("status") String status,
        @Param("updaterId") Long updaterId);

    /**
     * 查询所有启用的角色
     *
     * @return 角色列表
     */
    List<Role> selectActiveRoles();

    /**
     * 根据角色ID列表查询用户数量
     *
     * @param roleIds 角色ID列表
     * @return 用户数量
     */
    int countUsersByRoleIds(@Param("roleIds") List<Long> roleIds);
}
