package com.bdyl.erp.pisp.user.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.bdyl.erp.pisp.user.entity.RolePermission;

/**
 * 角色权限关联数据访问接口
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Mapper
public interface RolePermissionMapper extends BaseMapper<RolePermission> {

    /**
     * 根据角色ID查询角色权限关联列表
     *
     * @param roleId 角色ID
     * @return 角色权限关联列表
     */
    List<RolePermission> selectByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据权限ID查询角色权限关联列表
     *
     * @param permissionId 权限ID
     * @return 角色权限关联列表
     */
    List<RolePermission> selectByPermissionId(@Param("permissionId") Long permissionId);

    /**
     * 检查角色权限关联是否存在
     *
     * @param roleId 角色ID
     * @param permissionId 权限ID
     * @return 存在数量
     */
    int checkRolePermissionExists(@Param("roleId") Long roleId, @Param("permissionId") Long permissionId);

    /**
     * 根据角色ID删除角色权限关联
     *
     * @param roleId 角色ID
     * @return 删除数量
     */
    int deleteByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据权限ID删除角色权限关联
     *
     * @param permissionId 权限ID
     * @return 删除数量
     */
    int deleteByPermissionId(@Param("permissionId") Long permissionId);

    /**
     * 批量插入角色权限关联
     *
     * @param rolePermissions 角色权限关联列表
     * @return 插入数量
     */
    int batchInsert(@Param("rolePermissions") List<RolePermission> rolePermissions);

    /**
     * 根据角色ID和权限ID列表删除角色权限关联
     *
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @return 删除数量
     */
    int deleteByRoleIdAndPermissionIds(@Param("roleId") Long roleId, @Param("permissionIds") List<Long> permissionIds);

    /**
     * 根据角色ID列表和权限ID删除角色权限关联
     *
     * @param roleIds 角色ID列表
     * @param permissionId 权限ID
     * @return 删除数量
     */
    int deleteByRoleIdsAndPermissionId(@Param("roleIds") List<Long> roleIds, @Param("permissionId") Long permissionId);

    /**
     * 根据权限ID查询角色数量
     *
     * @param permissionId 权限ID
     * @return 角色数量
     */
    int countRolesByPermissionId(@Param("permissionId") Long permissionId);

    /**
     * 根据角色ID查询权限数量
     *
     * @param roleId 角色ID
     * @return 权限数量
     */
    int countPermissionsByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据角色ID列表查询权限ID列表
     *
     * @param roleIds 角色ID列表
     * @return 权限ID列表
     */
    List<Long> selectPermissionIdsByRoleIds(@Param("roleIds") List<Long> roleIds);
}
