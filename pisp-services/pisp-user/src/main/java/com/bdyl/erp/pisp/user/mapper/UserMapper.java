package com.bdyl.erp.pisp.user.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.bdyl.erp.pisp.user.dto.request.UserQueryRequest;
import com.bdyl.erp.pisp.user.entity.Role;
import com.bdyl.erp.pisp.user.entity.User;

/**
 * 用户数据访问接口
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据用户名查询用户（包含角色和权限信息）
     *
     * @param username 用户名
     * @return 用户信息
     */
    User selectByUsernameWithRoles(@Param("username") String username);

    /**
     * 根据邮箱查询用户
     *
     * @param email 邮箱
     * @return 用户信息
     */
    User selectByEmail(@Param("email") String email);

    /**
     * 根据手机号查询用户
     *
     * @param phone 手机号
     * @return 用户信息
     */
    User selectByPhone(@Param("phone") String phone);

    /**
     * 根据部门ID查询用户列表
     *
     * @param departmentId 部门ID
     * @return 用户列表
     */
    List<User> selectByDepartmentId(@Param("departmentId") Long departmentId);

    /**
     * 分页查询用户列表（包含部门信息）
     *
     * @param page 分页参数
     * @param request 查询条件
     * @return 分页用户列表
     */
    IPage<User> selectUserPageWithDepartment(Page<User> page, @Param("request") UserQueryRequest request);

    /**
     * 根据用户ID查询用户角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<Role> selectRolesByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID查询用户权限代码列表
     *
     * @param userId 用户ID
     * @return 权限代码列表
     */
    List<String> selectPermissionCodesByUserId(@Param("userId") Long userId);

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @param excludeId 排除的用户ID（用于更新时检查）
     * @return 是否存在
     */
    boolean checkUsernameExists(@Param("username") String username, @Param("excludeId") Long excludeId);

    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @param excludeId 排除的用户ID（用于更新时检查）
     * @return 是否存在
     */
    boolean checkEmailExists(@Param("email") String email, @Param("excludeId") Long excludeId);

    /**
     * 检查手机号是否存在
     *
     * @param phone 手机号
     * @param excludeId 排除的用户ID（用于更新时检查）
     * @return 是否存在
     */
    boolean checkPhoneExists(@Param("phone") String phone, @Param("excludeId") Long excludeId);

    /**
     * 批量更新用户状态
     *
     * @param userIds 用户ID列表
     * @param status 新状态
     * @param updaterId 更新人ID
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("userIds") List<Long> userIds, @Param("status") String status,
        @Param("updaterId") Long updaterId);

    /**
     * 根据部门ID列表查询用户数量
     *
     * @param departmentIds 部门ID列表
     * @return 用户数量
     */
    int countByDepartmentIds(@Param("departmentIds") List<Long> departmentIds);

    /**
     * 更新用户最后登录信息
     *
     * @param userId 用户ID
     * @param loginIp 登录IP
     * @return 更新数量
     */
    int updateLastLoginInfo(@Param("userId") Long userId, @Param("loginIp") String loginIp);

    /**
     * 根据ID查询用户（包含角色信息）
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    User selectByIdWithRoles(@Param("userId") Long userId);
}
