package com.bdyl.erp.pisp.user.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.bdyl.erp.pisp.user.entity.UserRole;

/**
 * 用户角色关联数据访问接口
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Mapper
public interface UserRoleMapper extends BaseMapper<UserRole> {

    /**
     * 根据用户ID查询用户角色关联列表
     *
     * @param userId 用户ID
     * @return 用户角色关联列表
     */
    List<UserRole> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据角色ID查询用户角色关联列表
     *
     * @param roleId 角色ID
     * @return 用户角色关联列表
     */
    List<UserRole> selectByRoleId(@Param("roleId") Long roleId);

    /**
     * 检查用户角色关联是否存在
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 存在数量
     */
    int checkUserRoleExists(@Param("userId") Long userId, @Param("roleId") Long roleId);

    /**
     * 根据用户ID删除用户角色关联
     *
     * @param userId 用户ID
     * @return 删除数量
     */
    int deleteByUserId(@Param("userId") Long userId);

    /**
     * 根据角色ID删除用户角色关联
     *
     * @param roleId 角色ID
     * @return 删除数量
     */
    int deleteByRoleId(@Param("roleId") Long roleId);

    /**
     * 批量插入用户角色关联
     *
     * @param userRoles 用户角色关联列表
     * @return 插入数量
     */
    int batchInsert(@Param("userRoles") List<UserRole> userRoles);

    /**
     * 根据用户ID和角色ID列表删除用户角色关联
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 删除数量
     */
    int deleteByUserIdAndRoleIds(@Param("userId") Long userId, @Param("roleIds") List<Long> roleIds);

    /**
     * 根据用户ID列表和角色ID删除用户角色关联
     *
     * @param userIds 用户ID列表
     * @param roleId 角色ID
     * @return 删除数量
     */
    int deleteByUserIdsAndRoleId(@Param("userIds") List<Long> userIds, @Param("roleId") Long roleId);

    /**
     * 根据角色ID查询用户数量
     *
     * @param roleId 角色ID
     * @return 用户数量
     */
    int countUsersByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据用户ID查询角色数量
     *
     * @param userId 用户ID
     * @return 角色数量
     */
    int countRolesByUserId(@Param("userId") Long userId);
}
