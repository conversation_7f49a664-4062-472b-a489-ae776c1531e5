package com.bdyl.erp.pisp.user.service;

import com.bdyl.erp.pisp.user.dto.request.LoginRequest;
import com.bdyl.erp.pisp.user.dto.request.RefreshTokenRequest;
import com.bdyl.erp.pisp.user.dto.response.LoginResponse;

/**
 * 认证服务接口
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
public interface AuthService {

    /**
     * 用户登录
     *
     * @param request 登录请求
     * @return 登录响应
     */
    LoginResponse login(LoginRequest request);

    /**
     * 用户注销
     *
     * @param userId 用户ID
     */
    void logout(Long userId);

    /**
     * 刷新令牌
     *
     * @param request 刷新令牌请求
     * @return 登录响应
     */
    LoginResponse refreshToken(RefreshTokenRequest request);

    /**
     * 验证令牌
     *
     * @param token 访问令牌
     * @return 是否有效
     */
    boolean validateToken(String token);

    /**
     * 获取当前用户信息
     *
     * @return 用户信息
     */
    LoginResponse.UserInfo getCurrentUserInfo();
}
