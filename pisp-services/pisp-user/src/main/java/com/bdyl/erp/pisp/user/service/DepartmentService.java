package com.bdyl.erp.pisp.user.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import com.bdyl.erp.pisp.user.dto.request.DepartmentCreateRequest;
import com.bdyl.erp.pisp.user.dto.request.DepartmentQueryRequest;
import com.bdyl.erp.pisp.user.dto.request.DepartmentUpdateRequest;
import com.bdyl.erp.pisp.user.dto.response.DepartmentResponse;
import com.bdyl.erp.pisp.user.entity.Department;

/**
 * 部门服务接口
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
public interface DepartmentService extends IService<Department> {

    /**
     * 创建部门
     *
     * @param request 部门创建请求
     * @return 部门响应
     */
    DepartmentResponse createDepartment(DepartmentCreateRequest request);

    /**
     * 更新部门
     *
     * @param request 部门更新请求
     * @return 部门响应
     */
    DepartmentResponse updateDepartment(DepartmentUpdateRequest request);

    /**
     * 根据ID删除部门
     *
     * @param deptId 部门ID
     */
    void deleteDepartment(Long deptId);

    /**
     * 批量删除部门
     *
     * @param deptIds 部门ID列表
     */
    void batchDeleteDepartments(List<Long> deptIds);

    /**
     * 根据ID查询部门详情
     *
     * @param deptId 部门ID
     * @return 部门响应
     */
    DepartmentResponse getDepartmentById(Long deptId);

    /**
     * 根据部门代码查询部门
     *
     * @param deptCode 部门代码
     * @return 部门实体
     */
    Department getByDeptCode(String deptCode);

    /**
     * 分页查询部门列表
     *
     * @param request 查询请求
     * @return 分页部门响应
     */
    IPage<DepartmentResponse> getDepartmentPage(DepartmentQueryRequest request);

    /**
     * 查询部门树结构
     *
     * @return 部门树列表
     */
    List<DepartmentResponse> getDepartmentTree();

    /**
     * 查询部门树结构（包含员工数量）
     *
     * @return 部门树列表
     */
    List<DepartmentResponse> getDepartmentTreeWithEmployeeCount();

    /**
     * 根据父部门ID查询子部门列表
     *
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    List<DepartmentResponse> getChildDepartments(Long parentId);

    /**
     * 查询所有启用的部门
     *
     * @return 部门列表
     */
    List<DepartmentResponse> getActiveDepartments();

    /**
     * 启用部门
     *
     * @param deptId 部门ID
     */
    void activateDepartment(Long deptId);

    /**
     * 禁用部门
     *
     * @param deptId 部门ID
     */
    void deactivateDepartment(Long deptId);

    /**
     * 批量更新部门状态
     *
     * @param deptIds 部门ID列表
     * @param status 新状态
     */
    void batchUpdateDepartmentStatus(List<Long> deptIds, String status);

    /**
     * 设置部门负责人
     *
     * @param deptId 部门ID
     * @param leaderId 负责人ID
     */
    void setDepartmentLeader(Long deptId, Long leaderId);

    /**
     * 移动部门到新的父部门
     *
     * @param deptId 部门ID
     * @param newParentId 新父部门ID
     */
    void moveDepartment(Long deptId, Long newParentId);

    /**
     * 检查部门代码是否存在
     *
     * @param deptCode 部门代码
     * @param excludeId 排除的部门ID
     * @return 是否存在
     */
    boolean isDeptCodeExists(String deptCode, Long excludeId);

    /**
     * 检查部门名称是否存在
     *
     * @param deptName 部门名称
     * @param parentId 父部门ID
     * @param excludeId 排除的部门ID
     * @return 是否存在
     */
    boolean isDeptNameExists(String deptName, Long parentId, Long excludeId);

    /**
     * 获取部门员工数量
     *
     * @param deptId 部门ID
     * @return 员工数量
     */
    int getDepartmentEmployeeCount(Long deptId);

    /**
     * 根据ID查询部门是否存在
     *
     * @param departmentId 部门ID
     * @return 是否存在
     */
    boolean existsById(Long departmentId);

    /**
     * 获取部门路径
     *
     * @param deptId 部门ID
     * @return 部门路径（如：总公司/技术部/开发组）
     */
    String getDepartmentPath(Long deptId);

    /**
     * 根据部门ID查询所有子部门ID（递归）
     *
     * @param deptId 部门ID
     * @return 子部门ID列表
     */
    List<Long> getChildDepartmentIds(Long deptId);
}
