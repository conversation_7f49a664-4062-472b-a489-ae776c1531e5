package com.bdyl.erp.pisp.user.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;

import com.bdyl.erp.pisp.common.web.result.PageResult;
import com.bdyl.erp.pisp.user.dto.request.PermissionCreateRequest;
import com.bdyl.erp.pisp.user.dto.request.PermissionQueryRequest;
import com.bdyl.erp.pisp.user.dto.request.PermissionUpdateRequest;
import com.bdyl.erp.pisp.user.dto.response.PermissionResponse;
import com.bdyl.erp.pisp.user.entity.Permission;

/**
 * 权限服务接口
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
public interface PermissionService extends IService<Permission> {

    /**
     * 创建权限
     *
     * @param request 权限创建请求
     * @return 权限响应信息
     */
    PermissionResponse createPermission(PermissionCreateRequest request);

    /**
     * 更新权限
     *
     * @param request 权限更新请求
     * @return 权限响应信息
     */
    PermissionResponse updatePermission(PermissionUpdateRequest request);

    /**
     * 删除权限
     *
     * @param permissionId 权限ID
     */
    void deletePermission(Long permissionId);

    /**
     * 批量删除权限
     *
     * @param permissionIds 权限ID列表
     */
    void batchDeletePermissions(List<Long> permissionIds);

    /**
     * 根据ID查询权限详情
     *
     * @param permissionId 权限ID
     * @return 权限响应信息
     */
    PermissionResponse getPermissionById(Long permissionId);

    /**
     * 根据权限代码查询权限
     *
     * @param permissionCode 权限代码
     * @return 权限响应信息
     */
    PermissionResponse getPermissionByCode(String permissionCode);

    /**
     * 分页查询权限列表
     *
     * @param request 查询请求
     * @return 分页权限列表
     */
    PageResult<PermissionResponse> getPermissionPage(PermissionQueryRequest request);

    /**
     * 查询权限树结构
     *
     * @return 权限树列表
     */
    List<PermissionResponse> getPermissionTree();

    /**
     * 根据父权限ID查询子权限列表
     *
     * @param parentId 父权限ID
     * @return 子权限列表
     */
    List<PermissionResponse> getChildPermissions(Long parentId);

    /**
     * 根据角色ID查询权限列表
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    List<PermissionResponse> getPermissionsByRoleId(Long roleId);

    /**
     * 根据用户ID查询权限列表
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    List<PermissionResponse> getPermissionsByUserId(Long userId);

    /**
     * 检查权限代码是否存在
     *
     * @param permissionCode 权限代码
     * @param excludeId 排除的权限ID
     * @return 是否存在
     */
    boolean isPermissionCodeExists(String permissionCode, Long excludeId);

    /**
     * 检查权限名称是否存在
     *
     * @param permissionName 权限名称
     * @param parentId 父权限ID
     * @param excludeId 排除的权限ID
     * @return 是否存在
     */
    boolean isPermissionNameExists(String permissionName, Long parentId, Long excludeId);

    /**
     * 检查权限是否有子权限
     *
     * @param permissionId 权限ID
     * @return 是否有子权限
     */
    boolean hasChildPermissions(Long permissionId);

    /**
     * 检查权限是否被角色使用
     *
     * @param permissionId 权限ID
     * @return 是否被使用
     */
    boolean isPermissionInUse(Long permissionId);

    /**
     * 获取权限的角色数量
     *
     * @param permissionId 权限ID
     * @return 角色数量
     */
    int getPermissionRoleCount(Long permissionId);
}
