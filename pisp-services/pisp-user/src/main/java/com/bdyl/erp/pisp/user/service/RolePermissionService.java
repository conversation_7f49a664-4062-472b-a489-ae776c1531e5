package com.bdyl.erp.pisp.user.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;

import com.bdyl.erp.pisp.user.entity.RolePermission;

/**
 * 角色权限关联服务接口
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
public interface RolePermissionService extends IService<RolePermission> {

    /**
     * 根据角色ID删除角色权限关联
     *
     * @param roleId 角色ID
     */
    void removeByRoleId(Long roleId);

    /**
     * 根据权限ID删除角色权限关联
     *
     * @param permissionId 权限ID
     */
    void removeByPermissionId(Long permissionId);

    /**
     * 根据角色ID和权限ID列表删除角色权限关联
     *
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     */
    void removeByRoleIdAndPermissionIds(Long roleId, List<Long> permissionIds);

    /**
     * 根据角色ID查询角色权限关联列表
     *
     * @param roleId 角色ID
     * @return 角色权限关联列表
     */
    List<RolePermission> getByRoleId(Long roleId);

    /**
     * 根据权限ID查询角色权限关联列表
     *
     * @param permissionId 权限ID
     * @return 角色权限关联列表
     */
    List<RolePermission> getByPermissionId(Long permissionId);

    /**
     * 检查角色权限关联是否存在
     *
     * @param roleId 角色ID
     * @param permissionId 权限ID
     * @return 是否存在
     */
    boolean exists(Long roleId, Long permissionId);

    /**
     * 批量插入角色权限关联
     *
     * @param rolePermissions 角色权限关联列表
     */
    void batchInsert(List<RolePermission> rolePermissions);
}
