package com.bdyl.erp.pisp.user.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import com.bdyl.erp.pisp.user.dto.request.RoleCreateRequest;
import com.bdyl.erp.pisp.user.dto.request.RoleQueryRequest;
import com.bdyl.erp.pisp.user.dto.request.RoleUpdateRequest;
import com.bdyl.erp.pisp.user.dto.response.RoleResponse;
import com.bdyl.erp.pisp.user.entity.Role;

/**
 * 角色服务接口
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
public interface RoleService extends IService<Role> {

    /**
     * 创建角色
     *
     * @param request 角色创建请求
     * @return 角色响应DTO
     */
    RoleResponse createRole(RoleCreateRequest request);

    /**
     * 更新角色
     *
     * @param request 角色更新请求
     * @return 角色响应DTO
     */
    RoleResponse updateRole(RoleUpdateRequest request);

    /**
     * 根据ID获取角色
     *
     * @param roleId 角色ID
     * @return 角色响应DTO
     */
    RoleResponse getRoleById(Long roleId);

    /**
     * 根据角色代码获取角色
     *
     * @param roleCode 角色代码
     * @return 角色响应DTO
     */
    RoleResponse getRoleByCode(String roleCode);

    /**
     * 删除角色
     *
     * @param roleId 角色ID
     */
    void deleteRole(Long roleId);

    /**
     * 批量删除角色
     *
     * @param roleIds 角色ID列表
     */
    void batchDeleteRoles(List<Long> roleIds);

    /**
     * 分页查询角色列表
     *
     * @param request 查询请求参数
     * @return 分页角色列表
     */
    IPage<RoleResponse> getRolePage(RoleQueryRequest request);

    /**
     * 获取所有启用的角色
     *
     * @return 角色列表
     */
    List<RoleResponse> getActiveRoles();

    /**
     * 启用角色
     *
     * @param roleId 角色ID
     */
    void activateRole(Long roleId);

    /**
     * 禁用角色
     *
     * @param roleId 角色ID
     */
    void deactivateRole(Long roleId);

    /**
     * 批量更新角色状态
     *
     * @param roleIds 角色ID列表
     * @param status 新状态
     */
    void batchUpdateRoleStatus(List<Long> roleIds, String status);

    /**
     * 为角色分配权限
     *
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     */
    void assignPermissionsToRole(Long roleId, List<Long> permissionIds);

    /**
     * 移除角色权限
     *
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     */
    void removePermissionsFromRole(Long roleId, List<Long> permissionIds);

    /**
     * 根据用户ID获取角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<RoleResponse> getRolesByUserId(Long userId);

    /**
     * 检查角色名称是否存在
     *
     * @param roleName 角色名称
     * @param excludeId 排除的角色ID
     * @return 是否存在
     */
    boolean isRoleNameExists(String roleName, Long excludeId);

    /**
     * 检查角色代码是否存在
     *
     * @param roleCode 角色代码
     * @param excludeId 排除的角色ID
     * @return 是否存在
     */
    boolean isRoleCodeExists(String roleCode, Long excludeId);

    /**
     * 获取角色的用户数量
     *
     * @param roleId 角色ID
     * @return 用户数量
     */
    int getRoleUserCount(Long roleId);
}
