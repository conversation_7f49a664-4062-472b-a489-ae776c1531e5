package com.bdyl.erp.pisp.user.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;

import com.bdyl.erp.pisp.user.entity.UserRole;

/**
 * 用户角色关联服务接口
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
public interface UserRoleService extends IService<UserRole> {

    /**
     * 根据用户ID删除用户角色关联
     *
     * @param userId 用户ID
     */
    void removeByUserId(Long userId);

    /**
     * 根据角色ID删除用户角色关联
     *
     * @param roleId 角色ID
     */
    void removeByRoleId(Long roleId);

    /**
     * 根据用户ID和角色ID列表删除用户角色关联
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     */
    void removeByUserIdAndRoleIds(Long userId, List<Long> roleIds);

    /**
     * 根据用户ID查询用户角色关联列表
     *
     * @param userId 用户ID
     * @return 用户角色关联列表
     */
    List<UserRole> getByUserId(Long userId);

    /**
     * 根据角色ID查询用户角色关联列表
     *
     * @param roleId 角色ID
     * @return 用户角色关联列表
     */
    List<UserRole> getByRoleId(Long roleId);

    /**
     * 检查用户角色关联是否存在
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 是否存在
     */
    boolean exists(Long userId, Long roleId);
}
