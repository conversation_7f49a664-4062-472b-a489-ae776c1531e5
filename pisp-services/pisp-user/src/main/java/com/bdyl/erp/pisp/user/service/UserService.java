package com.bdyl.erp.pisp.user.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import com.bdyl.erp.pisp.user.dto.request.PasswordChangeRequest;
import com.bdyl.erp.pisp.user.dto.request.UserCreateRequest;
import com.bdyl.erp.pisp.user.dto.request.UserQueryRequest;
import com.bdyl.erp.pisp.user.dto.request.UserUpdateRequest;
import com.bdyl.erp.pisp.user.dto.response.UserResponse;
import com.bdyl.erp.pisp.user.entity.User;

/**
 * 用户服务接口
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
public interface UserService extends IService<User> {

    /**
     * 创建用户
     *
     * @param request 用户创建请求
     * @return 用户响应
     */
    UserResponse createUser(UserCreateRequest request);

    /**
     * 更新用户
     *
     * @param request 用户更新请求
     * @return 用户响应
     */
    UserResponse updateUser(UserUpdateRequest request);

    /**
     * 根据ID删除用户
     *
     * @param userId 用户ID
     */
    void deleteUser(Long userId);

    /**
     * 批量删除用户
     *
     * @param userIds 用户ID列表
     */
    void batchDeleteUsers(List<Long> userIds);

    /**
     * 根据ID查询用户详情
     *
     * @param userId 用户ID
     * @return 用户响应
     */
    UserResponse getUserById(Long userId);

    /**
     * 根据用户名查询用户
     *
     * @param username 用户名
     * @return 用户实体
     */
    User getUserByUsername(String username);

    /**
     * 根据邮箱查询用户
     *
     * @param email 邮箱
     * @return 用户实体
     */
    User getUserByEmail(String email);

    /**
     * 根据手机号查询用户
     *
     * @param phone 手机号
     * @return 用户实体
     */
    User getUserByPhone(String phone);

    /**
     * 分页查询用户列表
     *
     * @param request 查询请求
     * @return 分页用户响应
     */
    IPage<UserResponse> getUserPage(UserQueryRequest request);

    /**
     * 根据部门ID查询用户列表
     *
     * @param departmentId 部门ID
     * @return 用户响应列表
     */
    List<UserResponse> getUsersByDepartmentId(Long departmentId);

    /**
     * 修改密码
     *
     * @param request 密码修改请求
     */
    void changePassword(PasswordChangeRequest request);

    /**
     * 重置密码
     *
     * @param userId 用户ID
     * @param newPassword 新密码
     */
    void resetPassword(Long userId, String newPassword);

    /**
     * 激活用户
     *
     * @param userId 用户ID
     */
    void activateUser(Long userId);

    /**
     * 停用用户
     *
     * @param userId 用户ID
     */
    void deactivateUser(Long userId);

    /**
     * 锁定用户
     *
     * @param userId 用户ID
     */
    void lockUser(Long userId);

    /**
     * 批量更新用户状态
     *
     * @param userIds 用户ID列表
     * @param status 新状态
     */
    void batchUpdateUserStatus(List<Long> userIds, String status);

    /**
     * 分配角色给用户
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     */
    void assignRolesToUser(Long userId, List<Long> roleIds);

    /**
     * 移除用户角色
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     */
    void removeRolesFromUser(Long userId, List<Long> roleIds);

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    boolean isUsernameExists(String username, Long excludeId);

    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    boolean isEmailExists(String email, Long excludeId);

    /**
     * 检查手机号是否存在
     *
     * @param phone 手机号
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    boolean isPhoneExists(String phone, Long excludeId);

    /**
     * 更新用户最后登录信息
     *
     * @param userId 用户ID
     * @param loginIp 登录IP
     */
    void updateLastLoginInfo(Long userId, String loginIp);

    /**
     * 验证用户密码
     *
     * @param userId 用户ID
     * @param password 密码
     * @return 是否验证通过
     */
    boolean validatePassword(Long userId, String password);
}
