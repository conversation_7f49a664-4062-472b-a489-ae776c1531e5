package com.bdyl.erp.pisp.user.service.impl;

import java.time.Duration;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.bdyl.erp.pisp.common.security.context.UserContextHolder;
import com.bdyl.erp.pisp.common.security.jwt.JwtTokenUtil;
import com.bdyl.erp.pisp.common.security.user.PispUserDetails;
import com.bdyl.erp.pisp.user.dto.request.LoginRequest;
import com.bdyl.erp.pisp.user.dto.request.RefreshTokenRequest;
import com.bdyl.erp.pisp.user.dto.response.LoginResponse;
import com.bdyl.erp.pisp.user.entity.User;
import com.bdyl.erp.pisp.user.service.AuthService;
import com.bdyl.erp.pisp.user.service.UserService;

/**
 * 认证服务实现类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {

    /**
     * 认证管理器
     */
    private final AuthenticationManager authenticationManager;

    /**
     * JWT令牌工具
     */
    private final JwtTokenUtil jwtTokenUtil;

    /**
     * 用户服务
     */
    private final UserService userService;

    /**
     * Redis模板
     */
    private final RedisTemplate<String, Object> redisTemplate;

    /**
     * JWT过期时间（秒）
     */
    @Value("${pisp.security.jwt.expiration:86400}")
    private Long jwtExpiration;

    @Override
    public LoginResponse login(LoginRequest request) {
        log.info("用户登录: username={}, ip={}", request.getUsername(), request.getLoginIp());

        try {
            // 1. 认证用户
            Authentication authentication = authenticationManager
                .authenticate(new UsernamePasswordAuthenticationToken(request.getUsername(), request.getPassword()));

            PispUserDetails userDetails = (PispUserDetails) authentication.getPrincipal();

            // 2. 更新登录信息
            userService.updateLastLoginInfo(userDetails.getUserId(), request.getLoginIp());

            // 3. 生成令牌
            String accessToken = jwtTokenUtil.generateAccessToken(userDetails);
            String refreshToken = jwtTokenUtil.generateRefreshToken(userDetails);

            // 4. 缓存用户会话信息
            cacheUserSession(userDetails, accessToken, request.getRememberMe());

            // 5. 构建响应
            LoginResponse response = buildLoginResponse(userDetails, accessToken, refreshToken);

            log.info("用户登录成功: username={}, userId={}", request.getUsername(), userDetails.getUserId());
            return response;

        } catch (Exception e) {
            log.error("用户登录失败: username={}, error={}", request.getUsername(), e.getMessage());
            throw new BadCredentialsException("用户名或密码错误");
        }
    }

    @Override
    public void logout(Long userId) {
        log.info("用户注销: userId={}", userId);

        try {
            // 清除Redis中的用户会话信息
            String sessionKey = "user:session:" + userId;
            redisTemplate.delete(sessionKey);

            // 清除当前线程的用户上下文
            UserContextHolder.clear();
            SecurityContextHolder.clearContext();

            log.info("用户注销成功: userId={}", userId);
        } catch (Exception e) {
            log.error("用户注销失败: userId={}, error={}", userId, e.getMessage());
        }
    }

    @Override
    public LoginResponse refreshToken(RefreshTokenRequest request) {
        log.debug("刷新令牌: refreshToken={}", request.getRefreshToken());

        try {
            // 1. 验证刷新令牌
            if (!jwtTokenUtil.validateToken(request.getRefreshToken())) {
                throw new BadCredentialsException("刷新令牌无效");
            }

            // 2. 从刷新令牌中获取用户信息
            PispUserDetails userDetails = jwtTokenUtil.getUserDetailsFromToken(request.getRefreshToken());
            if (userDetails == null) {
                throw new BadCredentialsException("无法从刷新令牌中获取用户信息");
            }

            // 3. 重新加载用户信息（确保用户状态是最新的）
            User user = userService.getUserByUsername(userDetails.getUsername());
            if (user == null) {
                throw new BadCredentialsException("用户不存在");
            }

            // 4. 生成新的访问令牌
            String newAccessToken = jwtTokenUtil.generateAccessToken(userDetails);
            String newRefreshToken = jwtTokenUtil.generateRefreshToken(userDetails);

            // 5. 更新缓存
            cacheUserSession(userDetails, newAccessToken, false);

            // 6. 构建响应
            LoginResponse response = buildLoginResponse(userDetails, newAccessToken, newRefreshToken);

            log.debug("令牌刷新成功: userId={}", userDetails.getUserId());
            return response;

        } catch (Exception e) {
            log.error("令牌刷新失败: error={}", e.getMessage());
            throw new BadCredentialsException("令牌刷新失败");
        }
    }

    @Override
    public boolean validateToken(String token) {
        if (!StringUtils.hasText(token)) {
            return false;
        }
        return jwtTokenUtil.validateToken(token);
    }

    @Override
    public LoginResponse.UserInfo getCurrentUserInfo() {
        // 从Spring Security上下文获取当前用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !(authentication.getPrincipal() instanceof PispUserDetails)) {
            return null;
        }

        PispUserDetails userDetails = (PispUserDetails) authentication.getPrincipal();

        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo();
        userInfo.setUserId(userDetails.getUserId());
        userInfo.setUsername(userDetails.getUsername());
        userInfo.setRealName(userDetails.getRealName());
        userInfo.setEmail(userDetails.getEmail());
        userInfo.setPhone(userDetails.getPhone());
        userInfo.setDepartmentId(userDetails.getDepartmentId());
        userInfo.setTenantId(userDetails.getTenantId());
        userInfo.setIsAdmin(userDetails.getIsAdmin());

        return userInfo;
    }

    /**
     * 缓存用户会话信息
     *
     * @param userDetails 用户详情
     * @param accessToken 访问令牌
     * @param rememberMe 是否记住我
     */
    private void cacheUserSession(PispUserDetails userDetails, String accessToken, Boolean rememberMe) {
        String sessionKey = "user:session:" + userDetails.getUserId();

        // 设置缓存时间：记住我为7天，否则为令牌过期时间
        Duration expiration = Boolean.TRUE.equals(rememberMe) ? Duration.ofDays(7) : Duration.ofSeconds(jwtExpiration);

        redisTemplate.opsForValue().set(sessionKey, userDetails, expiration);

        // 同时缓存令牌到用户ID的映射
        String tokenKey = "token:user:" + accessToken;
        redisTemplate.opsForValue().set(tokenKey, userDetails.getUserId(), expiration);
    }

    /**
     * 构建登录响应
     *
     * @param userDetails 用户详情
     * @param accessToken 访问令牌
     * @param refreshToken 刷新令牌
     * @return 登录响应
     */
    private LoginResponse buildLoginResponse(PispUserDetails userDetails, String accessToken, String refreshToken) {
        LoginResponse response = new LoginResponse();
        response.setAccessToken(accessToken);
        response.setRefreshToken(refreshToken);
        response.setExpiresIn(jwtExpiration);
        response.setRoles(userDetails.getRoles());
        response.setPermissions(userDetails.getPermissions());

        // 构建用户信息
        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo();
        userInfo.setUserId(userDetails.getUserId());
        userInfo.setUsername(userDetails.getUsername());
        userInfo.setRealName(userDetails.getRealName());
        userInfo.setEmail(userDetails.getEmail());
        userInfo.setPhone(userDetails.getPhone());
        userInfo.setDepartmentId(userDetails.getDepartmentId());
        userInfo.setTenantId(userDetails.getTenantId());
        userInfo.setIsAdmin(userDetails.getIsAdmin());

        response.setUserInfo(userInfo);
        return response;
    }
}
