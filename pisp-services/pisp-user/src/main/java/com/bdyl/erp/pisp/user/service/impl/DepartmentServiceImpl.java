package com.bdyl.erp.pisp.user.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.bdyl.erp.pisp.common.core.exception.BusinessException;
import com.bdyl.erp.pisp.common.core.result.ResponseCode;
import com.bdyl.erp.pisp.common.security.context.UserContextHolder;
import com.bdyl.erp.pisp.user.dto.request.DepartmentCreateRequest;
import com.bdyl.erp.pisp.user.dto.request.DepartmentQueryRequest;
import com.bdyl.erp.pisp.user.dto.request.DepartmentUpdateRequest;
import com.bdyl.erp.pisp.user.dto.response.DepartmentResponse;
import com.bdyl.erp.pisp.user.entity.Department;
import com.bdyl.erp.pisp.user.enums.DepartmentStatus;
import com.bdyl.erp.pisp.user.mapper.DepartmentMapper;
import com.bdyl.erp.pisp.user.service.DepartmentService;

/**
 * 部门服务实现类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Service
public class DepartmentServiceImpl extends ServiceImpl<DepartmentMapper, Department> implements DepartmentService {

    /**
     * 部门数据访问层
     */
    @Autowired
    private DepartmentMapper departmentMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DepartmentResponse createDepartment(DepartmentCreateRequest request) {
        log.info("创建部门: deptName={}, deptCode={}", request.getDeptName(), request.getDeptCode());

        // 1. 验证部门代码唯一性
        if (isDeptCodeExists(request.getDeptCode(), null)) {
            throw new BusinessException(ResponseCode.DEPARTMENT_CODE_EXISTS);
        }

        // 2. 验证部门名称在同级下的唯一性
        if (isDeptNameExists(request.getDeptName(), request.getParentId(), null)) {
            throw new BusinessException(ResponseCode.DEPARTMENT_NAME_EXISTS);
        }

        // 3. 验证父部门存在性
        if (request.getParentId() != null) {
            Department parentDept = getById(request.getParentId());
            if (parentDept == null) {
                throw new BusinessException(ResponseCode.PARENT_DEPARTMENT_NOT_FOUND);
            }
            if (!parentDept.isActive()) {
                throw new BusinessException(ResponseCode.PARENT_DEPARTMENT_INACTIVE);
            }
        }

        // 4. 创建部门实体
        Department department = new Department();
        BeanUtils.copyProperties(request, department);

        // 5. 设置部门层级和路径
        if (request.getParentId() != null) {
            Department parentDept = getById(request.getParentId());
            department.setDeptLevel((parentDept.getDeptLevel() == null ? 0 : parentDept.getDeptLevel()) + 1);
        } else {
            department.setDeptLevel(1);
        }

        // 6. 保存部门
        save(department);

        // 7. 更新部门路径
        updateDepartmentPath(department.getId());

        log.info("部门创建成功: deptId={}", department.getId());
        return convertToResponse(department);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DepartmentResponse updateDepartment(DepartmentUpdateRequest request) {
        log.info("更新部门: deptId={}", request.getId());

        // 1. 查询部门
        Department existingDept = getById(request.getId());
        if (existingDept == null) {
            throw new BusinessException(ResponseCode.DEPARTMENT_NOT_FOUND);
        }

        // 2. 验证部门名称在同级下的唯一性
        if (StringUtils.hasText(request.getDeptName()) && isDeptNameExists(request.getDeptName(),
            request.getParentId() != null ? request.getParentId() : existingDept.getParentId(), request.getId())) {
            throw new BusinessException(ResponseCode.DEPARTMENT_NAME_EXISTS);
        }

        // 3. 验证父部门变更（不能移动到自己的子部门）
        if (request.getParentId() != null && !request.getParentId().equals(existingDept.getParentId())) {
            List<Long> childIds = getChildDepartmentIds(request.getId());
            if (childIds.contains(request.getParentId())) {
                throw new BusinessException(ResponseCode.CANNOT_MOVE_TO_CHILD_DEPARTMENT);
            }
        }

        // 4. 更新部门信息
        BeanUtils.copyProperties(request, existingDept, "id", "deptCode", "createTime", "creatorId");
        existingDept.setUpdaterId(getCurrentUserId());
        updateById(existingDept);

        // 5. 如果父部门改变，需要更新层级和路径
        if (request.getParentId() != null && !request.getParentId().equals(existingDept.getParentId())) {
            updateDepartmentHierarchy(existingDept.getId());
        }

        log.info("部门更新成功: deptId={}", request.getId());
        return convertToResponse(existingDept);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDepartment(Long deptId) {
        log.info("删除部门: deptId={}", deptId);

        // 1. 验证部门存在
        Department department = getById(deptId);
        if (department == null) {
            throw new BusinessException(ResponseCode.DEPARTMENT_NOT_FOUND);
        }

        // 2. 检查是否有子部门
        List<Department> childDepts = departmentMapper.selectByParentId(deptId);
        if (!CollectionUtils.isEmpty(childDepts)) {
            throw new BusinessException(ResponseCode.DEPARTMENT_HAS_CHILDREN);
        }

        // 3. 检查是否有员工
        int employeeCount = departmentMapper.countEmployeesByDeptId(deptId);
        if (employeeCount > 0) {
            throw new BusinessException(ResponseCode.DEPARTMENT_HAS_EMPLOYEES);
        }

        // 4. 删除部门
        removeById(deptId);

        log.info("部门删除成功: deptId={}", deptId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteDepartments(List<Long> deptIds) {
        if (CollectionUtils.isEmpty(deptIds)) {
            return;
        }

        log.info("批量删除部门: deptIds={}", deptIds);

        for (Long deptId : deptIds) {
            deleteDepartment(deptId);
        }

        log.info("批量删除部门完成: count={}", deptIds.size());
    }

    @Override
    public DepartmentResponse getDepartmentById(Long deptId) {
        Department department = departmentMapper.selectByIdWithLeader(deptId);
        if (department == null) {
            throw new BusinessException(ResponseCode.DEPARTMENT_NOT_FOUND);
        }
        return convertToResponse(department);
    }

    @Override
    public Department getByDeptCode(String deptCode) {
        return departmentMapper.selectByDeptCode(deptCode);
    }

    @Override
    public IPage<DepartmentResponse> getDepartmentPage(DepartmentQueryRequest request) {
        Page<Department> page = new Page<>(request.getPageNum(), request.getPageSize());

        String status = request.getStatus() != null ? request.getStatus().getCode() : null;
        IPage<Department> deptPage = departmentMapper.selectDepartmentPageWithLeader(page, request.getDeptName(),
            request.getDeptCode(), request.getParentId(), status);

        return deptPage.convert(this::convertToResponse);
    }

    @Override
    public List<DepartmentResponse> getDepartmentTree() {
        List<Department> allDepts = departmentMapper.selectDepartmentTree();
        return buildDepartmentTree(allDepts, null);
    }

    @Override
    public List<DepartmentResponse> getDepartmentTreeWithEmployeeCount() {
        List<Department> allDepts = departmentMapper.selectDepartmentTreeWithEmployeeCount();
        return buildDepartmentTree(allDepts, null);
    }

    @Override
    public List<DepartmentResponse> getChildDepartments(Long parentId) {
        List<Department> childDepts = departmentMapper.selectByParentId(parentId);
        return childDepts.stream().map(this::convertToResponse).collect(Collectors.toList());
    }

    @Override
    public List<DepartmentResponse> getActiveDepartments() {
        List<Department> activeDepts = departmentMapper.selectActiveDepartments();
        return activeDepts.stream().map(this::convertToResponse).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void activateDepartment(Long deptId) {
        log.info("启用部门: deptId={}", deptId);
        updateDepartmentStatus(deptId, DepartmentStatus.ACTIVE);
        log.info("部门启用成功: deptId={}", deptId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deactivateDepartment(Long deptId) {
        log.info("禁用部门: deptId={}", deptId);
        updateDepartmentStatus(deptId, DepartmentStatus.INACTIVE);
        log.info("部门禁用成功: deptId={}", deptId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateDepartmentStatus(List<Long> deptIds, String status) {
        if (CollectionUtils.isEmpty(deptIds)) {
            return;
        }

        log.info("批量更新部门状态: deptIds={}, status={}", deptIds, status);
        departmentMapper.batchUpdateStatus(deptIds, status, getCurrentUserId());
        log.info("批量更新部门状态完成: count={}", deptIds.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setDepartmentLeader(Long deptId, Long leaderId) {
        log.info("设置部门负责人: deptId={}, leaderId={}", deptId, leaderId);

        Department department = getById(deptId);
        if (department == null) {
            throw new BusinessException(ResponseCode.DEPARTMENT_NOT_FOUND);
        }

        department.setLeaderId(leaderId);
        department.setUpdaterId(getCurrentUserId());
        updateById(department);

        log.info("部门负责人设置成功: deptId={}, leaderId={}", deptId, leaderId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void moveDepartment(Long deptId, Long newParentId) {
        log.info("移动部门: deptId={}, newParentId={}", deptId, newParentId);

        Department department = getById(deptId);
        if (department == null) {
            throw new BusinessException(ResponseCode.DEPARTMENT_NOT_FOUND);
        }

        // 验证不能移动到自己的子部门
        if (newParentId != null) {
            List<Long> childIds = getChildDepartmentIds(deptId);
            if (childIds.contains(newParentId)) {
                throw new BusinessException(ResponseCode.CANNOT_MOVE_TO_CHILD_DEPARTMENT);
            }
        }

        department.setParentId(newParentId);
        department.setUpdaterId(getCurrentUserId());
        updateById(department);

        // 更新层级和路径
        updateDepartmentHierarchy(deptId);

        log.info("部门移动成功: deptId={}, newParentId={}", deptId, newParentId);
    }

    @Override
    public boolean isDeptCodeExists(String deptCode, Long excludeId) {
        return departmentMapper.checkDeptCodeExists(deptCode, excludeId) > 0;
    }

    @Override
    public boolean isDeptNameExists(String deptName, Long parentId, Long excludeId) {
        return departmentMapper.checkDeptNameExists(deptName, parentId, excludeId) > 0;
    }

    @Override
    public int getDepartmentEmployeeCount(Long deptId) {
        return departmentMapper.countEmployeesByDeptId(deptId);
    }

    @Override
    public boolean existsById(Long departmentId) {
        return getById(departmentId) != null;
    }

    @Override
    public String getDepartmentPath(Long deptId) {
        Department department = getById(deptId);
        if (department == null) {
            return "";
        }

        List<String> pathNames = new ArrayList<>();
        buildDepartmentPathNames(deptId, pathNames);

        // 反转列表，使其从根部门开始
        List<String> reversedPath = new ArrayList<>();
        for (int i = pathNames.size() - 1; i >= 0; i--) {
            reversedPath.add(pathNames.get(i));
        }

        return String.join("/", reversedPath);
    }

    @Override
    public List<Long> getChildDepartmentIds(Long deptId) {
        return departmentMapper.selectChildDeptIds(deptId);
    }

    // 私有方法

    /**
     * 更新部门状态
     */
    private void updateDepartmentStatus(Long deptId, DepartmentStatus status) {
        Department department = getById(deptId);
        if (department == null) {
            throw new BusinessException(ResponseCode.DEPARTMENT_NOT_FOUND);
        }

        department.setStatus(status);
        department.setUpdaterId(getCurrentUserId());
        updateById(department);
    }

    /**
     * 更新部门路径
     */
    private void updateDepartmentPath(Long deptId) {
        String path = generateDepartmentPath(deptId);
        departmentMapper.updateDeptPath(deptId, path);
    }

    /**
     * 生成部门路径
     */
    private String generateDepartmentPath(Long deptId) {
        List<Long> pathIds = new ArrayList<>();
        buildDepartmentPathIds(deptId, pathIds);

        // 反转列表，使其从根部门开始
        List<Long> reversedPath = new ArrayList<>();
        for (int i = pathIds.size() - 1; i >= 0; i--) {
            reversedPath.add(pathIds.get(i));
        }

        return "/" + reversedPath.stream().map(String::valueOf).collect(Collectors.joining("/"));
    }

    /**
     * 递归构建部门路径ID
     */
    private void buildDepartmentPathIds(Long deptId, List<Long> pathIds) {
        Department department = getById(deptId);
        if (department != null) {
            pathIds.add(deptId);
            if (department.getParentId() != null) {
                buildDepartmentPathIds(department.getParentId(), pathIds);
            }
        }
    }

    /**
     * 递归构建部门路径名称
     */
    private void buildDepartmentPathNames(Long deptId, List<String> pathNames) {
        Department department = getById(deptId);
        if (department != null) {
            pathNames.add(department.getDeptName());
            if (department.getParentId() != null) {
                buildDepartmentPathNames(department.getParentId(), pathNames);
            }
        }
    }

    /**
     * 更新部门层级结构
     */
    private void updateDepartmentHierarchy(Long deptId) {
        Department department = getById(deptId);
        if (department == null) {
            return;
        }

        // 更新当前部门的层级
        if (department.getParentId() != null) {
            Department parentDept = getById(department.getParentId());
            department.setDeptLevel((parentDept.getDeptLevel() == null ? 0 : parentDept.getDeptLevel()) + 1);
        } else {
            department.setDeptLevel(1);
        }
        updateById(department);

        // 更新路径
        updateDepartmentPath(deptId);

        // 递归更新所有子部门
        List<Department> childDepts = departmentMapper.selectByParentId(deptId);
        for (Department childDept : childDepts) {
            updateDepartmentHierarchy(childDept.getId());
        }
    }

    /**
     * 构建部门树
     */
    private List<DepartmentResponse> buildDepartmentTree(List<Department> allDepts, Long parentId) {
        List<DepartmentResponse> tree = new ArrayList<>();

        for (Department dept : allDepts) {
            if ((parentId == null && dept.getParentId() == null)
                || (parentId != null && parentId.equals(dept.getParentId()))) {

                DepartmentResponse deptResponse = convertToResponse(dept);
                deptResponse.setChildren(buildDepartmentTree(allDepts, dept.getId()));
                tree.add(deptResponse);
            }
        }

        return tree;
    }

    /**
     * 转换为响应DTO
     */
    private DepartmentResponse convertToResponse(Department department) {
        DepartmentResponse response = new DepartmentResponse();
        BeanUtils.copyProperties(department, response);

        // 设置负责人信息
        if (department.getLeader() != null) {
            response.setLeaderName(department.getLeader().getRealName());
        }

        // 设置员工数量
        response.setEmployeeCount(department.getEmployeeCount());

        return response;
    }

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId() {
        var context = UserContextHolder.getContext();
        if (context == null) {
            throw new BusinessException(ResponseCode.UNAUTHORIZED);
        }
        return context.getUserId();
    }
}
