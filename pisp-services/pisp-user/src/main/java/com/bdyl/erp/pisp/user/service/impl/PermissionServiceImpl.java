package com.bdyl.erp.pisp.user.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.bdyl.erp.pisp.common.core.exception.BusinessException;
import com.bdyl.erp.pisp.common.core.result.ResponseCode;
import com.bdyl.erp.pisp.common.security.context.UserContextHolder;
import com.bdyl.erp.pisp.common.web.result.PageResult;
import com.bdyl.erp.pisp.user.dto.request.PermissionCreateRequest;
import com.bdyl.erp.pisp.user.dto.request.PermissionQueryRequest;
import com.bdyl.erp.pisp.user.dto.request.PermissionUpdateRequest;
import com.bdyl.erp.pisp.user.dto.response.PermissionResponse;
import com.bdyl.erp.pisp.user.entity.Permission;
import com.bdyl.erp.pisp.user.mapper.PermissionMapper;
import com.bdyl.erp.pisp.user.mapper.RolePermissionMapper;
import com.bdyl.erp.pisp.user.service.PermissionService;

/**
 * 权限服务实现类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PermissionServiceImpl extends ServiceImpl<PermissionMapper, Permission> implements PermissionService {

    /**
     * 权限Mapper
     */
    private final PermissionMapper permissionMapper;

    /**
     * 角色权限Mapper
     */
    private final RolePermissionMapper rolePermissionMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PermissionResponse createPermission(PermissionCreateRequest request) {
        log.info("创建权限: permissionName={}, permissionCode={}", request.getPermissionName(),
            request.getPermissionCode());

        // 验证权限代码唯一性
        if (isPermissionCodeExists(request.getPermissionCode(), null)) {
            throw new BusinessException(ResponseCode.PERMISSION_CODE_EXISTS);
        }

        // 验证权限名称在同级下唯一性
        if (isPermissionNameExists(request.getPermissionName(), request.getParentId(), null)) {
            throw new BusinessException(ResponseCode.PERMISSION_NAME_EXISTS);
        }

        // 验证父权限是否存在
        if (request.getParentId() != null) {
            Permission parentPermission = getById(request.getParentId());
            if (parentPermission == null) {
                throw new BusinessException(ResponseCode.PARENT_PERMISSION_NOT_FOUND);
            }
        }

        // 创建权限实体
        Permission permission = new Permission();
        BeanUtils.copyProperties(request, permission);

        // 设置创建信息
        permission.setCreatorId(getCurrentUserId());
        permission.setUpdaterId(getCurrentUserId());

        // 保存权限
        save(permission);

        log.info("权限创建成功: permissionId={}", permission.getId());
        return convertToResponse(permission);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PermissionResponse updatePermission(PermissionUpdateRequest request) {
        log.info("更新权限: permissionId={}, permissionName={}", request.getId(), request.getPermissionName());

        // 检查权限是否存在
        Permission existingPermission = getById(request.getId());
        if (existingPermission == null) {
            throw new BusinessException(ResponseCode.PERMISSION_NOT_FOUND);
        }

        // 检查是否为系统权限
        if (Boolean.TRUE.equals(existingPermission.getIsSystem())) {
            throw new BusinessException(ResponseCode.SYSTEM_PERMISSION_CANNOT_MODIFY);
        }

        // 验证权限代码唯一性
        if (isPermissionCodeExists(request.getPermissionCode(), request.getId())) {
            throw new BusinessException(ResponseCode.PERMISSION_CODE_EXISTS);
        }

        // 验证权限名称在同级下唯一性
        if (isPermissionNameExists(request.getPermissionName(), request.getParentId(), request.getId())) {
            throw new BusinessException(ResponseCode.PERMISSION_NAME_EXISTS);
        }

        // 验证父权限是否存在
        if (request.getParentId() != null) {
            Permission parentPermission = getById(request.getParentId());
            if (parentPermission == null) {
                throw new BusinessException(ResponseCode.PARENT_PERMISSION_NOT_FOUND);
            }

            // 检查是否会形成循环引用
            if (isCircularReference(request.getId(), request.getParentId())) {
                throw new BusinessException(ResponseCode.CIRCULAR_REFERENCE_NOT_ALLOWED);
            }
        }

        // 更新权限信息
        BeanUtils.copyProperties(request, existingPermission);
        existingPermission.setUpdaterId(getCurrentUserId());

        // 保存更新
        updateById(existingPermission);

        log.info("权限更新成功: permissionId={}", request.getId());
        return convertToResponse(existingPermission);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePermission(Long permissionId) {
        log.info("删除权限: permissionId={}", permissionId);

        // 检查权限是否存在
        Permission permission = getById(permissionId);
        if (permission == null) {
            throw new BusinessException(ResponseCode.PERMISSION_NOT_FOUND);
        }

        // 检查是否为系统权限
        if (Boolean.TRUE.equals(permission.getIsSystem())) {
            throw new BusinessException(ResponseCode.SYSTEM_PERMISSION_CANNOT_DELETE);
        }

        // 检查是否有子权限
        if (hasChildPermissions(permissionId)) {
            throw new BusinessException(ResponseCode.PERMISSION_HAS_CHILDREN);
        }

        // 检查是否被角色使用
        if (isPermissionInUse(permissionId)) {
            throw new BusinessException(ResponseCode.PERMISSION_IN_USE);
        }

        // 删除权限
        removeById(permissionId);

        log.info("权限删除成功: permissionId={}", permissionId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeletePermissions(List<Long> permissionIds) {
        if (CollectionUtils.isEmpty(permissionIds)) {
            return;
        }

        log.info("批量删除权限: permissionIds={}", permissionIds);

        for (Long permissionId : permissionIds) {
            deletePermission(permissionId);
        }

        log.info("批量删除权限完成: count={}", permissionIds.size());
    }

    @Override
    public PermissionResponse getPermissionById(Long permissionId) {
        Permission permission = getById(permissionId);
        if (permission == null) {
            throw new BusinessException(ResponseCode.PERMISSION_NOT_FOUND);
        }
        return convertToResponse(permission);
    }

    @Override
    public PermissionResponse getPermissionByCode(String permissionCode) {
        Permission permission = permissionMapper.selectByPermissionCode(permissionCode);
        if (permission == null) {
            throw new BusinessException(ResponseCode.PERMISSION_NOT_FOUND);
        }
        return convertToResponse(permission);
    }

    @Override
    public PageResult<PermissionResponse> getPermissionPage(PermissionQueryRequest request) {
        Page<Permission> page = Page.of(request.getPageNum(), request.getPageSize());

        LambdaQueryWrapper<Permission> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
            .like(request.getPermissionName() != null, Permission::getPermissionName, request.getPermissionName())
            .like(request.getPermissionCode() != null, Permission::getPermissionCode, request.getPermissionCode())
            .eq(request.getPermissionType() != null, Permission::getPermissionType, request.getPermissionType())
            .eq(request.getParentId() != null, Permission::getParentId, request.getParentId())
            .eq(request.getIsSystem() != null, Permission::getIsSystem, request.getIsSystem())
            .orderByAsc(Permission::getSortOrder, Permission::getCreateTime);

        IPage<Permission> permissionPage = page(page, queryWrapper);

        List<PermissionResponse> responseList =
            permissionPage.getRecords().stream().map(this::convertToResponse).collect(Collectors.toList());

        return PageResult.of(responseList, permissionPage.getTotal(), permissionPage.getCurrent(),
            permissionPage.getSize());
    }

    @Override
    public List<PermissionResponse> getPermissionTree() {
        List<Permission> allPermissions = permissionMapper.selectPermissionTree();
        return buildPermissionTree(allPermissions);
    }

    @Override
    public List<PermissionResponse> getChildPermissions(Long parentId) {
        List<Permission> childPermissions = permissionMapper.selectByParentId(parentId);
        return childPermissions.stream().map(this::convertToResponse).collect(Collectors.toList());
    }

    @Override
    public List<PermissionResponse> getPermissionsByRoleId(Long roleId) {
        List<Permission> permissions = permissionMapper.selectByRoleId(roleId);
        return permissions.stream().map(this::convertToResponse).collect(Collectors.toList());
    }

    @Override
    public List<PermissionResponse> getPermissionsByUserId(Long userId) {
        List<Permission> permissions = permissionMapper.selectByUserId(userId);
        return permissions.stream().map(this::convertToResponse).collect(Collectors.toList());
    }

    @Override
    public boolean isPermissionCodeExists(String permissionCode, Long excludeId) {
        return permissionMapper.checkPermissionCodeExists(permissionCode, excludeId);
    }

    @Override
    public boolean isPermissionNameExists(String permissionName, Long parentId, Long excludeId) {
        return permissionMapper.checkPermissionNameExists(permissionName, parentId, excludeId);
    }

    @Override
    public boolean hasChildPermissions(Long permissionId) {
        return permissionMapper.selectByParentId(permissionId).size() > 0;
    }

    @Override
    public boolean isPermissionInUse(Long permissionId) {
        return rolePermissionMapper.selectByPermissionId(permissionId).size() > 0;
    }

    @Override
    public int getPermissionRoleCount(Long permissionId) {
        return rolePermissionMapper.selectByPermissionId(permissionId).size();
    }

    /**
     * 构建权限树结构
     *
     * @param permissions 权限列表
     * @return 权限树
     */
    private List<PermissionResponse> buildPermissionTree(List<Permission> permissions) {
        if (CollectionUtils.isEmpty(permissions)) {
            return new ArrayList<>();
        }

        // 转换为响应对象并按父权限ID分组
        Map<Long, List<PermissionResponse>> permissionMap = new HashMap<>();
        List<PermissionResponse> rootPermissions = new ArrayList<>();

        for (Permission permission : permissions) {
            PermissionResponse response = convertToResponse(permission);

            if (permission.getParentId() == null) {
                rootPermissions.add(response);
            } else {
                permissionMap.computeIfAbsent(permission.getParentId(), k -> new ArrayList<>()).add(response);
            }
        }

        // 递归构建树结构
        buildTree(rootPermissions, permissionMap);

        return rootPermissions;
    }

    /**
     * 递归构建树结构
     *
     * @param permissions 当前层级权限列表
     * @param permissionMap 权限映射
     */
    private void buildTree(List<PermissionResponse> permissions, Map<Long, List<PermissionResponse>> permissionMap) {
        for (PermissionResponse permission : permissions) {
            List<PermissionResponse> children = permissionMap.get(permission.getId());
            if (!CollectionUtils.isEmpty(children)) {
                permission.setChildren(children);
                buildTree(children, permissionMap);
            }
        }
    }

    /**
     * 检查是否会形成循环引用
     *
     * @param permissionId 权限ID
     * @param parentId 父权限ID
     * @return 是否循环引用
     */
    private boolean isCircularReference(Long permissionId, Long parentId) {
        if (parentId == null || permissionId.equals(parentId)) {
            return true;
        }

        Permission parentPermission = getById(parentId);
        if (parentPermission == null) {
            return false;
        }

        // 递归检查父权限链
        return isCircularReference(permissionId, parentPermission.getParentId());
    }

    /**
     * 转换为响应DTO
     *
     * @param permission 权限实体
     * @return 权限响应DTO
     */
    private PermissionResponse convertToResponse(Permission permission) {
        PermissionResponse response = new PermissionResponse();
        BeanUtils.copyProperties(permission, response);

        // 设置角色数量
        response.setRoleCount(getPermissionRoleCount(permission.getId()));

        return response;
    }

    /**
     * 获取当前用户ID
     *
     * @return 用户ID
     */
    private Long getCurrentUserId() {
        return UserContextHolder.getContext().getUserId();
    }
}
