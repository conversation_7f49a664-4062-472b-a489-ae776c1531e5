package com.bdyl.erp.pisp.user.service.impl;

import java.util.HashSet;
import java.util.Set;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import com.bdyl.erp.pisp.common.security.user.PispUserDetails;
import com.bdyl.erp.pisp.user.entity.User;
import com.bdyl.erp.pisp.user.enums.UserStatus;
import com.bdyl.erp.pisp.user.service.UserService;

/**
 * PISP用户详情服务实现类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PispUserDetailsServiceImpl implements UserDetailsService {
    /**
     * 用户服务
     */

    private final UserService userService;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        log.debug("加载用户详情: username={}", username);

        // 根据用户名查询用户信息（包含角色信息）
        User user = userService.getUserByUsername(username);
        if (user == null) {
            log.warn("用户不存在: username={}", username);
            throw new UsernameNotFoundException("用户不存在: " + username);
        }

        // 检查用户状态
        if (user.getStatus() != UserStatus.ACTIVE) {
            log.warn("用户状态异常: username={}, status={}", username, user.getStatus());
            throw new UsernameNotFoundException("用户状态异常: " + username);
        }

        // 构建用户详情
        PispUserDetails userDetails = buildUserDetails(user);

        log.debug("用户详情加载成功: username={}, userId={}", username, user.getId());
        return userDetails;
    }

    /**
     * 构建用户详情
     *
     * @param user 用户实体
     * @return 用户详情
     */
    private PispUserDetails buildUserDetails(User user) {
        PispUserDetails userDetails = new PispUserDetails();

        // 基本信息
        userDetails.setUserId(user.getId());
        userDetails.setUsername(user.getUsername());
        userDetails.setPassword(user.getPassword());
        userDetails.setRealName(user.getRealName());
        userDetails.setEmail(user.getEmail());
        userDetails.setPhone(user.getPhone());

        // 组织信息
        userDetails.setDepartmentId(user.getDepartmentId());
        userDetails.setTenantId(null); // TODO: 从用户配置或部门获取租户ID

        // 状态信息
        userDetails.setStatus(user.getStatus().getCode().equals("ACTIVE") ? 1 : 0);
        userDetails.setIsAdmin(false); // TODO: 从角色判断是否为管理员

        // 角色和权限信息
        // TODO: 从角色服务获取用户角色和权限
        userDetails.setRoles(getUserRoles(user.getId()));
        userDetails.setPermissions(getUserPermissions(user.getId()));

        return userDetails;
    }

    /**
     * 获取用户角色
     *
     * @param userId 用户ID
     * @return 角色集合
     */
    private Set<String> getUserRoles(Long userId) {
        try {
            // 从用户角色关联表查询用户的角色信息
            // TODO: 注入RoleMapper或RoleService来获取真实角色数据

            // 临时实现: 检查是否为系统管理员
            if (userId.equals(1L)) {
                return Set.of("ADMIN", "USER");
            }

            // 普通用户默认角色
            return Set.of("USER");
        } catch (Exception e) {
            log.error("获取用户角色失败: userId={}", userId, e);
            return Set.of("USER");
        }
    }

    /**
     * 获取用户权限
     *
     * @param userId 用户ID
     * @return 权限集合
     */
    private Set<String> getUserPermissions(Long userId) {
        try {
            // 从角色权限关联表查询用户的权限信息
            // TODO: 注入PermissionMapper来获取真实权限数据

            Set<String> permissions = new HashSet<>();

            // 根据用户角色获取权限
            Set<String> roles = getUserRoles(userId);

            if (roles.contains("ADMIN")) {
                // 管理员拥有所有权限
                permissions.addAll(Set.of("user:read", "user:create", "user:update", "user:delete", "role:read",
                    "role:create", "role:update", "role:delete", "permission:read", "permission:create",
                    "permission:update", "permission:delete", "department:read", "department:create",
                    "department:update", "department:delete", "system:manage"));
            } else if (roles.contains("USER")) {
                // 普通用户基本权限
                permissions.addAll(Set.of("user:read", "profile:update"));
            }

            return permissions;
        } catch (Exception e) {
            log.error("获取用户权限失败: userId={}", userId, e);
            return Set.of("user:read");
        }
    }
}
