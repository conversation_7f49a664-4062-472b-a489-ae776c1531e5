package com.bdyl.erp.pisp.user.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.bdyl.erp.pisp.user.entity.RolePermission;
import com.bdyl.erp.pisp.user.mapper.RolePermissionMapper;
import com.bdyl.erp.pisp.user.service.RolePermissionService;

/**
 * 角色权限关联服务实现类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Service
public class RolePermissionServiceImpl extends ServiceImpl<RolePermissionMapper, RolePermission>
    implements RolePermissionService {

    /**
     * 角色权限关联数据访问层
     */
    @Autowired
    private RolePermissionMapper rolePermissionMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByRoleId(Long roleId) {
        log.info("删除角色的所有权限关联: roleId={}", roleId);
        rolePermissionMapper.deleteByRoleId(roleId);
        log.info("删除角色权限关联完成: roleId={}", roleId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByPermissionId(Long permissionId) {
        log.info("删除权限的所有角色关联: permissionId={}", permissionId);
        rolePermissionMapper.deleteByPermissionId(permissionId);
        log.info("删除权限角色关联完成: permissionId={}", permissionId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByRoleIdAndPermissionIds(Long roleId, List<Long> permissionIds) {
        if (permissionIds == null || permissionIds.isEmpty()) {
            return;
        }
        log.info("删除角色的指定权限关联: roleId={}, permissionIds={}", roleId, permissionIds);
        rolePermissionMapper.deleteByRoleIdAndPermissionIds(roleId, permissionIds);
        log.info("删除角色指定权限关联完成: roleId={}, count={}", roleId, permissionIds.size());
    }

    @Override
    public List<RolePermission> getByRoleId(Long roleId) {
        return rolePermissionMapper.selectByRoleId(roleId);
    }

    @Override
    public List<RolePermission> getByPermissionId(Long permissionId) {
        return rolePermissionMapper.selectByPermissionId(permissionId);
    }

    @Override
    public boolean exists(Long roleId, Long permissionId) {
        return rolePermissionMapper.checkRolePermissionExists(roleId, permissionId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInsert(List<RolePermission> rolePermissions) {
        if (rolePermissions == null || rolePermissions.isEmpty()) {
            return;
        }
        log.info("批量插入角色权限关联: count={}", rolePermissions.size());
        rolePermissionMapper.batchInsert(rolePermissions);
        log.info("批量插入角色权限关联完成: count={}", rolePermissions.size());
    }
}
