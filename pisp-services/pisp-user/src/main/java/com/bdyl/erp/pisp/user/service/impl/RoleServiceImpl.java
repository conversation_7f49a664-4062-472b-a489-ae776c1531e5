package com.bdyl.erp.pisp.user.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.bdyl.erp.pisp.common.core.exception.BusinessException;
import com.bdyl.erp.pisp.common.core.result.ResponseCode;
import com.bdyl.erp.pisp.common.security.context.UserContextHolder;
import com.bdyl.erp.pisp.user.dto.request.RoleCreateRequest;
import com.bdyl.erp.pisp.user.dto.request.RoleQueryRequest;
import com.bdyl.erp.pisp.user.dto.request.RoleUpdateRequest;
import com.bdyl.erp.pisp.user.dto.response.RoleResponse;
import com.bdyl.erp.pisp.user.entity.Role;
import com.bdyl.erp.pisp.user.entity.RolePermission;
import com.bdyl.erp.pisp.user.enums.RoleStatus;
import com.bdyl.erp.pisp.user.mapper.RoleMapper;
import com.bdyl.erp.pisp.user.mapper.RolePermissionMapper;
import com.bdyl.erp.pisp.user.mapper.UserRoleMapper;
import com.bdyl.erp.pisp.user.service.RoleService;

/**
 * 角色服务实现类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Service
public class RoleServiceImpl extends ServiceImpl<RoleMapper, Role> implements RoleService {

    /**
     * 角色数据访问层
     */
    @Autowired
    private RoleMapper roleMapper;

    /**
     * 角色权限关联数据访问层
     */
    @Autowired
    private RolePermissionMapper rolePermissionMapper;

    /**
     * 用户角色关联数据访问层
     */
    @Autowired
    private UserRoleMapper userRoleMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RoleResponse createRole(RoleCreateRequest request) {
        log.info("创建角色: roleName={}, roleCode={}", request.getRoleName(), request.getRoleCode());

        // 1. 验证角色代码和名称的唯一性
        if (isRoleCodeExists(request.getRoleCode(), null)) {
            throw new BusinessException(ResponseCode.ROLE_CODE_EXISTS);
        }
        if (isRoleNameExists(request.getRoleName(), null)) {
            throw new BusinessException(ResponseCode.ROLE_NAME_EXISTS);
        }

        // 2. 创建角色实体
        Role role = new Role();
        BeanUtils.copyProperties(request, role);
        role.setIsSystem(false); // 通过API创建的角色都不是系统角色
        role.setCreatorId(getCurrentUserId());

        // 3. 保存角色
        save(role);

        // 4. 分配权限（如果有）
        if (!CollectionUtils.isEmpty(request.getPermissionIds())) {
            assignPermissionsToRole(role.getId(), request.getPermissionIds());
        }

        log.info("角色创建成功: roleId={}", role.getId());
        return convertToResponse(role);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RoleResponse updateRole(RoleUpdateRequest request) {
        log.info("更新角色: roleId={}", request.getId());

        // 1. 查询角色
        Role existingRole = getById(request.getId());
        if (existingRole == null) {
            throw new BusinessException(ResponseCode.ROLE_NOT_FOUND);
        }

        // 2. 检查系统角色
        if (existingRole.isSystemRole()) {
            throw new BusinessException(ResponseCode.SYSTEM_ROLE_CANNOT_MODIFY);
        }

        // 3. 验证角色代码和名称的唯一性
        if (isRoleCodeExists(request.getRoleCode(), request.getId())) {
            throw new BusinessException(ResponseCode.ROLE_CODE_EXISTS);
        }
        if (isRoleNameExists(request.getRoleName(), request.getId())) {
            throw new BusinessException(ResponseCode.ROLE_NAME_EXISTS);
        }

        // 4. 更新角色信息
        BeanUtils.copyProperties(request, existingRole, "id", "isSystem", "createTime", "creatorId");
        existingRole.setUpdaterId(getCurrentUserId());
        updateById(existingRole);

        // 5. 更新权限（如果有）
        if (request.getPermissionIds() != null) {
            assignPermissionsToRole(request.getId(), request.getPermissionIds());
        }

        log.info("角色更新成功: roleId={}", request.getId());
        return convertToResponse(existingRole);
    }

    @Override
    public RoleResponse getRoleById(Long roleId) {
        Role role = roleMapper.selectByIdWithPermissions(roleId);
        if (role == null) {
            throw new BusinessException(ResponseCode.ROLE_NOT_FOUND);
        }
        return convertToResponse(role);
    }

    @Override
    public RoleResponse getRoleByCode(String roleCode) {
        Role role = roleMapper.selectByRoleCode(roleCode);
        if (role == null) {
            throw new BusinessException(ResponseCode.ROLE_NOT_FOUND);
        }
        return convertToResponse(role);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRole(Long roleId) {
        log.info("删除角色: roleId={}", roleId);

        // 1. 查询角色
        Role role = getById(roleId);
        if (role == null) {
            throw new BusinessException(ResponseCode.ROLE_NOT_FOUND);
        }

        // 2. 检查系统角色
        if (role.isSystemRole()) {
            throw new BusinessException(ResponseCode.SYSTEM_ROLE_CANNOT_DELETE);
        }

        // 3. 检查是否有用户关联
        int userCount = userRoleMapper.selectByRoleId(roleId).size();
        if (userCount > 0) {
            throw new BusinessException(ResponseCode.ROLE_HAS_USERS);
        }

        // 4. 删除角色权限关联
        rolePermissionMapper.deleteByRoleId(roleId);

        // 5. 删除角色
        removeById(roleId);

        log.info("角色删除成功: roleId={}", roleId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteRoles(List<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return;
        }

        log.info("批量删除角色: roleIds={}", roleIds);

        for (Long roleId : roleIds) {
            deleteRole(roleId);
        }

        log.info("批量删除角色完成: count={}", roleIds.size());
    }

    @Override
    public IPage<RoleResponse> getRolePage(RoleQueryRequest request) {
        Page<Role> page = new Page<>(request.getPageNum(), request.getPageSize());
        IPage<Role> rolePage = roleMapper.selectRolePage(page, request.getRoleName(), request.getRoleCode(),
            request.getStatus(), request.getIsSystem());

        // 转换为响应DTO
        IPage<RoleResponse> responsePage = new Page<>(request.getPageNum(), request.getPageSize(), rolePage.getTotal());
        List<RoleResponse> responses =
            rolePage.getRecords().stream().map(this::convertToResponse).collect(Collectors.toList());
        responsePage.setRecords(responses);

        return responsePage;
    }

    @Override
    public List<RoleResponse> getActiveRoles() {
        List<Role> roles = roleMapper.selectActiveRoles();
        return roles.stream().map(this::convertToResponse).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void activateRole(Long roleId) {
        log.info("启用角色: roleId={}", roleId);

        Role role = getById(roleId);
        if (role == null) {
            throw new BusinessException(ResponseCode.ROLE_NOT_FOUND);
        }

        role.activate();
        role.setUpdaterId(getCurrentUserId());
        updateById(role);

        log.info("角色启用成功: roleId={}", roleId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deactivateRole(Long roleId) {
        log.info("禁用角色: roleId={}", roleId);

        Role role = getById(roleId);
        if (role == null) {
            throw new BusinessException(ResponseCode.ROLE_NOT_FOUND);
        }

        if (role.isSystemRole()) {
            throw new BusinessException(ResponseCode.SYSTEM_ROLE_CANNOT_DISABLE);
        }

        role.deactivate();
        role.setUpdaterId(getCurrentUserId());
        updateById(role);

        log.info("角色禁用成功: roleId={}", roleId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateRoleStatus(List<Long> roleIds, String status) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return;
        }

        log.info("批量更新角色状态: roleIds={}, status={}", roleIds, status);

        RoleStatus roleStatus = RoleStatus.fromCode(status);
        roleMapper.batchUpdateStatus(roleIds, status, getCurrentUserId());

        log.info("批量更新角色状态完成: count={}", roleIds.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void assignPermissionsToRole(Long roleId, List<Long> permissionIds) {
        if (CollectionUtils.isEmpty(permissionIds)) {
            return;
        }

        log.info("为角色分配权限: roleId={}, permissionIds={}", roleId, permissionIds);

        // 1. 删除现有权限关联
        rolePermissionMapper.deleteByRoleId(roleId);

        // 2. 创建新的权限关联
        List<RolePermission> rolePermissions = permissionIds.stream().map(permissionId -> {
            RolePermission rp = new RolePermission();
            rp.setRoleId(roleId);
            rp.setPermissionId(permissionId);
            rp.setAssignedAt(LocalDateTime.now());
            rp.setAssignedBy(getCurrentUserId());
            return rp;
        }).collect(Collectors.toList());

        // 3. 批量插入
        rolePermissionMapper.batchInsert(rolePermissions);

        log.info("权限分配完成: roleId={}, count={}", roleId, permissionIds.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removePermissionsFromRole(Long roleId, List<Long> permissionIds) {
        if (CollectionUtils.isEmpty(permissionIds)) {
            return;
        }

        log.info("移除角色权限: roleId={}, permissionIds={}", roleId, permissionIds);

        rolePermissionMapper.deleteByRoleIdAndPermissionIds(roleId, permissionIds);

        log.info("权限移除完成: roleId={}, count={}", roleId, permissionIds.size());
    }

    @Override
    public List<RoleResponse> getRolesByUserId(Long userId) {
        List<Role> roles = roleMapper.selectByUserId(userId);
        return roles.stream().map(this::convertToResponse).collect(Collectors.toList());
    }

    @Override
    public boolean isRoleNameExists(String roleName, Long excludeId) {
        return roleMapper.checkRoleNameExists(roleName, excludeId);
    }

    @Override
    public boolean isRoleCodeExists(String roleCode, Long excludeId) {
        return roleMapper.checkRoleCodeExists(roleCode, excludeId);
    }

    @Override
    public int getRoleUserCount(Long roleId) {
        return userRoleMapper.selectByRoleId(roleId).size();
    }

    /**
     * 转换为响应DTO
     *
     * @param role 角色实体
     * @return 角色响应DTO
     */
    private RoleResponse convertToResponse(Role role) {
        RoleResponse response = new RoleResponse();
        BeanUtils.copyProperties(role, response);

        // 设置权限信息
        if (role.getPermissions() != null) {
            response.setPermissionCodes(role.getPermissionCodes());
        }

        // 设置用户数量
        response.setUserCount(userRoleMapper.selectByRoleId(role.getId()).size());

        return response;
    }

    /**
     * 获取当前用户ID
     *
     * @return 用户ID
     */
    private Long getCurrentUserId() {
        var context = UserContextHolder.getContext();
        if (context == null) {
            throw new BusinessException(ResponseCode.UNAUTHORIZED);
        }
        return context.getUserId();
    }
}
