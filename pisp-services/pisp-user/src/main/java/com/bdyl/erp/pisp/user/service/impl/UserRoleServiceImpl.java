package com.bdyl.erp.pisp.user.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.bdyl.erp.pisp.user.entity.UserRole;
import com.bdyl.erp.pisp.user.mapper.UserRoleMapper;
import com.bdyl.erp.pisp.user.service.UserRoleService;

/**
 * 用户角色关联服务实现类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Service
public class UserRoleServiceImpl extends ServiceImpl<UserRoleMapper, UserRole> implements UserRoleService {

    /**
     * 用户角色关联数据访问层
     */
    @Autowired
    private UserRoleMapper userRoleMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByUserId(Long userId) {
        log.info("删除用户的所有角色关联: userId={}", userId);
        userRoleMapper.deleteByUserId(userId);
        log.info("删除用户角色关联完成: userId={}", userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByRoleId(Long roleId) {
        log.info("删除角色的所有用户关联: roleId={}", roleId);
        userRoleMapper.deleteByRoleId(roleId);
        log.info("删除角色用户关联完成: roleId={}", roleId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByUserIdAndRoleIds(Long userId, List<Long> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return;
        }
        log.info("删除用户的指定角色关联: userId={}, roleIds={}", userId, roleIds);
        userRoleMapper.deleteByUserIdAndRoleIds(userId, roleIds);
        log.info("删除用户指定角色关联完成: userId={}, count={}", userId, roleIds.size());
    }

    @Override
    public List<UserRole> getByUserId(Long userId) {
        return userRoleMapper.selectByUserId(userId);
    }

    @Override
    public List<UserRole> getByRoleId(Long roleId) {
        return userRoleMapper.selectByRoleId(roleId);
    }

    @Override
    public boolean exists(Long userId, Long roleId) {
        return userRoleMapper.checkUserRoleExists(userId, roleId) > 0;
    }
}
