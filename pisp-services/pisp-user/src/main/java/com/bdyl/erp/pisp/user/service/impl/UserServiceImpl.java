package com.bdyl.erp.pisp.user.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.bdyl.erp.pisp.common.core.exception.BusinessException;
import com.bdyl.erp.pisp.common.core.result.ResponseCode;
import com.bdyl.erp.pisp.common.security.context.UserContextHolder;
import com.bdyl.erp.pisp.user.dto.request.PasswordChangeRequest;
import com.bdyl.erp.pisp.user.dto.request.UserCreateRequest;
import com.bdyl.erp.pisp.user.dto.request.UserQueryRequest;
import com.bdyl.erp.pisp.user.dto.request.UserUpdateRequest;
import com.bdyl.erp.pisp.user.dto.response.UserResponse;
import com.bdyl.erp.pisp.user.entity.User;
import com.bdyl.erp.pisp.user.entity.UserRole;
import com.bdyl.erp.pisp.user.enums.UserStatus;
import com.bdyl.erp.pisp.user.mapper.UserMapper;
import com.bdyl.erp.pisp.user.service.UserService;

/**
 * 用户服务实现类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    /**
     * 密码编码器
     */
    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserResponse createUser(UserCreateRequest request) {
        log.info("创建用户: {}", request.getUsername());

        // 创建用户实体
        User user = new User();
        BeanUtils.copyProperties(request, user);
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setStatus(UserStatus.ACTIVE); // 设置默认状态为激活

        // 保存用户
        save(user);

        log.info("用户创建成功: userId={}", user.getId());
        return convertToResponse(user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserResponse updateUser(UserUpdateRequest request) {
        log.info("更新用户: userId={}", request.getId());

        // 查询用户
        User existingUser = getById(request.getId());
        if (existingUser == null) {
            throw new BusinessException(ResponseCode.USER_NOT_FOUND);
        }

        // 更新用户信息
        BeanUtils.copyProperties(request, existingUser, "id", "username", "password");
        updateById(existingUser);

        log.info("用户更新成功: userId={}", existingUser.getId());
        return convertToResponse(existingUser);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteUser(Long userId) {
        log.info("删除用户: userId={}", userId);

        // 验证用户存在
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException(ResponseCode.USER_NOT_FOUND);
        }

        // 删除用户
        removeById(userId);

        log.info("用户删除成功: userId={}", userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteUsers(List<Long> userIds) {
        log.info("批量删除用户: userIds={}", userIds);
        if (!CollectionUtils.isEmpty(userIds)) {
            removeByIds(userIds);
        }
        log.info("批量删除用户成功: count={}", userIds.size());
    }

    @Override
    public UserResponse getUserById(Long userId) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException(ResponseCode.USER_NOT_FOUND);
        }
        return convertToResponse(user);
    }

    @Override
    public User getUserByUsername(String username) {
        return baseMapper.selectByUsernameWithRoles(username);
    }

    @Override
    public User getUserByEmail(String email) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getEmail, email);
        return getOne(queryWrapper);
    }

    @Override
    public User getUserByPhone(String phone) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getPhone, phone);
        return getOne(queryWrapper);
    }

    @Override
    public IPage<UserResponse> getUserPage(UserQueryRequest request) {
        Page<User> page = new Page<>(request.getPageNum(), request.getPageSize());

        // 使用QueryWrapper构建查询条件
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.hasText(request.getUsername()), User::getUsername, request.getUsername())
            .like(StringUtils.hasText(request.getRealName()), User::getRealName, request.getRealName())
            .like(StringUtils.hasText(request.getEmail()), User::getEmail, request.getEmail())
            .like(StringUtils.hasText(request.getPhone()), User::getPhone, request.getPhone())
            .eq(request.getDepartmentId() != null, User::getDepartmentId, request.getDepartmentId())
            .eq(request.getStatus() != null, User::getStatus, request.getStatus()).orderByDesc(User::getCreateTime);

        IPage<User> userPage = page(page, queryWrapper);

        return userPage.convert(this::convertToResponse);
    }

    @Override
    public List<UserResponse> getUsersByDepartmentId(Long departmentId) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getDepartmentId, departmentId).orderByDesc(User::getCreateTime);
        List<User> users = list(queryWrapper);
        return users.stream().map(this::convertToResponse).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changePassword(PasswordChangeRequest request) {
        log.info("修改密码: userId={}", request.getUserId());

        // 验证用户存在
        User user = getById(request.getUserId());
        if (user == null) {
            throw new BusinessException(ResponseCode.USER_NOT_FOUND);
        }

        // 验证原密码
        if (!passwordEncoder.matches(request.getOldPassword(), user.getPassword())) {
            throw new BusinessException(ResponseCode.USER_PASSWORD_ERROR, "原密码不正确");
        }

        // 验证新密码确认
        if (!request.getNewPassword().equals(request.getConfirmPassword())) {
            throw new BusinessException(ResponseCode.UNPROCESSABLE_ENTITY, "新密码确认不匹配");
        }

        // 更新密码
        user.setPassword(passwordEncoder.encode(request.getNewPassword()));
        updateById(user);

        log.info("密码修改成功: userId={}", request.getUserId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetPassword(Long userId, String newPassword) {
        log.info("重置密码: userId={}", userId);

        User user = getById(userId);
        if (user == null) {
            throw new BusinessException(ResponseCode.USER_NOT_FOUND);
        }

        user.setPassword(passwordEncoder.encode(newPassword));
        updateById(user);

        log.info("密码重置成功: userId={}", userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void activateUser(Long userId) {
        log.info("激活用户: userId={}", userId);

        User user = getById(userId);
        if (user == null) {
            throw new BusinessException(ResponseCode.USER_NOT_FOUND);
        }

        user.activate();
        updateById(user);

        log.info("用户激活成功: userId={}", userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deactivateUser(Long userId) {
        updateUserStatus(userId, com.bdyl.erp.pisp.user.enums.UserStatus.INACTIVE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void lockUser(Long userId) {
        updateUserStatus(userId, com.bdyl.erp.pisp.user.enums.UserStatus.LOCKED);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateUserStatus(List<Long> userIds, String status) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        baseMapper.batchUpdateStatus(userIds, status, getCurrentUserId());
        log.info("批量更新用户状态成功: count={}, status={}", userIds.size(), status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void assignRolesToUser(Long userId, List<Long> roleIds) {
        log.info("分配角色给用户: userId={}, roleIds={}", userId, roleIds);

        if (CollectionUtils.isEmpty(roleIds)) {
            return;
        }

        // 1. 验证用户存在
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException(ResponseCode.USER_NOT_FOUND);
        }

        // 2. 验证角色存在性和有效性
        // TODO: 调用角色服务验证角色ID的有效性

        // 3. 删除用户现有角色关联
        // TODO: 调用UserRoleService删除现有关联

        // 4. 创建新的角色关联
        List<UserRole> userRoles = roleIds.stream().map(roleId -> {
            UserRole userRole = new UserRole();
            userRole.setUserId(userId);
            userRole.setRoleId(roleId);
            userRole.setAssignedAt(LocalDateTime.now());
            userRole.setAssignedBy(getCurrentUserId());
            return userRole;
        }).collect(Collectors.toList());

        // TODO: 批量插入用户角色关联

        // 5. 清除用户权限缓存
        clearUserPermissionCache(userId);

        log.info("角色分配完成: userId={}, assignedRoles={}", userId, roleIds.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeRolesFromUser(Long userId, List<Long> roleIds) {
        log.info("移除用户角色: userId={}, roleIds={}", userId, roleIds);

        if (CollectionUtils.isEmpty(roleIds)) {
            return;
        }

        // 1. 验证用户存在
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException(ResponseCode.USER_NOT_FOUND);
        }

        // 2. 删除指定的角色关联
        // TODO: 调用UserRoleService删除指定角色关联

        // 3. 清除用户权限缓存
        clearUserPermissionCache(userId);

        log.info("角色移除完成: userId={}, removedRoles={}", userId, roleIds.size());
    }

    /**
     * 清除用户权限缓存
     *
     * @param userId 用户ID
     */
    private void clearUserPermissionCache(Long userId) {
        // TODO: 实现Redis缓存清除逻辑
        log.debug("清除用户权限缓存: userId={}", userId);
    }

    @Override
    public boolean isUsernameExists(String username, Long excludeId) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getUsername, username);
        if (excludeId != null) {
            queryWrapper.ne(User::getId, excludeId);
        }
        return count(queryWrapper) > 0;
    }

    @Override
    public boolean isEmailExists(String email, Long excludeId) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getEmail, email);
        if (excludeId != null) {
            queryWrapper.ne(User::getId, excludeId);
        }
        return count(queryWrapper) > 0;
    }

    @Override
    public boolean isPhoneExists(String phone, Long excludeId) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getPhone, phone);
        if (excludeId != null) {
            queryWrapper.ne(User::getId, excludeId);
        }
        return count(queryWrapper) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLastLoginInfo(Long userId, String loginIp) {
        baseMapper.updateLastLoginInfo(userId, loginIp);
    }

    @Override
    public boolean validatePassword(Long userId, String password) {
        User user = getById(userId);
        if (user == null) {
            return false;
        }
        return passwordEncoder.matches(password, user.getPassword());
    }

    // 私有方法

    private void updateUserStatus(Long userId, com.bdyl.erp.pisp.user.enums.UserStatus status) {
        User user = getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        user.setStatus(status);
        updateById(user);

        log.info("用户状态更新成功: userId={}, status={}", userId, status);
    }

    private UserResponse convertToResponse(User user) {
        UserResponse response = new UserResponse();
        BeanUtils.copyProperties(user, response);

        // 设置部门信息
        if (user.getDepartment() != null) {
            response.setDepartmentName(user.getDepartment().getDeptName());
        }

        // TODO: 设置角色和权限信息

        return response;
    }

    private Long getCurrentUserId() {
        var context = UserContextHolder.getContext();
        if (context == null) {
            throw new BusinessException(ResponseCode.UNAUTHORIZED);
        }
        return context.getUserId();
    }
}
