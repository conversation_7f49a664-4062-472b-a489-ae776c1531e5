# 开发环境配置
spring:
  # 数据源配置
  datasource:
    url: **************************************************************************************************************************
    username: pisp_dev
    password: pisp123456
    hikari:
      minimum-idle: 2
      maximum-pool-size: 10
  
  # Liquibase配置
  liquibase:
    contexts: dev
    drop-first: false
  
  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      database: 1

# 日志配置
logging:
  level:
    root: INFO
    com.bdyl.erp.pisp: DEBUG
    com.bdyl.erp.pisp.user.mapper: DEBUG
    org.springframework.security: DEBUG
  file:
    name: logs/pisp-user-service-dev.log

# 自定义配置
pisp:
  user:
    password:
      # 开发环境使用较低的加密强度
      strength: 4
    cache:
      # 开发环境缓存时间较短
      user-info-ttl: 600
      permission-ttl: 300
