# 生产环境配置
spring:
  # 数据源配置
  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:pisp_prod}?useUnicode=true&characterEncoding=utf8&useSSL=true&serverTimezone=Asia/Shanghai
    username: ${DB_USERNAME:pisp_prod}
    password: ${DB_PASSWORD:pisp123456}
    hikari:
      minimum-idle: 10
      maximum-pool-size: 50
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-timeout: 30000
  
  # Liquibase配置
  liquibase:
    contexts: prod
    drop-first: false
  
  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: ${REDIS_DB:2}
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 20
          max-wait: -1ms
          max-idle: 10
          min-idle: 5

# 日志配置
logging:
  level:
    root: WARN
    com.bdyl.erp.pisp: INFO
    com.bdyl.erp.pisp.user.mapper: INFO
  file:
    name: /var/log/pisp/pisp-user-service.log
    max-size: 500MB
    max-history: 60

# 管理端点配置
management:
  endpoint:
    health:
      show-details: never
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus

# 自定义配置
pisp:
  user:
    password:
      # 生产环境使用高强度加密
      strength: 12
    cache:
      # 生产环境缓存时间较长
      user-info-ttl: 7200
      permission-ttl: 3600
