# 测试环境配置
spring:
  # 数据源配置
  datasource:
    url: ***************************************************************************************************************************
    username: pisp_test
    password: pisp123456
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
  
  # Liquibase配置
  liquibase:
    contexts: test
    drop-first: true  # 测试环境每次重新创建表
  
  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      database: 3

# 日志配置
logging:
  level:
    root: INFO
    com.bdyl.erp.pisp: DEBUG
    com.bdyl.erp.pisp.user.mapper: DEBUG
  file:
    name: logs/pisp-user-service-test.log

# 自定义配置
pisp:
  user:
    password:
      # 测试环境使用较低的加密强度
      strength: 4
    cache:
      # 测试环境缓存时间很短
      user-info-ttl: 300
      permission-ttl: 180
