server:
  port: 8001
  servlet:
    context-path: /user-service

spring:
  application:
    name: pisp-user-service
  profiles:
    active: dev

  # 数据源配置
  datasource:
    driver-class-name: org.postgresql.Driver
    url: ***************************************************************************************************************************
    username: pisp_user
    password: pisp123456
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: HikariCP-User
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1

  # Liquibase配置
  liquibase:
    change-log: classpath:db/changelog/db.changelog-master.xml
    enabled: true
    drop-first: false
    contexts: dev
    default-schema: public

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password:
      database: 0
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
      fail-on-empty-beans: false
    deserialization:
      fail-on-unknown-properties: false

  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: pisp-system
      config:
        server-addr: localhost:8848
        namespace: pisp-system
        file-extension: yml
        group: DEFAULT_GROUP

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      update-strategy: not_null
      insert-strategy: not_null
      select-strategy: not_empty
    banner: false
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  type-aliases-package: com.bdyl.erp.pisp.user.entity

# 日志配置
logging:
  level:
    root: INFO
    com.bdyl.erp.pisp: DEBUG
    com.bdyl.erp.pisp.user.mapper: DEBUG
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
  file:
    name: logs/pisp-user-service.log
    max-size: 100MB
    max-history: 30

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

# 应用信息
info:
  app:
    name: ${spring.application.name}
    description: PISP用户管理服务
    version: 1.0.0
    encoding: UTF-8
    java:
      version: ${java.version}

# 自定义配置
pisp:
  # 安全配置
  security:
    jwt:
      # JWT密钥
      secret: "pisp-user-service-jwt-secret-key-for-token-generation-and-validation-2024"
      # JWT过期时间（秒）- 24小时
      expiration: 86400
      # 刷新Token过期时间（秒）- 7天
      refresh-expiration: 604800
      # JWT发行者
      issuer: "pisp-user-service"
  user:
    # 密码配置
    password:
      # 默认密码
      default: "123456"
      # 密码加密强度
      strength: 10
      # 密码过期天数
      expire-days: 90
    # 登录配置
    login:
      # 最大失败次数
      max-fail-count: 5
      # 锁定时间（分钟）
      lock-minutes: 30
    # 缓存配置
    cache:
      # 用户信息缓存时间（秒）
      user-info-ttl: 3600
      # 权限信息缓存时间（秒）
      permission-ttl: 1800
