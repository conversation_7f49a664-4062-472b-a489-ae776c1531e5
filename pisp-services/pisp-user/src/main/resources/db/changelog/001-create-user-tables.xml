<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.20.xsd">

    <!-- 创建部门表 -->
    <changeSet id="001-create-departments-table" author="pisp-system">
        <createTable tableName="sys_departments">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="dept_name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="dept_code" type="VARCHAR(50)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="parent_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="dept_level" type="INTEGER" defaultValue="1">
                <constraints nullable="false"/>
            </column>
            <column name="dept_path" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
            <column name="description" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
            <column name="leader_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="phone" type="VARCHAR(20)">
                <constraints nullable="true"/>
            </column>
            <column name="email" type="VARCHAR(100)">
                <constraints nullable="true"/>
            </column>
            <column name="sort_order" type="INTEGER" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="VARCHAR(20)" defaultValue="ACTIVE">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="INTEGER" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="deleted" type="INTEGER" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="tenant_id" type="BIGINT" defaultValue="1">
                <constraints nullable="false"/>
            </column>
            <column name="create_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="update_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="creator_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="updater_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="additional_info" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="remark" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        
        <!-- 创建索引 -->
        <createIndex tableName="sys_departments" indexName="idx_dept_code">
            <column name="dept_code"/>
        </createIndex>
        <createIndex tableName="sys_departments" indexName="idx_dept_parent_id">
            <column name="parent_id"/>
        </createIndex>
        <createIndex tableName="sys_departments" indexName="idx_dept_status">
            <column name="status"/>
        </createIndex>
        <createIndex tableName="sys_departments" indexName="idx_dept_tenant_deleted">
            <column name="tenant_id"/>
            <column name="deleted"/>
        </createIndex>
    </changeSet>

    <!-- 创建用户表 -->
    <changeSet id="002-create-users-table" author="pisp-system">
        <createTable tableName="sys_users">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="username" type="VARCHAR(50)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="password_hash" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="email" type="VARCHAR(100)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="phone" type="VARCHAR(20)">
                <constraints nullable="true"/>
            </column>
            <column name="real_name" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="avatar_url" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
            <column name="status" type="VARCHAR(20)" defaultValue="ACTIVE">
                <constraints nullable="false"/>
            </column>
            <column name="department_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="last_login_time" type="TIMESTAMP">
                <constraints nullable="true"/>
            </column>
            <column name="last_login_ip" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="login_count" type="INTEGER" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="INTEGER" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="deleted" type="INTEGER" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="tenant_id" type="BIGINT" defaultValue="1">
                <constraints nullable="false"/>
            </column>
            <column name="create_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="update_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="creator_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="updater_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="additional_info" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="remark" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        
        <!-- 创建索引 -->
        <createIndex tableName="sys_users" indexName="idx_user_username">
            <column name="username"/>
        </createIndex>
        <createIndex tableName="sys_users" indexName="idx_user_email">
            <column name="email"/>
        </createIndex>
        <createIndex tableName="sys_users" indexName="idx_user_phone">
            <column name="phone"/>
        </createIndex>
        <createIndex tableName="sys_users" indexName="idx_user_dept_id">
            <column name="department_id"/>
        </createIndex>
        <createIndex tableName="sys_users" indexName="idx_user_status">
            <column name="status"/>
        </createIndex>
        <createIndex tableName="sys_users" indexName="idx_user_tenant_deleted">
            <column name="tenant_id"/>
            <column name="deleted"/>
        </createIndex>
        
        <!-- 添加外键约束 -->
        <addForeignKeyConstraint
            baseTableName="sys_users"
            baseColumnNames="department_id"
            referencedTableName="sys_departments"
            referencedColumnNames="id"
            constraintName="fk_user_department"/>
    </changeSet>

    <!-- 创建权限表 -->
    <changeSet id="003-create-permissions-table" author="pisp-system">
        <createTable tableName="sys_permissions">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="permission_name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="permission_code" type="VARCHAR(100)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="description" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
            <column name="permission_type" type="VARCHAR(20)" defaultValue="API">
                <constraints nullable="false"/>
            </column>
            <column name="parent_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="permission_path" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
            <column name="sort_order" type="INTEGER" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="is_system" type="BOOLEAN" defaultValue="false">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="INTEGER" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="deleted" type="INTEGER" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="tenant_id" type="BIGINT" defaultValue="1">
                <constraints nullable="false"/>
            </column>
            <column name="create_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="update_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="creator_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="updater_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="additional_info" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="remark" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <!-- 创建索引 -->
        <createIndex tableName="sys_permissions" indexName="idx_perm_code">
            <column name="permission_code"/>
        </createIndex>
        <createIndex tableName="sys_permissions" indexName="idx_perm_parent_id">
            <column name="parent_id"/>
        </createIndex>
        <createIndex tableName="sys_permissions" indexName="idx_perm_type">
            <column name="permission_type"/>
        </createIndex>
        <createIndex tableName="sys_permissions" indexName="idx_perm_tenant_deleted">
            <column name="tenant_id"/>
            <column name="deleted"/>
        </createIndex>
    </changeSet>

    <!-- 创建角色表 -->
    <changeSet id="004-create-roles-table" author="pisp-system">
        <createTable tableName="sys_roles">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="role_name" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="role_code" type="VARCHAR(50)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="description" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
            <column name="is_system" type="BOOLEAN" defaultValue="false">
                <constraints nullable="false"/>
            </column>
            <column name="sort_order" type="INTEGER" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="VARCHAR(20)" defaultValue="ACTIVE">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="INTEGER" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="deleted" type="INTEGER" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="tenant_id" type="BIGINT" defaultValue="1">
                <constraints nullable="false"/>
            </column>
            <column name="create_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="update_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="creator_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="updater_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="additional_info" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="remark" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <!-- 创建索引 -->
        <createIndex tableName="sys_roles" indexName="idx_role_code">
            <column name="role_code"/>
        </createIndex>
        <createIndex tableName="sys_roles" indexName="idx_role_status">
            <column name="status"/>
        </createIndex>
        <createIndex tableName="sys_roles" indexName="idx_role_tenant_deleted">
            <column name="tenant_id"/>
            <column name="deleted"/>
        </createIndex>
    </changeSet>

    <!-- 创建用户角色关联表 -->
    <changeSet id="005-create-user-roles-table" author="pisp-system">
        <createTable tableName="sys_user_roles">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="role_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="assigned_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="assigned_by" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="version" type="INTEGER" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="deleted" type="INTEGER" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="tenant_id" type="BIGINT" defaultValue="1">
                <constraints nullable="false"/>
            </column>
            <column name="create_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="update_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="creator_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="updater_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="additional_info" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="remark" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <!-- 创建唯一约束 -->
        <addUniqueConstraint
            tableName="sys_user_roles"
            columnNames="user_id,role_id,deleted"
            constraintName="uk_user_role"/>

        <!-- 创建索引 -->
        <createIndex tableName="sys_user_roles" indexName="idx_ur_user_id">
            <column name="user_id"/>
        </createIndex>
        <createIndex tableName="sys_user_roles" indexName="idx_ur_role_id">
            <column name="role_id"/>
        </createIndex>
        <createIndex tableName="sys_user_roles" indexName="idx_ur_tenant_deleted">
            <column name="tenant_id"/>
            <column name="deleted"/>
        </createIndex>

        <!-- 添加外键约束 -->
        <addForeignKeyConstraint
            baseTableName="sys_user_roles"
            baseColumnNames="user_id"
            referencedTableName="sys_users"
            referencedColumnNames="id"
            constraintName="fk_ur_user"/>
        <addForeignKeyConstraint
            baseTableName="sys_user_roles"
            baseColumnNames="role_id"
            referencedTableName="sys_roles"
            referencedColumnNames="id"
            constraintName="fk_ur_role"/>
    </changeSet>

    <!-- 创建角色权限关联表 -->
    <changeSet id="006-create-role-permissions-table" author="pisp-system">
        <createTable tableName="sys_role_permissions">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="role_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="permission_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="assigned_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="assigned_by" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="version" type="INTEGER" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="deleted" type="INTEGER" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="tenant_id" type="BIGINT" defaultValue="1">
                <constraints nullable="false"/>
            </column>
            <column name="create_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="update_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="creator_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="updater_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="additional_info" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="remark" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <!-- 创建唯一约束 -->
        <addUniqueConstraint
            tableName="sys_role_permissions"
            columnNames="role_id,permission_id,deleted"
            constraintName="uk_role_permission"/>

        <!-- 创建索引 -->
        <createIndex tableName="sys_role_permissions" indexName="idx_rp_role_id">
            <column name="role_id"/>
        </createIndex>
        <createIndex tableName="sys_role_permissions" indexName="idx_rp_permission_id">
            <column name="permission_id"/>
        </createIndex>
        <createIndex tableName="sys_role_permissions" indexName="idx_rp_tenant_deleted">
            <column name="tenant_id"/>
            <column name="deleted"/>
        </createIndex>

        <!-- 添加外键约束 -->
        <addForeignKeyConstraint
            baseTableName="sys_role_permissions"
            baseColumnNames="role_id"
            referencedTableName="sys_roles"
            referencedColumnNames="id"
            constraintName="fk_rp_role"/>
        <addForeignKeyConstraint
            baseTableName="sys_role_permissions"
            baseColumnNames="permission_id"
            referencedTableName="sys_permissions"
            referencedColumnNames="id"
            constraintName="fk_rp_permission"/>
    </changeSet>

</databaseChangeLog>
