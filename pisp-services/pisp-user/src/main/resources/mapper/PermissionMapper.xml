<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdyl.erp.pisp.user.mapper.PermissionMapper">

    <!-- Base Permission Result Map -->
    <resultMap id="BasePermissionResult" type="com.bdyl.erp.pisp.user.entity.Permission">
        <id property="id" column="id" />
        <result property="permissionName" column="permission_name" />
        <result property="permissionCode" column="permission_code" />
        <result property="description" column="description" />
        <result property="permissionType" column="permission_type" />
        <result property="parentId" column="parent_id" />
        <result property="permissionPath" column="permission_path" />
        <result property="sortOrder" column="sort_order" />
        <result property="isSystem" column="is_system" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
        <result property="creatorId" column="creator_id" />
        <result property="updaterId" column="updater_id" />
        <result property="version" column="version" />
    </resultMap>

    <!-- Select permission by code -->
    <select id="selectByPermissionCode" parameterType="string" resultMap="BasePermissionResult">
        SELECT * FROM sys_permissions WHERE permission_code = #{permissionCode} AND deleted = 0 </select>

    <!-- Select child permissions by parent ID -->
    <select id="selectByParentId" parameterType="long" resultMap="BasePermissionResult"> SELECT *
        FROM sys_permissions WHERE parent_id = #{parentId} AND deleted = 0 ORDER BY sort_order ASC,
        create_time ASC </select>

    <!-- Select permission tree -->
    <select id="selectPermissionTree" resultMap="BasePermissionResult"> SELECT * FROM
        sys_permissions WHERE deleted = 0 ORDER BY parent_id ASC, sort_order ASC, create_time ASC </select>

    <!-- Select permissions by role ID -->
    <select id="selectByRoleId" parameterType="long" resultMap="BasePermissionResult"> SELECT p.*
        FROM sys_permissions p INNER JOIN sys_role_permissions rp ON p.id = rp.permission_id WHERE
        rp.role_id = #{roleId} AND p.deleted = 0 AND rp.deleted = 0 ORDER BY p.sort_order ASC,
        p.create_time ASC </select>

    <!-- Select permissions by user ID -->
    <select id="selectByUserId" parameterType="long" resultMap="BasePermissionResult"> SELECT
        DISTINCT p.* FROM sys_permissions p INNER JOIN sys_role_permissions rp ON p.id =
        rp.permission_id INNER JOIN sys_user_roles ur ON rp.role_id = ur.role_id WHERE ur.user_id =
        #{userId} AND p.deleted = 0 AND rp.deleted = 0 AND ur.deleted = 0 ORDER BY p.sort_order ASC,
        p.create_time ASC </select>

    <!-- Check if permission code exists -->
    <select id="checkPermissionCodeExists" resultType="int"> SELECT COUNT(*) FROM sys_permissions
        WHERE permission_code = #{permissionCode} AND deleted = 0 <if test="excludeId != null"> AND
        id != #{excludeId} </if>
    </select>

    <!-- Check if permission name exists -->
    <select id="checkPermissionNameExists" resultType="int"> SELECT COUNT(*) FROM sys_permissions
        WHERE permission_name = #{permissionName} AND deleted = 0 <if test="parentId != null"> AND
        parent_id = #{parentId} </if>
        <if test="parentId == null"> AND parent_id IS NULL </if>
        <if
            test="excludeId != null"> AND id != #{excludeId} </if>
    </select>

    <!-- Select permissions by permission type -->
    <select id="selectByPermissionType" parameterType="string" resultMap="BasePermissionResult">
        SELECT * FROM sys_permissions WHERE permission_type = #{permissionType} AND deleted = 0
        ORDER BY sort_order ASC, create_time ASC </select>

    <!-- Select menu permissions -->
    <select id="selectMenuPermissions" resultMap="BasePermissionResult"> SELECT * FROM
        sys_permissions WHERE permission_type = 'MENU' AND deleted = 0 ORDER BY parent_id ASC,
        sort_order ASC, create_time ASC </select>

    <!-- Count roles by permission IDs -->
    <select id="countRolesByPermissionIds" resultType="int"> SELECT COUNT(DISTINCT rp.role_id) FROM
        sys_role_permissions rp WHERE rp.permission_id IN <foreach collection="permissionIds"
            item="permissionId" open="(" separator="," close=")"> #{permissionId} </foreach> AND
        rp.deleted = 0 </select>

</mapper> 