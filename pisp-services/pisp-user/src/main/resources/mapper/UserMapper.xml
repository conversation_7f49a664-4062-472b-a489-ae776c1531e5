<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdyl.erp.pisp.user.mapper.UserMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.bdyl.erp.pisp.user.entity.User">
        <id column="id" property="id" />
        <result column="username" property="username" />
        <result column="password_hash" property="password" />
        <result column="email" property="email" />
        <result column="phone" property="phone" />
        <result column="real_name" property="realName" />
        <result column="avatar_url" property="avatarUrl" />
        <result column="status" property="status" />
        <result column="department_id" property="departmentId" />
        <result column="last_login_time" property="lastLoginTime" />
        <result column="last_login_ip" property="lastLoginIp" />
        <result column="login_count" property="loginCount" />
        <result column="version" property="version" />
        <result column="deleted" property="deleted" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator_id" property="creatorId" />
        <result column="updater_id" property="updaterId" />
        <result column="additional_info" property="additionalInfo" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 包含部门信息的结果映射 -->
    <resultMap id="UserWithDepartmentResultMap" type="com.bdyl.erp.pisp.user.entity.User" extends="BaseResultMap">
        <association property="department" javaType="com.bdyl.erp.pisp.user.entity.Department">
            <id column="dept_id" property="id" />
            <result column="dept_name" property="deptName" />
            <result column="dept_code" property="deptCode" />
            <result column="dept_status" property="status" />
        </association>
    </resultMap>

    <!-- 包含角色信息的结果映射 -->
    <resultMap id="UserWithRolesResultMap" type="com.bdyl.erp.pisp.user.entity.User" extends="BaseResultMap">
        <collection property="roles" ofType="com.bdyl.erp.pisp.user.entity.Role">
            <id column="role_id" property="id" />
            <result column="role_name" property="roleName" />
            <result column="role_code" property="roleCode" />
            <result column="role_status" property="status" />
        </collection>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        u.id, u.username, u.password_hash, u.email, u.phone, u.real_name, u.avatar_url,
        u.status, u.department_id, u.last_login_time, u.last_login_ip, u.login_count,
        u.version, u.deleted, u.tenant_id, u.create_time, u.update_time, 
        u.creator_id, u.updater_id, u.additional_info, u.remark
    </sql>

    <!-- 根据用户名查询用户（包含角色信息） -->
    <select id="selectByUsernameWithRoles" resultMap="UserWithRolesResultMap">
        SELECT 
            <include refid="Base_Column_List" />,
            r.id as role_id, r.role_name, r.role_code, r.status as role_status
        FROM sys_users u
        LEFT JOIN sys_user_roles ur ON u.id = ur.user_id AND ur.deleted = 0
        LEFT JOIN sys_roles r ON ur.role_id = r.id AND r.deleted = 0
        WHERE u.username = #{username} AND u.deleted = 0
    </select>

    <!-- 根据邮箱查询用户 -->
    <select id="selectByEmail" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_users u
        WHERE u.email = #{email} AND u.deleted = 0
    </select>

    <!-- 根据手机号查询用户 -->
    <select id="selectByPhone" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_users u
        WHERE u.phone = #{phone} AND u.deleted = 0
    </select>

    <!-- 根据部门ID查询用户列表 -->
    <select id="selectByDepartmentId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_users u
        WHERE u.department_id = #{departmentId} AND u.deleted = 0
        ORDER BY u.create_time DESC
    </select>

    <!-- 分页查询用户列表（包含部门信息） -->
    <select id="selectUserPageWithDepartment" resultMap="UserWithDepartmentResultMap">
        SELECT 
            <include refid="Base_Column_List" />,
            d.id as dept_id, d.dept_name, d.dept_code, d.status as dept_status
        FROM sys_users u
        LEFT JOIN sys_departments d ON u.department_id = d.id AND d.deleted = 0
        <where>
            u.deleted = 0
            <if test="username != null and username != ''">
                AND u.username LIKE CONCAT('%', #{username}, '%')
            </if>
            <if test="realName != null and realName != ''">
                AND u.real_name LIKE CONCAT('%', #{realName}, '%')
            </if>
            <if test="email != null and email != ''">
                AND u.email LIKE CONCAT('%', #{email}, '%')
            </if>
            <if test="phone != null and phone != ''">
                AND u.phone LIKE CONCAT('%', #{phone}, '%')
            </if>
            <if test="departmentId != null">
                AND u.department_id = #{departmentId}
            </if>
            <if test="status != null and status != ''">
                AND u.status = #{status}
            </if>
        </where>
        ORDER BY u.create_time DESC
    </select>

    <!-- 根据用户ID查询用户角色列表 -->
    <select id="selectRolesByUserId" resultType="com.bdyl.erp.pisp.user.entity.Role">
        SELECT r.id, r.role_name, r.role_code, r.description, r.status, r.is_system, r.sort_order
        FROM sys_roles r
        INNER JOIN sys_user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = #{userId} AND r.deleted = 0 AND ur.deleted = 0
        ORDER BY r.sort_order, r.create_time
    </select>

    <!-- 根据用户ID查询用户权限代码列表 -->
    <select id="selectPermissionCodesByUserId" resultType="string">
        SELECT DISTINCT p.permission_code
        FROM sys_permissions p
        INNER JOIN sys_role_permissions rp ON p.id = rp.permission_id
        INNER JOIN sys_user_roles ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = #{userId} 
        AND p.deleted = 0 AND rp.deleted = 0 AND ur.deleted = 0
    </select>

    <!-- 检查用户名是否存在 -->
    <select id="checkUsernameExists" resultType="int">
        SELECT COUNT(1)
        FROM sys_users
        WHERE username = #{username} AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查邮箱是否存在 -->
    <select id="checkEmailExists" resultType="int">
        SELECT COUNT(1)
        FROM sys_users
        WHERE email = #{email} AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查手机号是否存在 -->
    <select id="checkPhoneExists" resultType="int">
        SELECT COUNT(1)
        FROM sys_users
        WHERE phone = #{phone} AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 批量更新用户状态 -->
    <update id="batchUpdateStatus">
        UPDATE sys_users 
        SET status = #{status}, updater_id = #{updaterId}, update_time = NOW()
        WHERE id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        AND deleted = 0
    </update>

    <!-- 根据部门ID列表查询用户数量 -->
    <select id="countByDepartmentIds" resultType="int">
        SELECT COUNT(1)
        FROM sys_users
        WHERE department_id IN
        <foreach collection="departmentIds" item="departmentId" open="(" separator="," close=")">
            #{departmentId}
        </foreach>
        AND deleted = 0
    </select>

    <!-- 更新用户最后登录信息 -->
    <update id="updateLastLoginInfo">
        UPDATE sys_users
        SET last_login_time = NOW(),
            last_login_ip = #{loginIp},
            login_count = COALESCE(login_count, 0) + 1,
            update_time = NOW()
        WHERE id = #{userId} AND deleted = 0
    </update>

</mapper>
