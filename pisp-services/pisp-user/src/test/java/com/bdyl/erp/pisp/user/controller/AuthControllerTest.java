package com.bdyl.erp.pisp.user.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import com.bdyl.erp.pisp.user.dto.request.LoginRequest;
import com.bdyl.erp.pisp.user.dto.request.RefreshTokenRequest;
import com.bdyl.erp.pisp.user.dto.response.LoginResponse;
import com.bdyl.erp.pisp.user.service.AuthService;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 认证控制器测试类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class AuthControllerTest {

    /**
     * 认证服务Mock
     */
    @Mock
    private AuthService authService;

    /**
     * 认证控制器
     */
    @InjectMocks
    private AuthController authController;

    /**
     * MockMvc
     */
    private MockMvc mockMvc;

    /**
     * JSON对象映射器
     */
    private ObjectMapper objectMapper;

    /**
     * 登录响应
     */
    private LoginResponse loginResponse;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(authController).build();
        objectMapper = new ObjectMapper();

        // 准备测试数据
        loginResponse = new LoginResponse();
        loginResponse.setAccessToken("test-access-token");
        loginResponse.setRefreshToken("test-refresh-token");
        loginResponse.setExpiresIn(86400L);

        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo();
        userInfo.setUserId(1L);
        userInfo.setUsername("testuser");
        userInfo.setRealName("测试用户");
        loginResponse.setUserInfo(userInfo);
    }

    @Test
    void testLogin() throws Exception {
        // Given
        LoginRequest request = new LoginRequest();
        request.setUsername("testuser");
        request.setPassword("password123");

        when(authService.login(any(LoginRequest.class))).thenReturn(loginResponse);

        // When & Then
        mockMvc
            .perform(post("/api/auth/login").contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk()).andExpect(jsonPath("$.code").value(200))
            .andExpect(jsonPath("$.data.accessToken").value("test-access-token"))
            .andExpect(jsonPath("$.data.userInfo.username").value("testuser"));

        verify(authService).login(any(LoginRequest.class));
    }

    @Test
    void testRefreshToken() throws Exception {
        // Given
        RefreshTokenRequest request = new RefreshTokenRequest();
        request.setRefreshToken("test-refresh-token");

        when(authService.refreshToken(any(RefreshTokenRequest.class))).thenReturn(loginResponse);

        // When & Then
        mockMvc
            .perform(post("/api/auth/refresh").contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk()).andExpect(jsonPath("$.code").value(200))
            .andExpect(jsonPath("$.data.accessToken").value("test-access-token"));

        verify(authService).refreshToken(any(RefreshTokenRequest.class));
    }

    @Test
    void testValidateToken() throws Exception {
        // Given
        when(authService.validateToken(any())).thenReturn(true);

        // When & Then
        mockMvc.perform(get("/api/auth/validate").header("Authorization", "Bearer test-token"))
            .andExpect(status().isOk()).andExpect(jsonPath("$.code").value(200))
            .andExpect(jsonPath("$.data").value(true));

        verify(authService).validateToken(any());
    }
}
