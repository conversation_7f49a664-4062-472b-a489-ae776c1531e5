package com.bdyl.erp.pisp.user.controller;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import com.bdyl.erp.pisp.user.dto.request.PasswordChangeRequest;
import com.bdyl.erp.pisp.user.dto.request.UserCreateRequest;
import com.bdyl.erp.pisp.user.dto.request.UserQueryRequest;
import com.bdyl.erp.pisp.user.dto.request.UserUpdateRequest;
import com.bdyl.erp.pisp.user.dto.response.UserResponse;
import com.bdyl.erp.pisp.user.enums.UserStatus;
import com.bdyl.erp.pisp.user.service.UserService;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 用户控制器简单测试类 - 不依赖Spring Boot自动配置
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class UserControllerTest {

    /**
     * 用户服务Mock对象
     */
    @Mock
    private UserService userService;

    /**
     * 用户控制器对象
     */
    @InjectMocks
    private UserController userController;

    /**
     * MockMvc对象
     */
    private MockMvc mockMvc;
    /**
     * ObjectMapper对象
     */
    private ObjectMapper objectMapper;
    /**
     * 用户响应对象
     */
    private UserResponse userResponse;
    /**
     * 用户创建请求对象
     */
    private UserCreateRequest createRequest;
    /**
     * 用户更新请求对象
     */
    private UserUpdateRequest updateRequest;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(userController).build();
        objectMapper = new ObjectMapper();

        userResponse = new UserResponse();
        userResponse.setId(1L);
        userResponse.setUsername("testuser");
        userResponse.setEmail("<EMAIL>");
        userResponse.setPhone("13800138000");
        userResponse.setRealName("测试用户");
        userResponse.setStatus(UserStatus.ACTIVE);
        userResponse.setDepartmentId(1L);
        userResponse.setCreateTime(LocalDateTime.now());

        createRequest = new UserCreateRequest();
        createRequest.setUsername("newuser");
        createRequest.setPassword("password123");
        createRequest.setEmail("<EMAIL>");
        createRequest.setPhone("13800138001");
        createRequest.setRealName("新用户");
        createRequest.setDepartmentId(1L);
        createRequest.setStatus(UserStatus.ACTIVE);

        updateRequest = new UserUpdateRequest();
        updateRequest.setId(1L);
        updateRequest.setEmail("<EMAIL>");
        updateRequest.setPhone("13800138002");
        updateRequest.setRealName("更新用户");
        updateRequest.setDepartmentId(2L);
        updateRequest.setVersion(0);
    }

    @Test
    void testCreateUser() throws Exception {
        // Given
        when(userService.createUser(any(UserCreateRequest.class))).thenReturn(userResponse);

        // When & Then
        mockMvc
            .perform(post("/api/users").contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createRequest)))
            .andExpect(status().isOk()).andExpect(jsonPath("$.code").value(200))
            .andExpect(jsonPath("$.data.username").value(userResponse.getUsername()))
            .andExpect(jsonPath("$.data.email").value(userResponse.getEmail()));

        verify(userService).createUser(any(UserCreateRequest.class));
    }

    @Test
    void testUpdateUser() throws Exception {
        // Given
        when(userService.updateUser(any(UserUpdateRequest.class))).thenReturn(userResponse);

        // When & Then
        mockMvc
            .perform(put("/api/users/1").contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest)))
            .andExpect(status().isOk()).andExpect(jsonPath("$.code").value(200))
            .andExpect(jsonPath("$.data.id").value(userResponse.getId()));

        verify(userService).updateUser(any(UserUpdateRequest.class));
    }

    @Test
    void testDeleteUser() throws Exception {
        // Given
        doNothing().when(userService).deleteUser(1L);

        // When & Then
        mockMvc.perform(delete("/api/users/1")).andExpect(status().isOk()).andExpect(jsonPath("$.code").value(200));

        verify(userService).deleteUser(1L);
    }

    @Test
    void testBatchDeleteUsers() throws Exception {
        // Given
        List<Long> userIds = Arrays.asList(1L, 2L, 3L);
        doNothing().when(userService).batchDeleteUsers(userIds);

        // When & Then
        mockMvc
            .perform(delete("/api/users/batch").contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(userIds)))
            .andExpect(status().isOk()).andExpect(jsonPath("$.code").value(200));

        verify(userService).batchDeleteUsers(userIds);
    }

    @Test
    void testGetUserById() throws Exception {
        // Given
        when(userService.getUserById(1L)).thenReturn(userResponse);

        // When & Then
        mockMvc.perform(get("/api/users/1")).andExpect(status().isOk()).andExpect(jsonPath("$.code").value(200))
            .andExpect(jsonPath("$.data.id").value(userResponse.getId()))
            .andExpect(jsonPath("$.data.username").value(userResponse.getUsername()));

        verify(userService).getUserById(1L);
    }

    @Test
    void testGetUserPage() throws Exception {
        // Given
        Page<UserResponse> page = new Page<>(1, 10);
        page.setRecords(Arrays.asList(userResponse));
        page.setTotal(1);

        when(userService.getUserPage(any(UserQueryRequest.class))).thenReturn(page);

        // When & Then
        mockMvc.perform(get("/api/users").param("pageNum", "1").param("pageSize", "10").param("username", "test"))
            .andExpect(status().isOk()).andExpect(jsonPath("$.code").value(200))
            .andExpect(jsonPath("$.data.records").isArray()).andExpect(jsonPath("$.data.total").value(1));

        verify(userService).getUserPage(any(UserQueryRequest.class));
    }

    @Test
    void testGetUsersByDepartmentId() throws Exception {
        // Given
        List<UserResponse> users = Arrays.asList(userResponse);
        when(userService.getUsersByDepartmentId(1L)).thenReturn(users);

        // When & Then
        mockMvc.perform(get("/api/users/department/1")).andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(200)).andExpect(jsonPath("$.data").isArray())
            .andExpect(jsonPath("$.data[0].id").value(userResponse.getId()));

        verify(userService).getUsersByDepartmentId(1L);
    }

    @Test
    void testChangePassword() throws Exception {
        // Given
        PasswordChangeRequest request = new PasswordChangeRequest();
        request.setUserId(1L);
        request.setOldPassword("oldPassword");
        request.setNewPassword("newPassword");
        request.setConfirmPassword("newPassword");

        doNothing().when(userService).changePassword(any(PasswordChangeRequest.class));

        // When & Then
        mockMvc
            .perform(put("/api/users/password/change").contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk()).andExpect(jsonPath("$.code").value(200));

        verify(userService).changePassword(any(PasswordChangeRequest.class));
    }

    @Test
    void testResetPassword() throws Exception {
        // Given
        doNothing().when(userService).resetPassword(1L, "newPassword");

        // When & Then
        mockMvc.perform(put("/api/users/1/password/reset").param("newPassword", "newPassword"))
            .andExpect(status().isOk()).andExpect(jsonPath("$.code").value(200));

        verify(userService).resetPassword(1L, "newPassword");
    }

    @Test
    void testActivateUser() throws Exception {
        // Given
        doNothing().when(userService).activateUser(1L);

        // When & Then
        mockMvc.perform(put("/api/users/1/activate")).andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(200));

        verify(userService).activateUser(1L);
    }

    @Test
    void testDeactivateUser() throws Exception {
        // Given
        doNothing().when(userService).deactivateUser(1L);

        // When & Then
        mockMvc.perform(put("/api/users/1/deactivate")).andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(200));

        verify(userService).deactivateUser(1L);
    }

    @Test
    void testLockUser() throws Exception {
        // Given
        doNothing().when(userService).lockUser(1L);

        // When & Then
        mockMvc.perform(put("/api/users/1/lock")).andExpect(status().isOk()).andExpect(jsonPath("$.code").value(200));

        verify(userService).lockUser(1L);
    }

    @Test
    void testCheckUsernameExists() throws Exception {
        // Given
        when(userService.isUsernameExists("testuser", null)).thenReturn(true);

        // When & Then
        mockMvc.perform(get("/api/users/check/username").param("username", "testuser")).andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(200)).andExpect(jsonPath("$.data").value(true));

        verify(userService).isUsernameExists("testuser", null);
    }

    @Test
    void testCheckEmailExists() throws Exception {
        // Given
        when(userService.isEmailExists("<EMAIL>", null)).thenReturn(false);

        // When & Then
        mockMvc.perform(get("/api/users/check/email").param("email", "<EMAIL>")).andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(200)).andExpect(jsonPath("$.data").value(false));

        verify(userService).isEmailExists("<EMAIL>", null);
    }

    @Test
    void testCheckPhoneExists() throws Exception {
        // Given
        when(userService.isPhoneExists("13800138000", 1L)).thenReturn(false);

        // When & Then
        mockMvc.perform(get("/api/users/check/phone").param("phone", "13800138000").param("excludeId", "1"))
            .andExpect(status().isOk()).andExpect(jsonPath("$.code").value(200))
            .andExpect(jsonPath("$.data").value(false));

        verify(userService).isPhoneExists("13800138000", 1L);
    }
}
