package com.bdyl.erp.pisp.user.integration;

import java.util.Arrays;
import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.annotation.Transactional;

import com.bdyl.erp.pisp.common.core.exception.BusinessException;
import com.bdyl.erp.pisp.common.security.context.UserContext;
import com.bdyl.erp.pisp.common.security.context.UserContextHolder;
import com.bdyl.erp.pisp.user.dto.request.PasswordChangeRequest;
import com.bdyl.erp.pisp.user.dto.request.UserCreateRequest;
import com.bdyl.erp.pisp.user.dto.request.UserQueryRequest;
import com.bdyl.erp.pisp.user.dto.request.UserUpdateRequest;
import com.bdyl.erp.pisp.user.dto.response.UserResponse;
import com.bdyl.erp.pisp.user.enums.UserStatus;
import com.bdyl.erp.pisp.user.service.UserService;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 用户服务集成测试类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@SpringBootTest
@ActiveProfiles("test")
@TestPropertySource(properties = {"spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE",
    "spring.datasource.driver-class-name=org.h2.Driver", "spring.datasource.username=sa", "spring.datasource.password=",
    "spring.jpa.hibernate.ddl-auto=create-drop"})
@Transactional
class UserServiceIntegrationTest {

    /**
     * 用户服务对象
     */
    @Autowired
    private UserService userService;

    @BeforeEach
    void setUp() {
        // 设置测试用户上下文
        UserContext userContext = new UserContext();
        userContext.setUserId(1L);
        userContext.setUsername("test_user");
        userContext.setTenantId(1L);
        UserContextHolder.setContext(userContext);
    }

    @AfterEach
    void tearDown() {
        // 清理用户上下文 - 事务回滚时自动清理
        UserContextHolder.clear();
    }

    @Test
    void testCreateAndGetUser() {
        // Given
        UserCreateRequest createRequest = new UserCreateRequest();
        createRequest.setUsername("integrationtest");
        createRequest.setPassword("password123");
        createRequest.setEmail("<EMAIL>");
        createRequest.setPhone("13800138888");
        createRequest.setRealName("集成测试用户");
        createRequest.setDepartmentId(1L);

        // When
        UserResponse createdUser = userService.createUser(createRequest);

        // Then
        assertNotNull(createdUser);
        assertNotNull(createdUser.getId());
        assertEquals(createRequest.getUsername(), createdUser.getUsername());
        assertEquals(createRequest.getEmail(), createdUser.getEmail());
        assertEquals(createRequest.getPhone(), createdUser.getPhone());
        assertEquals(createRequest.getRealName(), createdUser.getRealName());
        assertEquals(UserStatus.ACTIVE, createdUser.getStatus());

        // Verify we can get the user
        UserResponse retrievedUser = userService.getUserById(createdUser.getId());
        assertEquals(createdUser.getId(), retrievedUser.getId());
        assertEquals(createdUser.getUsername(), retrievedUser.getUsername());
    }

    @Test
    void testUpdateUser() {
        // Given - Create a user first
        UserCreateRequest createRequest = new UserCreateRequest();
        createRequest.setUsername("updatetest");
        createRequest.setPassword("password123");
        createRequest.setEmail("<EMAIL>");
        createRequest.setPhone("13800138999");
        createRequest.setRealName("更新测试用户");
        createRequest.setDepartmentId(1L);

        UserResponse createdUser = userService.createUser(createRequest);

        // When - Update the user
        UserUpdateRequest updateRequest = new UserUpdateRequest();
        updateRequest.setId(createdUser.getId());
        updateRequest.setEmail("<EMAIL>");
        updateRequest.setPhone("13800139000");
        updateRequest.setRealName("已更新用户");
        updateRequest.setDepartmentId(2L);

        UserResponse updatedUser = userService.updateUser(updateRequest);

        // Then
        assertEquals(createdUser.getId(), updatedUser.getId());
        assertEquals(updateRequest.getEmail(), updatedUser.getEmail());
        assertEquals(updateRequest.getPhone(), updatedUser.getPhone());
        assertEquals(updateRequest.getRealName(), updatedUser.getRealName());
        assertEquals(updateRequest.getDepartmentId(), updatedUser.getDepartmentId());
        // Username should not change
        assertEquals(createdUser.getUsername(), updatedUser.getUsername());
    }

    @Test
    void testChangePassword() {
        // Given - Create a user first
        UserCreateRequest createRequest = new UserCreateRequest();
        createRequest.setUsername("passwordtest");
        createRequest.setPassword("oldpassword");
        createRequest.setEmail("<EMAIL>");
        createRequest.setPhone("13800139001");
        createRequest.setRealName("密码测试用户");
        createRequest.setDepartmentId(1L);

        UserResponse createdUser = userService.createUser(createRequest);

        // When - Change password
        PasswordChangeRequest passwordRequest = new PasswordChangeRequest();
        passwordRequest.setUserId(createdUser.getId());
        passwordRequest.setOldPassword("oldpassword");
        passwordRequest.setNewPassword("newpassword");
        passwordRequest.setConfirmPassword("newpassword");

        // Then - Should not throw exception
        assertDoesNotThrow(() -> userService.changePassword(passwordRequest));

        // Verify new password works
        assertTrue(userService.validatePassword(createdUser.getId(), "newpassword"));
        assertFalse(userService.validatePassword(createdUser.getId(), "oldpassword"));
    }

    @Test
    void testUserStatusOperations() {
        // Given - Create a user first
        UserCreateRequest createRequest = new UserCreateRequest();
        createRequest.setUsername("statustest");
        createRequest.setPassword("password123");
        createRequest.setEmail("<EMAIL>");
        createRequest.setPhone("13800139002");
        createRequest.setRealName("状态测试用户");
        createRequest.setDepartmentId(1L);

        UserResponse createdUser = userService.createUser(createRequest);
        assertEquals(UserStatus.ACTIVE, createdUser.getStatus());

        // When & Then - Test deactivate
        assertDoesNotThrow(() -> userService.deactivateUser(createdUser.getId()));
        UserResponse deactivatedUser = userService.getUserById(createdUser.getId());
        assertEquals(UserStatus.INACTIVE, deactivatedUser.getStatus());

        // When & Then - Test lock
        assertDoesNotThrow(() -> userService.lockUser(createdUser.getId()));
        UserResponse lockedUser = userService.getUserById(createdUser.getId());
        assertEquals(UserStatus.LOCKED, lockedUser.getStatus());

        // When & Then - Test activate
        assertDoesNotThrow(() -> userService.activateUser(createdUser.getId()));
        UserResponse activatedUser = userService.getUserById(createdUser.getId());
        assertEquals(UserStatus.ACTIVE, activatedUser.getStatus());
    }

    @Test
    void testUserQuery() {
        // Given - Create multiple users
        UserCreateRequest user1 = new UserCreateRequest();
        user1.setUsername("querytest1");
        user1.setPassword("password123");
        user1.setEmail("<EMAIL>");
        user1.setPhone("13800139003");
        user1.setRealName("查询测试用户1");
        user1.setDepartmentId(1L);

        UserCreateRequest user2 = new UserCreateRequest();
        user2.setUsername("querytest2");
        user2.setPassword("password123");
        user2.setEmail("<EMAIL>");
        user2.setPhone("13800139004");
        user2.setRealName("查询测试用户2");
        user2.setDepartmentId(2L);

        userService.createUser(user1);
        userService.createUser(user2);

        // When - Query by username
        UserQueryRequest queryRequest = new UserQueryRequest();
        queryRequest.setUsername("querytest");
        queryRequest.setPageNum(1);
        queryRequest.setPageSize(10);

        IPage<UserResponse> result = userService.getUserPage(queryRequest);

        // Then
        assertNotNull(result);
        assertTrue(result.getTotal() >= 2);
        assertTrue(result.getRecords().stream().anyMatch(user -> user.getUsername().equals("querytest1")));
        assertTrue(result.getRecords().stream().anyMatch(user -> user.getUsername().equals("querytest2")));
    }

    @Test
    void testGetUsersByDepartment() {
        // Given - Create users in different departments
        UserCreateRequest user1 = new UserCreateRequest();
        user1.setUsername("depttest1");
        user1.setPassword("password123");
        user1.setEmail("<EMAIL>");
        user1.setPhone("13800139005");
        user1.setRealName("部门测试用户1");
        user1.setDepartmentId(1L);

        UserCreateRequest user2 = new UserCreateRequest();
        user2.setUsername("depttest2");
        user2.setPassword("password123");
        user2.setEmail("<EMAIL>");
        user2.setPhone("13800139006");
        user2.setRealName("部门测试用户2");
        user2.setDepartmentId(1L);

        UserCreateRequest user3 = new UserCreateRequest();
        user3.setUsername("depttest3");
        user3.setPassword("password123");
        user3.setEmail("<EMAIL>");
        user3.setPhone("13800139007");
        user3.setRealName("部门测试用户3");
        user3.setDepartmentId(2L);

        userService.createUser(user1);
        userService.createUser(user2);
        userService.createUser(user3);

        // When
        List<UserResponse> dept1Users = userService.getUsersByDepartmentId(1L);
        List<UserResponse> dept2Users = userService.getUsersByDepartmentId(2L);

        // Then
        assertTrue(dept1Users.size() >= 2);
        assertTrue(dept2Users.size() >= 1);

        assertTrue(dept1Users.stream().allMatch(user -> user.getDepartmentId().equals(1L)));
        assertTrue(dept2Users.stream().allMatch(user -> user.getDepartmentId().equals(2L)));
    }

    @Test
    void testBatchOperations() {
        // Given - Create multiple users
        UserCreateRequest user1 = new UserCreateRequest();
        user1.setUsername("batchtest1");
        user1.setPassword("password123");
        user1.setEmail("<EMAIL>");
        user1.setPhone("13800139008");
        user1.setRealName("批量测试用户1");
        user1.setDepartmentId(1L);

        UserCreateRequest user2 = new UserCreateRequest();
        user2.setUsername("batchtest2");
        user2.setPassword("password123");
        user2.setEmail("<EMAIL>");
        user2.setPhone("13800139009");
        user2.setRealName("批量测试用户2");
        user2.setDepartmentId(1L);

        UserResponse createdUser1 = userService.createUser(user1);
        UserResponse createdUser2 = userService.createUser(user2);

        List<Long> userIds = Arrays.asList(createdUser1.getId(), createdUser2.getId());

        // When - Batch update status
        assertDoesNotThrow(() -> userService.batchUpdateUserStatus(userIds, "INACTIVE"));

        // Then - Verify status updated
        UserResponse updatedUser1 = userService.getUserById(createdUser1.getId());
        UserResponse updatedUser2 = userService.getUserById(createdUser2.getId());

        assertEquals(UserStatus.INACTIVE, updatedUser1.getStatus());
        assertEquals(UserStatus.INACTIVE, updatedUser2.getStatus());

        // When - Batch delete
        assertDoesNotThrow(() -> userService.batchDeleteUsers(userIds));

        // Then - Verify users deleted
        assertThrows(BusinessException.class, () -> userService.getUserById(createdUser1.getId()));
        assertThrows(BusinessException.class, () -> userService.getUserById(createdUser2.getId()));
    }

    @Test
    void testUniqueConstraints() {
        // Given - Create a user
        UserCreateRequest createRequest = new UserCreateRequest();
        createRequest.setUsername("uniquetest");
        createRequest.setPassword("password123");
        createRequest.setEmail("<EMAIL>");
        createRequest.setPhone("***********");
        createRequest.setRealName("唯一性测试用户");
        createRequest.setDepartmentId(1L);

        userService.createUser(createRequest);

        // When & Then - Test username uniqueness
        assertTrue(userService.isUsernameExists("uniquetest", null));
        assertFalse(userService.isUsernameExists("nonexistent", null));

        // When & Then - Test email uniqueness
        assertTrue(userService.isEmailExists("<EMAIL>", null));
        assertFalse(userService.isEmailExists("<EMAIL>", null));

        // When & Then - Test phone uniqueness
        assertTrue(userService.isPhoneExists("***********", null));
        assertFalse(userService.isPhoneExists("13800139999", null));
    }
}
