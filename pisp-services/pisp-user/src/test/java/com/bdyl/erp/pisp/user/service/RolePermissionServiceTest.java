package com.bdyl.erp.pisp.user.service;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.bdyl.erp.pisp.user.entity.RolePermission;
import com.bdyl.erp.pisp.user.mapper.RolePermissionMapper;
import com.bdyl.erp.pisp.user.service.impl.RolePermissionServiceImpl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * 角色权限关联服务测试类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class RolePermissionServiceTest {

    /**
     * 角色权限关联数据访问层Mock对象
     */
    @Mock
    private RolePermissionMapper rolePermissionMapper;

    /**
     * 角色权限关联服务实现对象
     */
    @InjectMocks
    private RolePermissionServiceImpl rolePermissionService;

    /**
     * 测试用角色权限关联对象
     */
    private RolePermission testRolePermission;

    @BeforeEach
    void setUp() throws Exception {
        // 初始化测试数据
        testRolePermission = new RolePermission();
        testRolePermission.setId(1L);
        testRolePermission.setRoleId(1L);
        testRolePermission.setPermissionId(1L);
        testRolePermission.setCreateTime(LocalDateTime.now());

        // 设置baseMapper
        setBaseMapper();
    }

    private void setBaseMapper() throws Exception {
        Class<?> serviceImplClass = rolePermissionService.getClass().getSuperclass();
        Field[] fields = serviceImplClass.getDeclaredFields();

        for (Field field : fields) {
            if (field.getType().isAssignableFrom(RolePermissionMapper.class)) {
                field.setAccessible(true);
                field.set(rolePermissionService, rolePermissionMapper);
                return;
            }
        }

        // 尝试父类的父类
        Class<?> parentClass = serviceImplClass.getSuperclass();
        if (parentClass != null) {
            Field[] parentFields = parentClass.getDeclaredFields();
            for (Field field : parentFields) {
                if (field.getType().isAssignableFrom(RolePermissionMapper.class)
                    || field.getName().contains("mapper")) {
                    field.setAccessible(true);
                    field.set(rolePermissionService, rolePermissionMapper);
                    return;
                }
            }
        }
    }

    @Test
    void testRemoveByRoleId() {
        // Given
        Long roleId = 1L;

        // When
        rolePermissionService.removeByRoleId(roleId);

        // Then
        verify(rolePermissionMapper).deleteByRoleId(roleId);
    }

    @Test
    void testRemoveByPermissionId() {
        // Given
        Long permissionId = 1L;

        // When
        rolePermissionService.removeByPermissionId(permissionId);

        // Then
        verify(rolePermissionMapper).deleteByPermissionId(permissionId);
    }

    @Test
    void testRemoveByRoleIdAndPermissionIds() {
        // Given
        Long roleId = 1L;
        List<Long> permissionIds = Arrays.asList(1L, 2L, 3L);

        // When
        rolePermissionService.removeByRoleIdAndPermissionIds(roleId, permissionIds);

        // Then
        verify(rolePermissionMapper).deleteByRoleIdAndPermissionIds(roleId, permissionIds);
    }

    @Test
    void testGetByRoleId() {
        // Given
        Long roleId = 1L;
        List<RolePermission> rolePermissions = Arrays.asList(testRolePermission);
        when(rolePermissionMapper.selectByRoleId(roleId)).thenReturn(rolePermissions);

        // When
        List<RolePermission> result = rolePermissionService.getByRoleId(roleId);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testRolePermission.getRoleId(), result.get(0).getRoleId());
        verify(rolePermissionMapper).selectByRoleId(roleId);
    }

    @Test
    void testGetByPermissionId() {
        // Given
        Long permissionId = 1L;
        List<RolePermission> rolePermissions = Arrays.asList(testRolePermission);
        when(rolePermissionMapper.selectByPermissionId(permissionId)).thenReturn(rolePermissions);

        // When
        List<RolePermission> result = rolePermissionService.getByPermissionId(permissionId);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testRolePermission.getPermissionId(), result.get(0).getPermissionId());
        verify(rolePermissionMapper).selectByPermissionId(permissionId);
    }

    @Test
    void testExistsTrue() {
        // Given
        Long roleId = 1L;
        Long permissionId = 1L;
        when(rolePermissionMapper.checkRolePermissionExists(roleId, permissionId)).thenReturn(1);

        // When
        boolean result = rolePermissionService.exists(roleId, permissionId);

        // Then
        assertTrue(result);
        verify(rolePermissionMapper).checkRolePermissionExists(roleId, permissionId);
    }

    @Test
    void testExistsFalse() {
        // Given
        Long roleId = 1L;
        Long permissionId = 1L;
        when(rolePermissionMapper.checkRolePermissionExists(roleId, permissionId)).thenReturn(0);

        // When
        boolean result = rolePermissionService.exists(roleId, permissionId);

        // Then
        assertFalse(result);
        verify(rolePermissionMapper).checkRolePermissionExists(roleId, permissionId);
    }

    @Test
    void testBatchInsert() {
        // Given
        List<RolePermission> rolePermissions = Arrays.asList(testRolePermission);

        // When
        rolePermissionService.batchInsert(rolePermissions);

        // Then
        verify(rolePermissionMapper).batchInsert(rolePermissions);
    }
}
