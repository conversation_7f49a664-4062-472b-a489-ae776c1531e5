package com.bdyl.erp.pisp.user.service;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.bdyl.erp.pisp.common.core.exception.BusinessException;
import com.bdyl.erp.pisp.common.core.result.ResponseCode;
import com.bdyl.erp.pisp.common.security.context.UserContext;
import com.bdyl.erp.pisp.common.security.context.UserContextHolder;
import com.bdyl.erp.pisp.user.dto.request.RoleCreateRequest;
import com.bdyl.erp.pisp.user.dto.request.RoleQueryRequest;
import com.bdyl.erp.pisp.user.dto.request.RoleUpdateRequest;
import com.bdyl.erp.pisp.user.dto.response.RoleResponse;
import com.bdyl.erp.pisp.user.entity.Role;
import com.bdyl.erp.pisp.user.entity.UserRole;
import com.bdyl.erp.pisp.user.enums.RoleStatus;
import com.bdyl.erp.pisp.user.mapper.RoleMapper;
import com.bdyl.erp.pisp.user.mapper.RolePermissionMapper;
import com.bdyl.erp.pisp.user.mapper.UserRoleMapper;
import com.bdyl.erp.pisp.user.service.impl.RoleServiceImpl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * 角色服务测试类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class RoleServiceTest {

    /**
     * 角色数据访问层Mock对象
     */
    @Mock
    private RoleMapper roleMapper;

    /**
     * 角色权限关联数据访问层Mock对象
     */
    @Mock
    private RolePermissionMapper rolePermissionMapper;

    /**
     * 用户角色关联数据访问层Mock对象
     */
    @Mock
    private UserRoleMapper userRoleMapper;

    /**
     * 角色服务实现对象
     */
    @InjectMocks
    private RoleServiceImpl roleService;

    /**
     * 测试用角色对象
     */
    private Role testRole;

    /**
     * 角色创建请求对象
     */
    private RoleCreateRequest createRequest;

    /**
     * 角色更新请求对象
     */
    private RoleUpdateRequest updateRequest;

    /**
     * 角色查询请求对象
     */
    private RoleQueryRequest queryRequest;

    @BeforeEach
    void setUp() throws Exception {

        // 设置测试用户上下文
        UserContext userContext = new UserContext();
        userContext.setUserId(1L);
        userContext.setUsername("test_user");
        userContext.setTenantId(1L);
        UserContextHolder.setContext(userContext);

        // 初始化测试数据
        testRole = new Role();
        testRole.setId(1L);
        testRole.setRoleName("测试角色");
        testRole.setRoleCode("TEST_ROLE");
        testRole.setDescription("测试角色描述");
        testRole.setStatus(RoleStatus.ACTIVE);
        testRole.setIsSystem(false);
        testRole.setSortOrder(1);
        testRole.setCreateTime(LocalDateTime.now());
        testRole.setVersion(0);

        createRequest = new RoleCreateRequest();
        createRequest.setRoleName("新角色");
        createRequest.setRoleCode("NEW_ROLE");
        createRequest.setDescription("新角色描述");
        createRequest.setStatus(RoleStatus.ACTIVE);
        createRequest.setSortOrder(2);

        updateRequest = new RoleUpdateRequest();
        updateRequest.setId(1L);
        updateRequest.setRoleName("更新角色");
        updateRequest.setRoleCode("UPDATED_ROLE");
        updateRequest.setDescription("更新后的角色描述");
        updateRequest.setStatus(RoleStatus.ACTIVE);
        updateRequest.setSortOrder(3);
        updateRequest.setVersion(0);

        queryRequest = new RoleQueryRequest();
        queryRequest.setPageNum(1);
        queryRequest.setPageSize(10);
        queryRequest.setRoleName("测试");
        queryRequest.setStatus("ACTIVE");

        // 设置baseMapper
        setBaseMapper();
    }

    @AfterEach
    void tearDown() {
        // 清理用户上下文 - 事务回滚时自动清理
        UserContextHolder.clear();
    }

    private void setBaseMapper() throws Exception {
        Class<?> serviceImplClass = roleService.getClass().getSuperclass();
        Field[] fields = serviceImplClass.getDeclaredFields();

        for (Field field : fields) {
            if (field.getType().isAssignableFrom(RoleMapper.class)) {
                field.setAccessible(true);
                field.set(roleService, roleMapper);
                return;
            }
        }

        // 尝试父类的父类
        Class<?> parentClass = serviceImplClass.getSuperclass();
        if (parentClass != null) {
            Field[] parentFields = parentClass.getDeclaredFields();
            for (Field field : parentFields) {
                if (field.getType().isAssignableFrom(RoleMapper.class) || field.getName().contains("mapper")) {
                    field.setAccessible(true);
                    field.set(roleService, roleMapper);
                    return;
                }
            }
        }
    }

    @Test
    void testCreateRole() {
        // Given
        when(roleMapper.checkRoleCodeExists(createRequest.getRoleCode(), null)).thenReturn(0);
        when(roleMapper.checkRoleNameExists(createRequest.getRoleName(), null)).thenReturn(0);
        when(userRoleMapper.selectByRoleId(any())).thenReturn(Arrays.asList());

        // When
        RoleResponse result = roleService.createRole(createRequest);

        // Then
        assertNotNull(result);
        assertEquals(createRequest.getRoleName(), result.getRoleName());
        assertEquals(createRequest.getRoleCode(), result.getRoleCode());
        verify(roleMapper).checkRoleCodeExists(createRequest.getRoleCode(), null);
        verify(roleMapper).checkRoleNameExists(createRequest.getRoleName(), null);
    }

    @Test
    void testCreateRoleWithDuplicateCode() {
        // Given
        when(roleMapper.checkRoleCodeExists(createRequest.getRoleCode(), null)).thenReturn(1);

        // When & Then
        BusinessException exception =
            assertThrows(BusinessException.class, () -> roleService.createRole(createRequest));
        assertEquals(ResponseCode.ROLE_CODE_EXISTS.getCode(), exception.getCode());
        verify(roleMapper).checkRoleCodeExists(createRequest.getRoleCode(), null);
    }

    @Test
    void testCreateRoleWithDuplicateName() {
        // Given
        when(roleMapper.checkRoleCodeExists(createRequest.getRoleCode(), null)).thenReturn(0);
        when(roleMapper.checkRoleNameExists(createRequest.getRoleName(), null)).thenReturn(1);

        // When & Then
        BusinessException exception =
            assertThrows(BusinessException.class, () -> roleService.createRole(createRequest));
        assertEquals(ResponseCode.ROLE_NAME_EXISTS.getCode(), exception.getCode());
        verify(roleMapper).checkRoleNameExists(createRequest.getRoleName(), null);
    }

    @Test
    void testGetRoleById() {
        // Given
        when(roleMapper.selectByIdWithPermissions(1L)).thenReturn(testRole);
        when(userRoleMapper.selectByRoleId(1L)).thenReturn(Arrays.asList());

        // When
        RoleResponse result = roleService.getRoleById(1L);

        // Then
        assertNotNull(result);
        assertEquals(testRole.getRoleName(), result.getRoleName());
        assertEquals(testRole.getRoleCode(), result.getRoleCode());
        verify(roleMapper).selectByIdWithPermissions(1L);
    }

    @Test
    void testGetRoleByIdNotFound() {
        // Given
        when(roleMapper.selectByIdWithPermissions(1L)).thenReturn(null);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> roleService.getRoleById(1L));
        assertEquals(ResponseCode.ROLE_NOT_FOUND.getCode(), exception.getCode());
        verify(roleMapper).selectByIdWithPermissions(1L);
    }

    @Test
    void testGetRoleByCode() {
        // Given
        when(roleMapper.selectByRoleCode("TEST_ROLE")).thenReturn(testRole);
        when(userRoleMapper.selectByRoleId(1L)).thenReturn(Arrays.asList());

        // When
        RoleResponse result = roleService.getRoleByCode("TEST_ROLE");

        // Then
        assertNotNull(result);
        assertEquals(testRole.getRoleName(), result.getRoleName());
        assertEquals(testRole.getRoleCode(), result.getRoleCode());
        verify(roleMapper).selectByRoleCode("TEST_ROLE");
    }

    @Test
    void testDeleteRole() {
        // Given
        when(roleMapper.selectById(1L)).thenReturn(testRole);
        when(userRoleMapper.selectByRoleId(1L)).thenReturn(Arrays.asList());

        // When
        roleService.deleteRole(1L);

        // Then
        verify(roleMapper).selectById(1L);
        verify(userRoleMapper).selectByRoleId(1L);
        verify(rolePermissionMapper).deleteByRoleId(1L);
    }

    @Test
    void testDeleteRoleNotFound() {
        // Given
        when(roleMapper.selectById(1L)).thenReturn(null);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> roleService.deleteRole(1L));
        assertEquals(ResponseCode.ROLE_NOT_FOUND.getCode(), exception.getCode());
        verify(roleMapper).selectById(1L);
    }

    @Test
    void testDeleteSystemRole() {
        // Given
        testRole.setIsSystem(true);
        when(roleMapper.selectById(1L)).thenReturn(testRole);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> roleService.deleteRole(1L));
        assertEquals(ResponseCode.SYSTEM_ROLE_CANNOT_DELETE.getCode(), exception.getCode());
        verify(roleMapper).selectById(1L);
    }

    @Test
    void testDeleteRoleWithUsers() {
        // Given
        UserRole userRole = new UserRole();
        userRole.setUserId(1L);
        userRole.setRoleId(1L);
        when(roleMapper.selectById(1L)).thenReturn(testRole);
        when(userRoleMapper.selectByRoleId(1L)).thenReturn(Arrays.asList(userRole));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> roleService.deleteRole(1L));
        assertEquals(ResponseCode.ROLE_HAS_USERS.getCode(), exception.getCode());
        verify(roleMapper).selectById(1L);
        verify(userRoleMapper).selectByRoleId(1L);
    }

    @Test
    void testActivateRole() {
        // Given
        when(roleMapper.selectById(1L)).thenReturn(testRole);

        // When
        roleService.activateRole(1L);

        // Then
        verify(roleMapper).selectById(1L);
    }

    @Test
    void testDeactivateRole() {
        // Given
        when(roleMapper.selectById(1L)).thenReturn(testRole);

        // When
        roleService.deactivateRole(1L);

        // Then
        verify(roleMapper).selectById(1L);
    }

    @Test
    void testDeactivateSystemRole() {
        // Given
        testRole.setIsSystem(true);
        when(roleMapper.selectById(1L)).thenReturn(testRole);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> roleService.deactivateRole(1L));
        assertEquals(ResponseCode.SYSTEM_ROLE_CANNOT_DISABLE.getCode(), exception.getCode());
        verify(roleMapper).selectById(1L);
    }

    @Test
    void testBatchUpdateRoleStatus() {
        // Given
        List<Long> roleIds = Arrays.asList(1L, 2L, 3L);
        String status = "INACTIVE";

        // When
        roleService.batchUpdateRoleStatus(roleIds, status);

        // Then
        verify(roleMapper).batchUpdateStatus(eq(roleIds), eq(status), anyLong());
    }

    @Test
    void testIsRoleNameExists() {
        // Given
        when(roleMapper.checkRoleNameExists("测试角色", null)).thenReturn(1);

        // When
        boolean result = roleService.isRoleNameExists("测试角色", null);

        // Then
        assertTrue(result);
        verify(roleMapper).checkRoleNameExists("测试角色", null);
    }

    @Test
    void testIsRoleNameNotExists() {
        // Given
        when(roleMapper.checkRoleNameExists("不存在的角色", null)).thenReturn(0);

        // When
        boolean result = roleService.isRoleNameExists("不存在的角色", null);

        // Then
        assertFalse(result);
        verify(roleMapper).checkRoleNameExists("不存在的角色", null);
    }

    @Test
    void testIsRoleCodeExists() {
        // Given
        when(roleMapper.checkRoleCodeExists("TEST_ROLE", null)).thenReturn(1);

        // When
        boolean result = roleService.isRoleCodeExists("TEST_ROLE", null);

        // Then
        assertTrue(result);
        verify(roleMapper).checkRoleCodeExists("TEST_ROLE", null);
    }

    @Test
    void testIsRoleCodeNotExists() {
        // Given
        when(roleMapper.checkRoleCodeExists("NOT_EXISTS", null)).thenReturn(0);

        // When
        boolean result = roleService.isRoleCodeExists("NOT_EXISTS", null);

        // Then
        assertFalse(result);
        verify(roleMapper).checkRoleCodeExists("NOT_EXISTS", null);
    }

    @Test
    void testGetRoleUserCount() {
        // Given
        UserRole userRole = new UserRole();
        userRole.setUserId(1L);
        userRole.setRoleId(1L);
        when(userRoleMapper.selectByRoleId(1L)).thenReturn(Arrays.asList(userRole));

        // When
        int result = roleService.getRoleUserCount(1L);

        // Then
        assertEquals(1, result);
        verify(userRoleMapper).selectByRoleId(1L);
    }

    @Test
    void testUpdateRole() {
        // Given
        when(roleMapper.selectById(1L)).thenReturn(testRole);
        when(roleMapper.checkRoleCodeExists(updateRequest.getRoleCode(), 1L)).thenReturn(0);
        when(roleMapper.checkRoleNameExists(updateRequest.getRoleName(), 1L)).thenReturn(0);
        when(userRoleMapper.selectByRoleId(1L)).thenReturn(Arrays.asList());

        // When
        RoleResponse result = roleService.updateRole(updateRequest);

        // Then
        assertNotNull(result);
        assertEquals(updateRequest.getRoleName(), result.getRoleName());
        assertEquals(updateRequest.getRoleCode(), result.getRoleCode());
        verify(roleMapper).selectById(1L);
        verify(roleMapper).checkRoleCodeExists(updateRequest.getRoleCode(), 1L);
        verify(roleMapper).checkRoleNameExists(updateRequest.getRoleName(), 1L);
    }

    @Test
    void testUpdateRoleNotFound() {
        // Given
        when(roleMapper.selectById(1L)).thenReturn(null);

        // When & Then
        BusinessException exception =
            assertThrows(BusinessException.class, () -> roleService.updateRole(updateRequest));
        assertEquals(ResponseCode.ROLE_NOT_FOUND.getCode(), exception.getCode());
        verify(roleMapper).selectById(1L);
    }

    @Test
    void testUpdateSystemRole() {
        // Given
        testRole.setIsSystem(true);
        when(roleMapper.selectById(1L)).thenReturn(testRole);

        // When & Then
        BusinessException exception =
            assertThrows(BusinessException.class, () -> roleService.updateRole(updateRequest));
        assertEquals(ResponseCode.SYSTEM_ROLE_CANNOT_MODIFY.getCode(), exception.getCode());
        verify(roleMapper).selectById(1L);
    }

    @Test
    void testGetRolesByUserId() {
        // Given
        List<Role> roles = Arrays.asList(testRole);
        when(roleMapper.selectByUserId(1L)).thenReturn(roles);
        when(userRoleMapper.selectByRoleId(1L)).thenReturn(Arrays.asList());

        // When
        List<RoleResponse> result = roleService.getRolesByUserId(1L);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testRole.getRoleName(), result.get(0).getRoleName());
        verify(roleMapper).selectByUserId(1L);
    }

    @Test
    @SuppressWarnings("unchecked")
    void testGetRolePage() {
        // Given
        RoleQueryRequest request = new RoleQueryRequest();
        request.setPageNum(1);
        request.setPageSize(10);
        request.setRoleName("测试");
        request.setStatus("ACTIVE");

        Page<Role> page = new Page<>();
        page.setRecords(Arrays.asList(testRole));
        page.setTotal(1L);
        page.setCurrent(1L);
        page.setSize(10L);

        when(roleMapper.selectRolePage(any(Page.class), any(), any(), any(), any())).thenReturn(page);
        when(userRoleMapper.selectByRoleId(1L)).thenReturn(Arrays.asList());

        // When
        IPage<RoleResponse> result = roleService.getRolePage(request);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getRecords().size());
        assertEquals(testRole.getRoleName(), result.getRecords().get(0).getRoleName());
        verify(roleMapper).selectRolePage(any(Page.class), any(), any(), any(), any());
    }
}
