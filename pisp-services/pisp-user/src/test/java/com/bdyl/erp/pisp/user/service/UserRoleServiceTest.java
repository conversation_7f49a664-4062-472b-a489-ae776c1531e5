package com.bdyl.erp.pisp.user.service;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.bdyl.erp.pisp.user.entity.UserRole;
import com.bdyl.erp.pisp.user.mapper.UserRoleMapper;
import com.bdyl.erp.pisp.user.service.impl.UserRoleServiceImpl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * 用户角色关联服务测试类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class UserRoleServiceTest {

    /**
     * 用户角色关联数据访问层Mock对象
     */
    @Mock
    private UserRoleMapper userRoleMapper;

    /**
     * 用户角色关联服务实现对象
     */
    @InjectMocks
    private UserRoleServiceImpl userRoleService;

    /**
     * 测试用用户角色关联对象
     */
    private UserRole testUserRole;

    @BeforeEach
    void setUp() throws Exception {
        // 初始化测试数据
        testUserRole = new UserRole();
        testUserRole.setId(1L);
        testUserRole.setUserId(1L);
        testUserRole.setRoleId(1L);
        testUserRole.setCreateTime(LocalDateTime.now());

        // 设置baseMapper
        setBaseMapper();
    }

    private void setBaseMapper() throws Exception {
        Class<?> serviceImplClass = userRoleService.getClass().getSuperclass();
        Field[] fields = serviceImplClass.getDeclaredFields();

        for (Field field : fields) {
            if (field.getType().isAssignableFrom(UserRoleMapper.class)) {
                field.setAccessible(true);
                field.set(userRoleService, userRoleMapper);
                return;
            }
        }

        // 尝试父类的父类
        Class<?> parentClass = serviceImplClass.getSuperclass();
        if (parentClass != null) {
            Field[] parentFields = parentClass.getDeclaredFields();
            for (Field field : parentFields) {
                if (field.getType().isAssignableFrom(UserRoleMapper.class) || field.getName().contains("mapper")) {
                    field.setAccessible(true);
                    field.set(userRoleService, userRoleMapper);
                    return;
                }
            }
        }
    }

    @Test
    void testRemoveByUserId() {
        // Given
        Long userId = 1L;

        // When
        userRoleService.removeByUserId(userId);

        // Then
        verify(userRoleMapper).deleteByUserId(userId);
    }

    @Test
    void testRemoveByRoleId() {
        // Given
        Long roleId = 1L;

        // When
        userRoleService.removeByRoleId(roleId);

        // Then
        verify(userRoleMapper).deleteByRoleId(roleId);
    }

    @Test
    void testRemoveByUserIdAndRoleIds() {
        // Given
        Long userId = 1L;
        List<Long> roleIds = Arrays.asList(1L, 2L, 3L);

        // When
        userRoleService.removeByUserIdAndRoleIds(userId, roleIds);

        // Then
        verify(userRoleMapper).deleteByUserIdAndRoleIds(userId, roleIds);
    }

    @Test
    void testGetByUserId() {
        // Given
        Long userId = 1L;
        List<UserRole> userRoles = Arrays.asList(testUserRole);
        when(userRoleMapper.selectByUserId(userId)).thenReturn(userRoles);

        // When
        List<UserRole> result = userRoleService.getByUserId(userId);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testUserRole.getUserId(), result.get(0).getUserId());
        verify(userRoleMapper).selectByUserId(userId);
    }

    @Test
    void testGetByRoleId() {
        // Given
        Long roleId = 1L;
        List<UserRole> userRoles = Arrays.asList(testUserRole);
        when(userRoleMapper.selectByRoleId(roleId)).thenReturn(userRoles);

        // When
        List<UserRole> result = userRoleService.getByRoleId(roleId);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testUserRole.getRoleId(), result.get(0).getRoleId());
        verify(userRoleMapper).selectByRoleId(roleId);
    }

    @Test
    void testExistsTrue() {
        // Given
        Long userId = 1L;
        Long roleId = 1L;
        when(userRoleMapper.checkUserRoleExists(userId, roleId)).thenReturn(1);

        // When
        boolean result = userRoleService.exists(userId, roleId);

        // Then
        assertTrue(result);
        verify(userRoleMapper).checkUserRoleExists(userId, roleId);
    }

    @Test
    void testExistsFalse() {
        // Given
        Long userId = 1L;
        Long roleId = 1L;
        when(userRoleMapper.checkUserRoleExists(userId, roleId)).thenReturn(0);

        // When
        boolean result = userRoleService.exists(userId, roleId);

        // Then
        assertFalse(result);
        verify(userRoleMapper).checkUserRoleExists(userId, roleId);
    }
}
