package com.bdyl.erp.pisp.user.service;

import java.time.LocalDateTime;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import org.springframework.security.crypto.password.PasswordEncoder;

import com.bdyl.erp.pisp.user.dto.request.UserCreateRequest;
import com.bdyl.erp.pisp.user.dto.response.UserResponse;
import com.bdyl.erp.pisp.user.entity.User;
import com.bdyl.erp.pisp.user.enums.UserStatus;
import com.bdyl.erp.pisp.user.mapper.UserMapper;
import com.bdyl.erp.pisp.user.service.impl.UserServiceImpl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * 用户服务简单测试类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class UserServiceSimpleTest {
    /**
     * 用户数据访问层Mock对象
     */
    @Mock
    private UserMapper userMapper;
    /**
     * 密码编码器Mock对象
     */
    @Mock
    private PasswordEncoder passwordEncoder;
    /**
     * 用户服务对象
     */
    @InjectMocks
    private UserServiceImpl userService;
    /**
     * 测试用户对象
     */
    private User testUser;
    /**
     * 用户创建请求对象
     */
    private UserCreateRequest createRequest;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testUser = new User();
        testUser.setId(1L);
        testUser.setUsername("testuser");
        testUser.setEmail("<EMAIL>");
        testUser.setPhone("13800138000");
        testUser.setRealName("测试用户");
        testUser.setStatus(UserStatus.ACTIVE);
        testUser.setDepartmentId(1L);
        testUser.setCreateTime(LocalDateTime.now());

        createRequest = new UserCreateRequest();
        createRequest.setUsername("newuser");
        createRequest.setPassword("password123");
        createRequest.setEmail("<EMAIL>");
        createRequest.setPhone("13800138001");
        createRequest.setRealName("新用户");
        createRequest.setDepartmentId(1L);
    }

    @Test
    void testPasswordEncoding() {
        // Given
        String rawPassword = "password123";
        String encodedPassword = "encodedPassword";
        when(passwordEncoder.encode(rawPassword)).thenReturn(encodedPassword);

        // When
        String result = passwordEncoder.encode(rawPassword);

        // Then
        assertEquals(encodedPassword, result);
        verify(passwordEncoder).encode(rawPassword);
    }

    @Test
    void testPasswordMatching() {
        // Given
        String rawPassword = "password123";
        String encodedPassword = "encodedPassword";
        when(passwordEncoder.matches(rawPassword, encodedPassword)).thenReturn(true);

        // When
        boolean result = passwordEncoder.matches(rawPassword, encodedPassword);

        // Then
        assertTrue(result);
        verify(passwordEncoder).matches(rawPassword, encodedPassword);
    }

    @Test
    void testUserEntityCreation() {
        // Given
        User user = new User();
        user.setUsername("testuser");
        user.setEmail("<EMAIL>");
        user.setRealName("测试用户");
        user.setStatus(UserStatus.ACTIVE);

        // Then
        assertEquals("testuser", user.getUsername());
        assertEquals("<EMAIL>", user.getEmail());
        assertEquals("测试用户", user.getRealName());
        assertEquals(UserStatus.ACTIVE, user.getStatus());
    }

    @Test
    void testUserCreateRequestValidation() {
        // Given
        UserCreateRequest request = new UserCreateRequest();
        request.setUsername("newuser");
        request.setPassword("password123");
        request.setEmail("<EMAIL>");
        request.setRealName("新用户");

        // Then
        assertNotNull(request.getUsername());
        assertNotNull(request.getPassword());
        assertNotNull(request.getEmail());
        assertNotNull(request.getRealName());
        assertEquals("newuser", request.getUsername());
        assertEquals("password123", request.getPassword());
        assertEquals("<EMAIL>", request.getEmail());
        assertEquals("新用户", request.getRealName());
    }

    @Test
    void testUserStatusEnum() {
        // Test all user status values
        assertEquals("ACTIVE", UserStatus.ACTIVE.getCode());
        assertEquals("激活", UserStatus.ACTIVE.getDescription());

        assertEquals("INACTIVE", UserStatus.INACTIVE.getCode());
        assertEquals("非激活", UserStatus.INACTIVE.getDescription());

        assertEquals("LOCKED", UserStatus.LOCKED.getCode());
        assertEquals("锁定", UserStatus.LOCKED.getDescription());
    }

    @Test
    void testConvertToResponse() {
        // Given
        User user = new User();
        user.setId(1L);
        user.setUsername("testuser");
        user.setEmail("<EMAIL>");
        user.setRealName("测试用户");
        user.setStatus(UserStatus.ACTIVE);

        // When
        UserResponse response = new UserResponse();
        response.setId(user.getId());
        response.setUsername(user.getUsername());
        response.setEmail(user.getEmail());
        response.setRealName(user.getRealName());
        response.setStatus(user.getStatus());

        // Then
        assertEquals(user.getId(), response.getId());
        assertEquals(user.getUsername(), response.getUsername());
        assertEquals(user.getEmail(), response.getEmail());
        assertEquals(user.getRealName(), response.getRealName());
        assertEquals(user.getStatus(), response.getStatus());
    }

    @Test
    void testUserServiceNotNull() {
        // Verify that the service is properly injected
        assertNotNull(userService);
        assertNotNull(userMapper);
        assertNotNull(passwordEncoder);
    }
}
