package com.bdyl.erp.pisp.user.service;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import org.springframework.security.crypto.password.PasswordEncoder;

import com.bdyl.erp.pisp.common.core.exception.BusinessException;
import com.bdyl.erp.pisp.user.dto.request.PasswordChangeRequest;
import com.bdyl.erp.pisp.user.dto.request.UserCreateRequest;
import com.bdyl.erp.pisp.user.dto.request.UserUpdateRequest;
import com.bdyl.erp.pisp.user.dto.response.UserResponse;
import com.bdyl.erp.pisp.user.entity.User;
import com.bdyl.erp.pisp.user.enums.UserStatus;
import com.bdyl.erp.pisp.user.mapper.UserMapper;
import com.bdyl.erp.pisp.user.service.impl.UserServiceImpl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * 用户服务测试类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class UserServiceTest {
    /**
     * 用户数据访问层Mock对象
     */
    @Mock
    private UserMapper userMapper;
    /**
     * 密码编码器Mock对象
     */
    @Mock
    private PasswordEncoder passwordEncoder;
    /**
     * 用户服务对象
     */
    @InjectMocks
    private UserServiceImpl userService;
    /**
     * 测试用户对象
     */
    private User testUser;
    /**
     * 用户创建请求对象
     */
    private UserCreateRequest createRequest;
    /**
     * 用户更新请求对象
     */
    private UserUpdateRequest updateRequest;

    @BeforeEach
    void setUp() throws Exception {
        // 初始化测试数据
        testUser = new User();
        testUser.setId(1L);
        testUser.setUsername("testuser");
        testUser.setEmail("<EMAIL>");
        testUser.setPhone("***********");
        testUser.setRealName("测试用户");
        testUser.setStatus(UserStatus.ACTIVE);
        testUser.setDepartmentId(1L);
        testUser.setCreateTime(LocalDateTime.now());

        createRequest = new UserCreateRequest();
        createRequest.setUsername("newuser");
        createRequest.setPassword("password123");
        createRequest.setEmail("<EMAIL>");
        createRequest.setPhone("13800138001");
        createRequest.setRealName("新用户");
        createRequest.setDepartmentId(1L);

        updateRequest = new UserUpdateRequest();
        updateRequest.setId(1L);
        updateRequest.setEmail("<EMAIL>");
        updateRequest.setPhone("13800138002");
        updateRequest.setRealName("更新用户");
        updateRequest.setDepartmentId(2L);

        // 使用反射设置baseMapper
        setBaseMapper();
    }

    private void setBaseMapper() throws Exception {
        // 尝试不同的字段名
        Class<?> serviceImplClass = userService.getClass().getSuperclass();
        Field[] fields = serviceImplClass.getDeclaredFields();

        for (Field field : fields) {
            if (field.getType().isAssignableFrom(UserMapper.class)) {
                field.setAccessible(true);
                field.set(userService, userMapper);
                return;
            }
        }

        // 如果没找到，尝试父类的父类
        Class<?> parentClass = serviceImplClass.getSuperclass();
        if (parentClass != null) {
            Field[] parentFields = parentClass.getDeclaredFields();
            for (Field field : parentFields) {
                if (field.getType().isAssignableFrom(UserMapper.class) || field.getName().contains("mapper")
                    || field.getName().contains("Mapper")) {
                    field.setAccessible(true);
                    field.set(userService, userMapper);
                    return;
                }
            }
        }
    }

    @Test
    void testCreateUser() {
        // Given
        when(passwordEncoder.encode(anyString())).thenReturn("encodedPassword");
        when(userMapper.insert(any(User.class))).thenReturn(1);

        // When
        UserResponse result = userService.createUser(createRequest);

        // Then
        assertNotNull(result);
        assertEquals(createRequest.getUsername(), result.getUsername());
        assertEquals(createRequest.getEmail(), result.getEmail());
        verify(passwordEncoder).encode(createRequest.getPassword());
        verify(userMapper).insert(any(User.class));
    }

    @Test
    void testUpdateUser() {
        // Given
        when(userMapper.selectById(1L)).thenReturn(testUser);
        when(userMapper.updateById(any(User.class))).thenReturn(1);

        // When
        UserResponse result = userService.updateUser(updateRequest);

        // Then
        assertNotNull(result);
        assertEquals(updateRequest.getEmail(), result.getEmail());
        assertEquals(updateRequest.getPhone(), result.getPhone());
        verify(userMapper).selectById(1L);
        verify(userMapper).updateById(any(User.class));
    }

    @Test
    void testUpdateUserNotFound() {
        // Given
        when(userMapper.selectById(1L)).thenReturn(null);

        // When & Then
        assertThrows(BusinessException.class, () -> userService.updateUser(updateRequest));
        verify(userMapper).selectById(1L);
        verify(userMapper, never()).updateById(any(User.class));
    }

    @Test
    void testDeleteUser() {
        // Given
        when(userMapper.selectById(1L)).thenReturn(testUser);
        when(userMapper.deleteById(1L)).thenReturn(1);

        // When
        userService.deleteUser(1L);

        // Then
        verify(userMapper).selectById(1L);
        verify(userMapper).deleteById(1L);
    }

    @Test
    void testBatchDeleteUsers() {
        // Given
        List<Long> userIds = Arrays.asList(1L, 2L, 3L);

        // When
        userService.batchDeleteUsers(userIds);

        // Then
        verify(userMapper).deleteByIds(userIds);
    }

    @Test
    void testGetUserById() {
        // Given
        when(userMapper.selectById(1L)).thenReturn(testUser);

        // When
        UserResponse result = userService.getUserById(1L);

        // Then
        assertNotNull(result);
        assertEquals(testUser.getId(), result.getId());
        assertEquals(testUser.getUsername(), result.getUsername());
        verify(userMapper).selectById(1L);
    }

    @Test
    void testGetUserByIdNotFound() {
        // Given
        when(userMapper.selectById(1L)).thenReturn(null);

        // When & Then
        assertThrows(BusinessException.class, () -> userService.getUserById(1L));
        verify(userMapper).selectById(1L);
    }

    @Test
    void testGetUserByEmail() {
        // Given
        lenient().when(userMapper.selectOne(any(), any(Boolean.class))).thenReturn(testUser);

        // When
        User result = userService.getUserByEmail("<EMAIL>");

        // Then
        assertNotNull(result);
        assertEquals(testUser.getEmail(), result.getEmail());
    }

    @Test
    void testGetUserByPhone() {
        // Given
        lenient().when(userMapper.selectOne(any(), any(Boolean.class))).thenReturn(testUser);

        // When
        User result = userService.getUserByPhone("***********");

        // Then
        assertNotNull(result);
        assertEquals(testUser.getPhone(), result.getPhone());
    }

    @Test
    void testGetUsersByDepartmentId() {
        // Given
        List<User> users = Arrays.asList(testUser);
        when(userMapper.selectList(any())).thenReturn(users);

        // When
        List<UserResponse> result = userService.getUsersByDepartmentId(1L);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testUser.getId(), result.get(0).getId());
        verify(userMapper).selectList(any());
    }

    @Test
    void testChangePassword() {
        // Given
        PasswordChangeRequest request = new PasswordChangeRequest();
        request.setUserId(1L);
        request.setOldPassword("oldPassword");
        request.setNewPassword("newPassword");
        request.setConfirmPassword("newPassword");

        testUser.setPassword("encodedOldPassword");
        when(userMapper.selectById(1L)).thenReturn(testUser);
        when(passwordEncoder.matches("oldPassword", "encodedOldPassword")).thenReturn(true);
        when(passwordEncoder.encode("newPassword")).thenReturn("encodedNewPassword");
        when(userMapper.updateById(any(User.class))).thenReturn(1);

        // When
        userService.changePassword(request);

        // Then
        verify(userMapper).selectById(1L);
        verify(passwordEncoder).matches("oldPassword", "encodedOldPassword");
        verify(passwordEncoder).encode("newPassword");
        verify(userMapper).updateById(any(User.class));
    }

    @Test
    void testChangePasswordWrongOldPassword() {
        // Given
        PasswordChangeRequest request = new PasswordChangeRequest();
        request.setUserId(1L);
        request.setOldPassword("wrongPassword");
        request.setNewPassword("newPassword");
        request.setConfirmPassword("newPassword");

        testUser.setPassword("encodedOldPassword");
        when(userMapper.selectById(1L)).thenReturn(testUser);
        when(passwordEncoder.matches("wrongPassword", "encodedOldPassword")).thenReturn(false);

        // When & Then
        assertThrows(BusinessException.class, () -> userService.changePassword(request));
        verify(userMapper).selectById(1L);
        verify(passwordEncoder).matches("wrongPassword", "encodedOldPassword");
        verify(passwordEncoder, never()).encode(anyString());
        verify(userMapper, never()).updateById(any(User.class));
    }

    @Test
    void testChangePasswordMismatch() {
        // Given
        PasswordChangeRequest request = new PasswordChangeRequest();
        request.setUserId(1L);
        request.setOldPassword("oldPassword");
        request.setNewPassword("newPassword");
        request.setConfirmPassword("differentPassword");

        testUser.setPassword("encodedOldPassword");
        when(userMapper.selectById(1L)).thenReturn(testUser);
        when(passwordEncoder.matches("oldPassword", "encodedOldPassword")).thenReturn(true);

        // When & Then
        assertThrows(BusinessException.class, () -> userService.changePassword(request));
        verify(userMapper).selectById(1L);
        verify(passwordEncoder).matches("oldPassword", "encodedOldPassword");
        verify(passwordEncoder, never()).encode(anyString());
        verify(userMapper, never()).updateById(any(User.class));
    }

    @Test
    void testActivateUser() {
        // Given
        when(userMapper.selectById(1L)).thenReturn(testUser);
        when(userMapper.updateById(any(User.class))).thenReturn(1);

        // When
        userService.activateUser(1L);

        // Then
        verify(userMapper).selectById(1L);
        verify(userMapper).updateById(any(User.class));
    }

    @Test
    void testDeactivateUser() {
        // Given
        when(userMapper.selectById(1L)).thenReturn(testUser);
        when(userMapper.updateById(any(User.class))).thenReturn(1);

        // When
        userService.deactivateUser(1L);

        // Then
        verify(userMapper).selectById(1L);
        verify(userMapper).updateById(any(User.class));
    }

    @Test
    void testLockUser() {
        // Given
        when(userMapper.selectById(1L)).thenReturn(testUser);
        when(userMapper.updateById(any(User.class))).thenReturn(1);

        // When
        userService.lockUser(1L);

        // Then
        verify(userMapper).selectById(1L);
        verify(userMapper).updateById(any(User.class));
    }

    @Test
    void testIsUsernameExists() {
        // Given
        when(userMapper.selectCount(any())).thenReturn(1L);

        // When
        boolean result = userService.isUsernameExists("testuser", null);

        // Then
        assertTrue(result);
        verify(userMapper).selectCount(any());
    }

    @Test
    void testIsUsernameNotExists() {
        // Given
        when(userMapper.selectCount(any())).thenReturn(0L);

        // When
        boolean result = userService.isUsernameExists("testuser", null);

        // Then
        assertFalse(result);
        verify(userMapper).selectCount(any());
    }

    @Test
    void testValidatePassword() {
        // Given
        testUser.setPassword("encodedPassword");
        when(userMapper.selectById(1L)).thenReturn(testUser);
        when(passwordEncoder.matches("password", "encodedPassword")).thenReturn(true);

        // When
        boolean result = userService.validatePassword(1L, "password");

        // Then
        assertTrue(result);
        verify(userMapper).selectById(1L);
        verify(passwordEncoder).matches("password", "encodedPassword");
    }
}
