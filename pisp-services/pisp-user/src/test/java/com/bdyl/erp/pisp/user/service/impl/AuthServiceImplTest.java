package com.bdyl.erp.pisp.user.service.impl;

import java.time.Duration;
import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.util.ReflectionTestUtils;

import com.bdyl.erp.pisp.common.security.jwt.JwtTokenUtil;
import com.bdyl.erp.pisp.common.security.user.PispUserDetails;
import com.bdyl.erp.pisp.user.dto.request.LoginRequest;
import com.bdyl.erp.pisp.user.dto.request.RefreshTokenRequest;
import com.bdyl.erp.pisp.user.dto.response.LoginResponse;
import com.bdyl.erp.pisp.user.entity.User;
import com.bdyl.erp.pisp.user.enums.UserStatus;
import com.bdyl.erp.pisp.user.service.UserService;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * AuthService单元测试类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class AuthServiceImplTest {

    /**
     * 认证管理器Mock
     */
    @Mock
    private AuthenticationManager authenticationManager;

    /**
     * JWT令牌工具Mock
     */
    @Mock
    private JwtTokenUtil jwtTokenUtil;

    /**
     * 用户服务Mock
     */
    @Mock
    private UserService userService;

    /**
     * Redis模板Mock
     */
    @Mock
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * Redis值操作Mock
     */
    @Mock
    private ValueOperations<String, Object> valueOperations;

    /**
     * 认证服务实现
     */
    @InjectMocks
    private AuthServiceImpl authService;

    /**
     * 测试用户详情
     */
    private PispUserDetails testUserDetails;

    /**
     * 测试用户实体
     */
    private User testUser;

    /**
     * 测试登录请求
     */
    private LoginRequest loginRequest;

    @BeforeEach
    void setUp() {
        // 设置JWT过期时间
        ReflectionTestUtils.setField(authService, "jwtExpiration", 86400L);

        // 准备测试数据
        setupTestData();
    }

    /**
     * 准备测试数据
     */
    private void setupTestData() {
        // 创建测试用户详情
        testUserDetails = new PispUserDetails();
        testUserDetails.setUserId(1L);
        testUserDetails.setUsername("testuser");
        testUserDetails.setPassword("encodedPassword");
        testUserDetails.setRealName("测试用户");
        testUserDetails.setEmail("<EMAIL>");
        testUserDetails.setPhone("13800138000");
        testUserDetails.setDepartmentId(1L);
        testUserDetails.setTenantId(1L);
        testUserDetails.setStatus(1);
        testUserDetails.setIsAdmin(false);
        testUserDetails.setRoles(Set.of("USER"));
        testUserDetails.setPermissions(Set.of("user:read"));

        // 创建测试用户实体
        testUser = new User();
        testUser.setId(1L);
        testUser.setUsername("testuser");
        testUser.setPassword("encodedPassword");
        testUser.setRealName("测试用户");
        testUser.setEmail("<EMAIL>");
        testUser.setPhone("13800138000");
        testUser.setDepartmentId(1L);
        testUser.setStatus(UserStatus.ACTIVE);

        // 创建测试登录请求
        loginRequest = new LoginRequest();
        loginRequest.setUsername("testuser");
        loginRequest.setPassword("password123");
        loginRequest.setLoginIp("*************");
        loginRequest.setRememberMe(false);
    }

    @Test
    void testLoginSuccess() {
        // Given
        Authentication authentication = mock(Authentication.class);
        when(authentication.getPrincipal()).thenReturn(testUserDetails);

        when(authenticationManager.authenticate(any(UsernamePasswordAuthenticationToken.class)))
            .thenReturn(authentication);

        doNothing().when(userService).updateLastLoginInfo(eq(1L), eq("*************"));

        when(jwtTokenUtil.generateAccessToken(testUserDetails)).thenReturn("access-token");
        when(jwtTokenUtil.generateRefreshToken(testUserDetails)).thenReturn("refresh-token");

        // 模拟Redis操作
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        doNothing().when(valueOperations).set(anyString(), any(), any(Duration.class));

        // When
        LoginResponse response = authService.login(loginRequest);

        // Then
        assertNotNull(response);
        assertEquals("access-token", response.getAccessToken());
        assertEquals("refresh-token", response.getRefreshToken());
        assertEquals("Bearer", response.getTokenType());
        assertEquals(86400L, response.getExpiresIn());

        assertNotNull(response.getUserInfo());
        assertEquals(1L, response.getUserInfo().getUserId());
        assertEquals("testuser", response.getUserInfo().getUsername());
        assertEquals("测试用户", response.getUserInfo().getRealName());

        assertEquals(Set.of("USER"), response.getRoles());
        assertEquals(Set.of("user:read"), response.getPermissions());

        // 验证方法调用
        verify(authenticationManager).authenticate(any(UsernamePasswordAuthenticationToken.class));
        verify(userService).updateLastLoginInfo(1L, "*************");
        verify(jwtTokenUtil).generateAccessToken(testUserDetails);
        verify(jwtTokenUtil).generateRefreshToken(testUserDetails);
        verify(valueOperations, times(2)).set(anyString(), any(), any(Duration.class));
    }

    @Test
    void testLoginWithRememberMe() {
        // Given
        loginRequest.setRememberMe(true);

        Authentication authentication = mock(Authentication.class);
        when(authentication.getPrincipal()).thenReturn(testUserDetails);

        when(authenticationManager.authenticate(any(UsernamePasswordAuthenticationToken.class)))
            .thenReturn(authentication);

        doNothing().when(userService).updateLastLoginInfo(eq(1L), eq("*************"));

        when(jwtTokenUtil.generateAccessToken(testUserDetails)).thenReturn("access-token");
        when(jwtTokenUtil.generateRefreshToken(testUserDetails)).thenReturn("refresh-token");

        // 模拟Redis操作
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        doNothing().when(valueOperations).set(anyString(), any(), eq(Duration.ofDays(7)));

        // When
        LoginResponse response = authService.login(loginRequest);

        // Then
        assertNotNull(response);
        assertEquals("access-token", response.getAccessToken());

        // 验证记住我功能：缓存时间为7天
        verify(valueOperations, times(2)).set(anyString(), any(), eq(Duration.ofDays(7)));
    }

    @Test
    void testLoginFailure() {
        // Given
        when(authenticationManager.authenticate(any(UsernamePasswordAuthenticationToken.class)))
            .thenThrow(new BadCredentialsException("认证失败"));

        // When & Then
        BadCredentialsException exception =
            assertThrows(BadCredentialsException.class, () -> authService.login(loginRequest));

        assertEquals("用户名或密码错误", exception.getMessage());

        // 验证不会调用后续方法
        verify(userService, never()).updateLastLoginInfo(any(), any());
        verify(jwtTokenUtil, never()).generateAccessToken(any());
    }

    @Test
    void testLogoutSuccess() {
        // Given
        Long userId = 1L;
        when(redisTemplate.delete("user:session:" + userId)).thenReturn(true);

        // When
        authService.logout(userId);

        // Then
        verify(redisTemplate).delete("user:session:" + userId);
    }

    @Test
    void testRefreshTokenSuccess() {
        // Given
        RefreshTokenRequest request = new RefreshTokenRequest();
        request.setRefreshToken("valid-refresh-token");

        when(jwtTokenUtil.validateToken("valid-refresh-token")).thenReturn(true);
        when(jwtTokenUtil.getUserDetailsFromToken("valid-refresh-token")).thenReturn(testUserDetails);
        when(userService.getUserByUsername("testuser")).thenReturn(testUser);
        when(jwtTokenUtil.generateAccessToken(testUserDetails)).thenReturn("new-access-token");
        when(jwtTokenUtil.generateRefreshToken(testUserDetails)).thenReturn("new-refresh-token");

        // 模拟Redis操作
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        doNothing().when(valueOperations).set(anyString(), any(), any(Duration.class));

        // When
        LoginResponse response = authService.refreshToken(request);

        // Then
        assertNotNull(response);
        assertEquals("new-access-token", response.getAccessToken());
        assertEquals("new-refresh-token", response.getRefreshToken());

        verify(jwtTokenUtil).validateToken("valid-refresh-token");
        verify(jwtTokenUtil).getUserDetailsFromToken("valid-refresh-token");
        verify(userService).getUserByUsername("testuser");
        verify(jwtTokenUtil).generateAccessToken(testUserDetails);
        verify(jwtTokenUtil).generateRefreshToken(testUserDetails);
    }

    @Test
    void testRefreshTokenInvalid() {
        // Given
        RefreshTokenRequest request = new RefreshTokenRequest();
        request.setRefreshToken("invalid-refresh-token");

        when(jwtTokenUtil.validateToken("invalid-refresh-token")).thenReturn(false);

        // When & Then
        BadCredentialsException exception =
            assertThrows(BadCredentialsException.class, () -> authService.refreshToken(request));

        assertEquals("令牌刷新失败", exception.getMessage());

        verify(jwtTokenUtil).validateToken("invalid-refresh-token");
        verify(jwtTokenUtil, never()).getUserDetailsFromToken(any());
    }

    @Test
    void testRefreshTokenUserNotFound() {
        // Given
        RefreshTokenRequest request = new RefreshTokenRequest();
        request.setRefreshToken("valid-refresh-token");

        when(jwtTokenUtil.validateToken("valid-refresh-token")).thenReturn(true);
        when(jwtTokenUtil.getUserDetailsFromToken("valid-refresh-token")).thenReturn(testUserDetails);
        when(userService.getUserByUsername("testuser")).thenReturn(null);

        // When & Then
        BadCredentialsException exception =
            assertThrows(BadCredentialsException.class, () -> authService.refreshToken(request));

        assertEquals("令牌刷新失败", exception.getMessage());

        verify(userService).getUserByUsername("testuser");
    }

    @Test
    void testValidateTokenValid() {
        // Given
        String validToken = "valid-token";
        when(jwtTokenUtil.validateToken(validToken)).thenReturn(true);

        // When
        boolean result = authService.validateToken(validToken);

        // Then
        assertTrue(result);
        verify(jwtTokenUtil).validateToken(validToken);
    }

    @Test
    void testValidateTokenInvalid() {
        // Given
        String invalidToken = "invalid-token";
        when(jwtTokenUtil.validateToken(invalidToken)).thenReturn(false);

        // When
        boolean result = authService.validateToken(invalidToken);

        // Then
        assertFalse(result);
        verify(jwtTokenUtil).validateToken(invalidToken);
    }

    @Test
    void testValidateTokenEmpty() {
        // When
        boolean result1 = authService.validateToken("");
        boolean result2 = authService.validateToken(null);
        boolean result3 = authService.validateToken("   ");

        // Then
        assertFalse(result1);
        assertFalse(result2);
        assertFalse(result3);

        // 验证不会调用JWT工具类
        verify(jwtTokenUtil, never()).validateToken(any());
    }

    @Test
    void testGetCurrentUserInfoSuccess() {
        // Given
        Authentication authentication = mock(Authentication.class);
        when(authentication.getPrincipal()).thenReturn(testUserDetails);

        SecurityContext securityContext = mock(SecurityContext.class);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        SecurityContextHolder.setContext(securityContext);

        // When
        LoginResponse.UserInfo userInfo = authService.getCurrentUserInfo();

        // Then
        assertNotNull(userInfo);
        assertEquals(1L, userInfo.getUserId());
        assertEquals("testuser", userInfo.getUsername());
        assertEquals("测试用户", userInfo.getRealName());
        assertEquals("<EMAIL>", userInfo.getEmail());
        assertEquals("13800138000", userInfo.getPhone());
        assertEquals(1L, userInfo.getDepartmentId());
        assertEquals(1L, userInfo.getTenantId());
        assertEquals(false, userInfo.getIsAdmin());
    }

    @Test
    void testGetCurrentUserInfoNoAuthentication() {
        // Given
        SecurityContext securityContext = mock(SecurityContext.class);
        when(securityContext.getAuthentication()).thenReturn(null);
        SecurityContextHolder.setContext(securityContext);

        // When
        LoginResponse.UserInfo userInfo = authService.getCurrentUserInfo();

        // Then
        assertNull(userInfo);
    }

    @Test
    void testGetCurrentUserInfoWrongPrincipalType() {
        // Given
        Authentication authentication = mock(Authentication.class);
        when(authentication.getPrincipal()).thenReturn("wrong-type");

        SecurityContext securityContext = mock(SecurityContext.class);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        SecurityContextHolder.setContext(securityContext);

        // When
        LoginResponse.UserInfo userInfo = authService.getCurrentUserInfo();

        // Then
        assertNull(userInfo);
    }
}
