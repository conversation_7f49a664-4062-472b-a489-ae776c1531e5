package com.bdyl.erp.pisp.user.service.impl;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import com.bdyl.erp.pisp.common.core.exception.BusinessException;
import com.bdyl.erp.pisp.common.security.context.UserContext;
import com.bdyl.erp.pisp.common.security.context.UserContextHolder;
import com.bdyl.erp.pisp.user.dto.request.DepartmentCreateRequest;
import com.bdyl.erp.pisp.user.dto.request.DepartmentQueryRequest;
import com.bdyl.erp.pisp.user.dto.request.DepartmentUpdateRequest;
import com.bdyl.erp.pisp.user.dto.response.DepartmentResponse;
import com.bdyl.erp.pisp.user.entity.Department;
import com.bdyl.erp.pisp.user.enums.DepartmentStatus;
import com.bdyl.erp.pisp.user.service.DepartmentService;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 部门服务测试类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class DepartmentServiceImplTest {

    /**
     * 部门服务
     */
    @Autowired
    private DepartmentService departmentService;

    /**
     * 部门创建请求
     */
    private DepartmentCreateRequest createRequest;

    /**
     * 创建的部门响应
     */
    private DepartmentResponse createdDepartment;

    @BeforeEach
    void setUp() {
        // 设置测试用户上下文
        UserContext userContext = new UserContext();
        userContext.setUserId(1L);
        userContext.setUsername("test_user");
        userContext.setTenantId(1L);
        UserContextHolder.setContext(userContext);

        // 准备测试数据 - 使用唯一的部门名称避免与预置数据冲突
        createRequest = new DepartmentCreateRequest();
        createRequest.setDeptName("单元测试部门" + System.currentTimeMillis());
        createRequest.setDeptCode("TEST_DEPT_" + System.currentTimeMillis());
        createRequest.setDescription("这是一个测试部门");
        createRequest.setStatus(DepartmentStatus.ACTIVE);
        createRequest.setSortOrder(1);
    }

    @AfterEach
    void tearDown() {
        // 清理用户上下文 - 事务回滚时自动清理
        UserContextHolder.setContext(null);
    }

    @Test
    void testCreateDepartment() {
        // 测试创建部门
        DepartmentResponse response = departmentService.createDepartment(createRequest);

        assertNotNull(response);
        assertNotNull(response.getId());
        assertEquals(createRequest.getDeptName(), response.getDeptName());
        assertEquals(createRequest.getDeptCode(), response.getDeptCode());
        assertEquals(createRequest.getDescription(), response.getDescription());
        assertEquals(createRequest.getStatus(), response.getStatus());
        assertEquals(1, response.getDeptLevel());

        createdDepartment = response;
    }

    @Test
    void testCreateDepartmentWithDuplicateCode() {
        // 创建第一个部门
        departmentService.createDepartment(createRequest);

        // 尝试创建具有相同代码的部门，应该抛出异常
        DepartmentCreateRequest duplicateRequest = new DepartmentCreateRequest();
        duplicateRequest.setDeptName("重复代码部门" + System.currentTimeMillis());
        duplicateRequest.setDeptCode(createRequest.getDeptCode()); // 使用相同的代码
        duplicateRequest.setStatus(DepartmentStatus.ACTIVE);

        assertThrows(BusinessException.class, () -> {
            departmentService.createDepartment(duplicateRequest);
        });
    }

    @Test
    void testCreateSubDepartment() {
        // 创建父部门
        DepartmentResponse parentDept = departmentService.createDepartment(createRequest);

        // 创建子部门
        DepartmentCreateRequest subDeptRequest = new DepartmentCreateRequest();
        subDeptRequest.setDeptName("子部门" + System.currentTimeMillis());
        subDeptRequest.setDeptCode("SUB_DEPT_" + System.currentTimeMillis());
        subDeptRequest.setParentId(parentDept.getId());
        subDeptRequest.setStatus(DepartmentStatus.ACTIVE);

        DepartmentResponse subDept = departmentService.createDepartment(subDeptRequest);

        assertNotNull(subDept);
        assertEquals(parentDept.getId(), subDept.getParentId());
        assertEquals(2, subDept.getDeptLevel()); // 子部门层级应为2
    }

    @Test
    void testUpdateDepartment() {
        // 创建部门
        DepartmentResponse created = departmentService.createDepartment(createRequest);

        // 更新部门
        DepartmentUpdateRequest updateRequest = new DepartmentUpdateRequest();
        updateRequest.setId(created.getId());
        updateRequest.setDeptName("更新后的部门名称");
        updateRequest.setDescription("更新后的描述");
        updateRequest.setVersion(created.getVersion());

        DepartmentResponse updated = departmentService.updateDepartment(updateRequest);

        assertNotNull(updated);
        assertEquals("更新后的部门名称", updated.getDeptName());
        assertEquals("更新后的描述", updated.getDescription());
        assertEquals(created.getDeptCode(), updated.getDeptCode()); // 代码不应变更
    }

    @Test
    void testDeleteDepartment() {
        // 创建部门
        DepartmentResponse created = departmentService.createDepartment(createRequest);

        // 删除部门
        departmentService.deleteDepartment(created.getId());

        // 验证部门已被删除
        assertThrows(BusinessException.class, () -> {
            departmentService.getDepartmentById(created.getId());
        });
    }

    @Test
    void testDeleteDepartmentWithChildren() {
        // 创建父部门
        DepartmentResponse parentDept = departmentService.createDepartment(createRequest);

        // 创建子部门
        DepartmentCreateRequest subDeptRequest = new DepartmentCreateRequest();
        subDeptRequest.setDeptName("子部门" + System.currentTimeMillis());
        subDeptRequest.setDeptCode("SUB_DEPT_" + System.currentTimeMillis());
        subDeptRequest.setParentId(parentDept.getId());
        subDeptRequest.setStatus(DepartmentStatus.ACTIVE);
        departmentService.createDepartment(subDeptRequest);

        // 尝试删除有子部门的父部门，应该抛出异常
        assertThrows(BusinessException.class, () -> {
            departmentService.deleteDepartment(parentDept.getId());
        });
    }

    @Test
    void testGetDepartmentById() {
        // 创建部门
        DepartmentResponse created = departmentService.createDepartment(createRequest);

        // 根据ID查询部门
        DepartmentResponse found = departmentService.getDepartmentById(created.getId());

        assertNotNull(found);
        assertEquals(created.getId(), found.getId());
        assertEquals(created.getDeptName(), found.getDeptName());
        assertEquals(created.getDeptCode(), found.getDeptCode());
    }

    @Test
    void testGetDepartmentByCode() {
        // 创建部门
        departmentService.createDepartment(createRequest);

        // 根据代码查询部门
        Department found = departmentService.getByDeptCode(createRequest.getDeptCode());

        assertNotNull(found);
        assertEquals(createRequest.getDeptCode(), found.getDeptCode());
        assertEquals(createRequest.getDeptName(), found.getDeptName());
    }

    @Test
    void testGetDepartmentPage() {
        // 创建多个部门
        long timestamp = System.currentTimeMillis();
        DepartmentCreateRequest request1 = new DepartmentCreateRequest();
        request1.setDeptName("测试部门1_" + timestamp);
        request1.setDeptCode("TEST_DEPT_1_" + timestamp);
        request1.setStatus(DepartmentStatus.ACTIVE);
        departmentService.createDepartment(request1);

        DepartmentCreateRequest request2 = new DepartmentCreateRequest();
        request2.setDeptName("测试部门2_" + timestamp);
        request2.setDeptCode("TEST_DEPT_2_" + timestamp);
        request2.setStatus(DepartmentStatus.ACTIVE);
        departmentService.createDepartment(request2);

        // 查询分页数据
        DepartmentQueryRequest queryRequest = new DepartmentQueryRequest();
        queryRequest.setPageNum(1);
        queryRequest.setPageSize(10);

        var page = departmentService.getDepartmentPage(queryRequest);

        assertNotNull(page);
        assertTrue(page.getRecords().size() >= 2);
    }

    @Test
    void testGetDepartmentTree() {
        // 创建部门树结构
        DepartmentResponse rootDept = departmentService.createDepartment(createRequest);

        DepartmentCreateRequest subDeptRequest = new DepartmentCreateRequest();
        subDeptRequest.setDeptName("子部门");
        subDeptRequest.setDeptCode("SUB_DEPT");
        subDeptRequest.setParentId(rootDept.getId());
        subDeptRequest.setStatus(DepartmentStatus.ACTIVE);
        departmentService.createDepartment(subDeptRequest);

        // 查询部门树
        List<DepartmentResponse> tree = departmentService.getDepartmentTree();

        assertNotNull(tree);
        assertFalse(tree.isEmpty());

        // 验证根部门存在
        DepartmentResponse foundRoot =
            tree.stream().filter(dept -> dept.getId().equals(rootDept.getId())).findFirst().orElse(null);

        assertNotNull(foundRoot);
        assertNotNull(foundRoot.getChildren());
        assertFalse(foundRoot.getChildren().isEmpty());
    }

    @Test
    void testActivateAndDeactivateDepartment() {
        // 创建部门
        DepartmentResponse created = departmentService.createDepartment(createRequest);

        // 禁用部门
        departmentService.deactivateDepartment(created.getId());

        DepartmentResponse deactivated = departmentService.getDepartmentById(created.getId());
        assertEquals(DepartmentStatus.INACTIVE, deactivated.getStatus());

        // 启用部门
        departmentService.activateDepartment(created.getId());

        DepartmentResponse activated = departmentService.getDepartmentById(created.getId());
        assertEquals(DepartmentStatus.ACTIVE, activated.getStatus());
    }

    @Test
    void testBatchDeleteDepartments() {
        // 创建多个部门
        long timestamp = System.currentTimeMillis();
        DepartmentCreateRequest request1 = new DepartmentCreateRequest();
        request1.setDeptName("批量测试部门1_" + timestamp);
        request1.setDeptCode("BATCH_DEPT_1_" + timestamp);
        request1.setStatus(DepartmentStatus.ACTIVE);
        DepartmentResponse dept1 = departmentService.createDepartment(request1);

        DepartmentCreateRequest request2 = new DepartmentCreateRequest();
        request2.setDeptName("批量测试部门2_" + timestamp);
        request2.setDeptCode("BATCH_DEPT_2_" + timestamp);
        request2.setStatus(DepartmentStatus.ACTIVE);
        DepartmentResponse dept2 = departmentService.createDepartment(request2);

        // 批量删除
        List<Long> deptIds = Arrays.asList(dept1.getId(), dept2.getId());
        departmentService.batchDeleteDepartments(deptIds);

        // 验证删除
        assertThrows(BusinessException.class, () -> {
            departmentService.getDepartmentById(dept1.getId());
        });
        assertThrows(BusinessException.class, () -> {
            departmentService.getDepartmentById(dept2.getId());
        });
    }

    @Test
    void testSetDepartmentLeader() {
        // 创建部门
        DepartmentResponse created = departmentService.createDepartment(createRequest);

        // 设置负责人（假设存在用户ID为1）
        Long leaderId = 1L;
        departmentService.setDepartmentLeader(created.getId(), leaderId);

        DepartmentResponse updated = departmentService.getDepartmentById(created.getId());
        assertEquals(leaderId, updated.getLeaderId());
    }

    @Test
    void testMoveDepartment() {
        // 创建父部门
        DepartmentResponse parentDept = departmentService.createDepartment(createRequest);

        // 创建子部门
        long timestamp = System.currentTimeMillis();
        DepartmentCreateRequest subDeptRequest = new DepartmentCreateRequest();
        subDeptRequest.setDeptName("子部门" + timestamp);
        subDeptRequest.setDeptCode("SUB_DEPT_" + timestamp);
        subDeptRequest.setParentId(parentDept.getId());
        subDeptRequest.setStatus(DepartmentStatus.ACTIVE);
        DepartmentResponse subDept = departmentService.createDepartment(subDeptRequest);

        // 创建新的父部门
        DepartmentCreateRequest newParentRequest = new DepartmentCreateRequest();
        newParentRequest.setDeptName("新父部门" + timestamp);
        newParentRequest.setDeptCode("NEW_PARENT_" + timestamp);
        newParentRequest.setStatus(DepartmentStatus.ACTIVE);
        DepartmentResponse newParent = departmentService.createDepartment(newParentRequest);

        // 移动部门
        departmentService.moveDepartment(subDept.getId(), newParent.getId());

        // 验证移动结果
        DepartmentResponse movedDept = departmentService.getDepartmentById(subDept.getId());
        assertEquals(newParent.getId(), movedDept.getParentId());
    }

    @Test
    void testIsDeptCodeExists() {
        // 创建部门
        departmentService.createDepartment(createRequest);

        // 检查代码是否存在
        assertTrue(departmentService.isDeptCodeExists(createRequest.getDeptCode(), null));
        assertFalse(departmentService.isDeptCodeExists("NON_EXISTENT_CODE", null));
    }

    @Test
    void testIsDeptNameExists() {
        // 创建部门
        departmentService.createDepartment(createRequest);

        // 检查名称是否存在
        assertTrue(departmentService.isDeptNameExists(createRequest.getDeptName(), null, null));
        assertFalse(departmentService.isDeptNameExists("不存在的部门名称", null, null));
    }
}
