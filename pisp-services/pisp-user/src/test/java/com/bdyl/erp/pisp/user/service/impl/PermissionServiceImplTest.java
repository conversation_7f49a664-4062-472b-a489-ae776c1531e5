package com.bdyl.erp.pisp.user.service.impl;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import com.bdyl.erp.pisp.common.security.context.UserContext;
import com.bdyl.erp.pisp.common.security.context.UserContextHolder;
import com.bdyl.erp.pisp.common.web.result.PageResult;
import com.bdyl.erp.pisp.user.dto.request.PermissionCreateRequest;
import com.bdyl.erp.pisp.user.dto.request.PermissionQueryRequest;
import com.bdyl.erp.pisp.user.dto.response.PermissionResponse;
import com.bdyl.erp.pisp.user.service.PermissionService;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 权限服务测试类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class PermissionServiceImplTest {

    /**
     * 权限服务
     */
    @Autowired
    private PermissionService permissionService;

    /**
     * 创建请求
     */
    private PermissionCreateRequest createRequest;

    @BeforeEach
    void setUp() {
        // 设置测试用户上下文
        UserContext userContext = new UserContext();
        userContext.setUserId(1L);
        userContext.setUsername("test_user");
        userContext.setTenantId(1L);
        UserContextHolder.setContext(userContext);

        // 准备测试数据
        createRequest = new PermissionCreateRequest();
        createRequest.setPermissionName("测试权限" + System.currentTimeMillis());
        createRequest.setPermissionCode("test:permission:" + System.currentTimeMillis());
        createRequest.setDescription("这是一个测试权限");
        createRequest.setPermissionType("API");
        createRequest.setSortOrder(1);
        createRequest.setIsSystem(false);
    }

    @AfterEach
    void tearDown() {
        // 清理用户上下文
        UserContextHolder.setContext(null);
    }

    @Test
    void testCreatePermission() {
        // 创建权限
        PermissionResponse response = permissionService.createPermission(createRequest);

        assertNotNull(response);
        assertNotNull(response.getId());
        assertEquals(createRequest.getPermissionName(), response.getPermissionName());
        assertEquals(createRequest.getPermissionCode(), response.getPermissionCode());
        assertEquals(createRequest.getPermissionType(), response.getPermissionType());
    }

    @Test
    void testGetPermissionPage() {
        // 创建权限
        permissionService.createPermission(createRequest);

        // 分页查询
        PermissionQueryRequest queryRequest = new PermissionQueryRequest();
        queryRequest.setPageNum(1);
        queryRequest.setPageSize(10);
        PageResult<PermissionResponse> pageResult = permissionService.getPermissionPage(queryRequest);

        assertNotNull(pageResult);
        assertNotNull(pageResult.getRecords());
        assertTrue(pageResult.getTotal() > 0);
    }
}
