# 测试环境主配置
spring:
  application:
    name: pisp-user-service-test
  
  # 数据源配置 - 使用H2内存数据库
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=PostgreSQL
    username: sa
    password: 
    hikari:
      minimum-idle: 1
      maximum-pool-size: 5
  
  # 禁用Liquibase
  liquibase:
    enabled: false
  
  # JPA配置 - 用于创建表结构
  jpa:
    hibernate:
      ddl-auto: create-drop
    database-platform: org.hibernate.dialect.H2Dialect
    show-sql: false
  
  # 禁用Redis
  data:
    redis:
      repositories:
        enabled: false
  
  # 完全禁用Spring Cloud
  cloud:
    nacos:
      discovery:
        enabled: false
      config:
        enabled: false
    discovery:
      enabled: false
    config:
      enabled: false
    loadbalancer:
      enabled: false

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.nop.NopImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
    banner: false

# 日志配置
logging:
  level:
    root: WARN
    com.bdyl.erp.pisp: DEBUG
    org.springframework.test: DEBUG

# 禁用管理端点
management:
  endpoints:
    enabled-by-default: false

# 测试配置
pisp:
  user:
    password:
      strength: 4  # 测试环境使用较低强度
    cache:
      user-info-ttl: 60
      permission-ttl: 30
