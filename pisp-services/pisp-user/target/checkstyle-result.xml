<?xml version="1.0" encoding="UTF-8"?>
<checkstyle version="9.3">
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/dto/response/UserResponse.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/dto/response/RoleResponse.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/dto/response/LoginResponse.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/dto/response/PermissionResponse.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/dto/response/DepartmentResponse.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/dto/request/PermissionQueryRequest.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/dto/request/RoleCreateRequest.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/dto/request/PermissionUpdateRequest.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/dto/request/PermissionCreateRequest.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/dto/request/RoleUpdateRequest.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/dto/request/UserCreateRequest.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/dto/request/UserQueryRequest.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/dto/request/DepartmentUpdateRequest.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/dto/request/LoginRequest.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/dto/request/RefreshTokenRequest.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/dto/request/DepartmentCreateRequest.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/dto/request/DepartmentQueryRequest.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/dto/request/UserUpdateRequest.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/dto/request/PasswordChangeRequest.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/dto/request/RoleQueryRequest.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/config/DatabaseConfig.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/config/SecurityConfig.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/config/UserModuleConfig.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/config/AsyncConfig.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/config/WebConfig.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/entity/UserRole.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/entity/Department.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/entity/User.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/entity/RolePermission.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/entity/Permission.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/entity/Role.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/UserServiceApplication.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/enums/DepartmentStatus.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/enums/UserStatus.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/enums/RoleStatus.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/mapper/PermissionMapper.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/mapper/RolePermissionMapper.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/mapper/DepartmentMapper.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/mapper/UserMapper.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/mapper/UserRoleMapper.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/mapper/RoleMapper.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/controller/DepartmentController.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/controller/AuthController.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/controller/RoleController.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/controller/UserController.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/controller/PermissionController.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/service/impl/PispUserDetailsServiceImpl.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/service/impl/RoleServiceImpl.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/service/impl/DepartmentServiceImpl.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/service/impl/PermissionServiceImpl.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/service/impl/RolePermissionServiceImpl.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/service/impl/UserServiceImpl.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/service/impl/AuthServiceImpl.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/service/impl/UserRoleServiceImpl.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/service/UserService.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/service/RolePermissionService.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/service/RoleService.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/service/AuthService.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/service/PermissionService.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/service/UserRoleService.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/main/java/com/bdyl/erp/pisp/user/service/DepartmentService.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/test/java/com/bdyl/erp/pisp/user/integration/UserServiceIntegrationTest.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/test/java/com/bdyl/erp/pisp/user/controller/AuthControllerTest.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/test/java/com/bdyl/erp/pisp/user/controller/UserControllerTest.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/test/java/com/bdyl/erp/pisp/user/service/impl/DepartmentServiceImplTest.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/test/java/com/bdyl/erp/pisp/user/service/impl/PermissionServiceImplTest.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/test/java/com/bdyl/erp/pisp/user/service/impl/AuthServiceImplTest.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/test/java/com/bdyl/erp/pisp/user/service/RoleServiceTest.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/test/java/com/bdyl/erp/pisp/user/service/RolePermissionServiceTest.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/test/java/com/bdyl/erp/pisp/user/service/UserServiceSimpleTest.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/test/java/com/bdyl/erp/pisp/user/service/UserRoleServiceTest.java">
</file>
<file name="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/src/test/java/com/bdyl/erp/pisp/user/service/UserServiceTest.java">
</file>
</checkstyle>
