<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.20.xsd">

    <!-- 初始化部门数据 -->
    <changeSet id="001-init-departments" author="pisp-system">
        <insert tableName="sys_departments">
            <column name="id" value="1"/>
            <column name="dept_name" value="总公司"/>
            <column name="dept_code" value="ROOT"/>
            <column name="parent_id" valueNumeric="null"/>
            <column name="dept_level" value="1"/>
            <column name="dept_path" value="/1"/>
            <column name="description" value="公司总部"/>
            <column name="sort_order" value="1"/>
            <column name="status" value="ACTIVE"/>
            <column name="tenant_id" value="1"/>
            <column name="creator_id" value="1"/>
        </insert>
        
        <insert tableName="sys_departments">
            <column name="id" value="2"/>
            <column name="dept_name" value="技术部"/>
            <column name="dept_code" value="TECH"/>
            <column name="parent_id" value="1"/>
            <column name="dept_level" value="2"/>
            <column name="dept_path" value="/1/2"/>
            <column name="description" value="技术研发部门"/>
            <column name="sort_order" value="1"/>
            <column name="status" value="ACTIVE"/>
            <column name="tenant_id" value="1"/>
            <column name="creator_id" value="1"/>
        </insert>
        
        <insert tableName="sys_departments">
            <column name="id" value="3"/>
            <column name="dept_name" value="运营部"/>
            <column name="dept_code" value="OPS"/>
            <column name="parent_id" value="1"/>
            <column name="dept_level" value="2"/>
            <column name="dept_path" value="/1/3"/>
            <column name="description" value="运营管理部门"/>
            <column name="sort_order" value="2"/>
            <column name="status" value="ACTIVE"/>
            <column name="tenant_id" value="1"/>
            <column name="creator_id" value="1"/>
        </insert>
    </changeSet>

    <!-- 初始化权限数据 -->
    <changeSet id="002-init-permissions" author="pisp-system">
        <!-- 用户管理权限 -->
        <insert tableName="sys_permissions">
            <column name="id" value="1"/>
            <column name="permission_name" value="用户管理"/>
            <column name="permission_code" value="user:manage"/>
            <column name="description" value="用户管理模块"/>
            <column name="permission_type" value="MENU"/>
            <column name="sort_order" value="1"/>
            <column name="is_system" value="true"/>
            <column name="tenant_id" value="1"/>
            <column name="creator_id" value="1"/>
        </insert>
        
        <insert tableName="sys_permissions">
            <column name="id" value="2"/>
            <column name="permission_name" value="用户查询"/>
            <column name="permission_code" value="user:read"/>
            <column name="description" value="查询用户信息"/>
            <column name="permission_type" value="API"/>
            <column name="parent_id" value="1"/>
            <column name="sort_order" value="1"/>
            <column name="is_system" value="true"/>
            <column name="tenant_id" value="1"/>
            <column name="creator_id" value="1"/>
        </insert>
        
        <insert tableName="sys_permissions">
            <column name="id" value="3"/>
            <column name="permission_name" value="用户创建"/>
            <column name="permission_code" value="user:create"/>
            <column name="description" value="创建用户"/>
            <column name="permission_type" value="API"/>
            <column name="parent_id" value="1"/>
            <column name="sort_order" value="2"/>
            <column name="is_system" value="true"/>
            <column name="tenant_id" value="1"/>
            <column name="creator_id" value="1"/>
        </insert>
        
        <insert tableName="sys_permissions">
            <column name="id" value="4"/>
            <column name="permission_name" value="用户更新"/>
            <column name="permission_code" value="user:update"/>
            <column name="description" value="更新用户信息"/>
            <column name="permission_type" value="API"/>
            <column name="parent_id" value="1"/>
            <column name="sort_order" value="3"/>
            <column name="is_system" value="true"/>
            <column name="tenant_id" value="1"/>
            <column name="creator_id" value="1"/>
        </insert>
        
        <insert tableName="sys_permissions">
            <column name="id" value="5"/>
            <column name="permission_name" value="用户删除"/>
            <column name="permission_code" value="user:delete"/>
            <column name="description" value="删除用户"/>
            <column name="permission_type" value="API"/>
            <column name="parent_id" value="1"/>
            <column name="sort_order" value="4"/>
            <column name="is_system" value="true"/>
            <column name="tenant_id" value="1"/>
            <column name="creator_id" value="1"/>
        </insert>
        
        <!-- 角色管理权限 -->
        <insert tableName="sys_permissions">
            <column name="id" value="6"/>
            <column name="permission_name" value="角色管理"/>
            <column name="permission_code" value="role:manage"/>
            <column name="description" value="角色管理模块"/>
            <column name="permission_type" value="MENU"/>
            <column name="sort_order" value="2"/>
            <column name="is_system" value="true"/>
            <column name="tenant_id" value="1"/>
            <column name="creator_id" value="1"/>
        </insert>
        
        <insert tableName="sys_permissions">
            <column name="id" value="7"/>
            <column name="permission_name" value="角色查询"/>
            <column name="permission_code" value="role:read"/>
            <column name="description" value="查询角色信息"/>
            <column name="permission_type" value="API"/>
            <column name="parent_id" value="6"/>
            <column name="sort_order" value="1"/>
            <column name="is_system" value="true"/>
            <column name="tenant_id" value="1"/>
            <column name="creator_id" value="1"/>
        </insert>
        
        <insert tableName="sys_permissions">
            <column name="id" value="8"/>
            <column name="permission_name" value="角色创建"/>
            <column name="permission_code" value="role:create"/>
            <column name="description" value="创建角色"/>
            <column name="permission_type" value="API"/>
            <column name="parent_id" value="6"/>
            <column name="sort_order" value="2"/>
            <column name="is_system" value="true"/>
            <column name="tenant_id" value="1"/>
            <column name="creator_id" value="1"/>
        </insert>
        
        <!-- 部门管理权限 -->
        <insert tableName="sys_permissions">
            <column name="id" value="9"/>
            <column name="permission_name" value="部门管理"/>
            <column name="permission_code" value="dept:manage"/>
            <column name="description" value="部门管理模块"/>
            <column name="permission_type" value="MENU"/>
            <column name="sort_order" value="3"/>
            <column name="is_system" value="true"/>
            <column name="tenant_id" value="1"/>
            <column name="creator_id" value="1"/>
        </insert>
        
        <insert tableName="sys_permissions">
            <column name="id" value="10"/>
            <column name="permission_name" value="部门查询"/>
            <column name="permission_code" value="dept:read"/>
            <column name="description" value="查询部门信息"/>
            <column name="permission_type" value="API"/>
            <column name="parent_id" value="9"/>
            <column name="sort_order" value="1"/>
            <column name="is_system" value="true"/>
            <column name="tenant_id" value="1"/>
            <column name="creator_id" value="1"/>
        </insert>
    </changeSet>

    <!-- 初始化角色数据 -->
    <changeSet id="003-init-roles" author="pisp-system">
        <insert tableName="sys_roles">
            <column name="id" value="1"/>
            <column name="role_name" value="超级管理员"/>
            <column name="role_code" value="SUPER_ADMIN"/>
            <column name="description" value="系统超级管理员，拥有所有权限"/>
            <column name="is_system" value="true"/>
            <column name="sort_order" value="1"/>
            <column name="status" value="ACTIVE"/>
            <column name="tenant_id" value="1"/>
            <column name="creator_id" value="1"/>
        </insert>
        
        <insert tableName="sys_roles">
            <column name="id" value="2"/>
            <column name="role_name" value="系统管理员"/>
            <column name="role_code" value="ADMIN"/>
            <column name="description" value="系统管理员"/>
            <column name="is_system" value="true"/>
            <column name="sort_order" value="2"/>
            <column name="status" value="ACTIVE"/>
            <column name="tenant_id" value="1"/>
            <column name="creator_id" value="1"/>
        </insert>
        
        <insert tableName="sys_roles">
            <column name="id" value="3"/>
            <column name="role_name" value="普通用户"/>
            <column name="role_code" value="USER"/>
            <column name="description" value="普通用户"/>
            <column name="is_system" value="false"/>
            <column name="sort_order" value="3"/>
            <column name="status" value="ACTIVE"/>
            <column name="tenant_id" value="1"/>
            <column name="creator_id" value="1"/>
        </insert>
    </changeSet>

    <!-- 初始化用户数据 -->
    <changeSet id="004-init-users" author="pisp-system">
        <!-- 默认密码为: admin123 -->
        <insert tableName="sys_users">
            <column name="id" value="1"/>
            <column name="username" value="admin"/>
            <column name="password_hash" value="$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi"/>
            <column name="email" value="<EMAIL>"/>
            <column name="phone" value="13800138000"/>
            <column name="real_name" value="系统管理员"/>
            <column name="status" value="ACTIVE"/>
            <column name="department_id" value="1"/>
            <column name="tenant_id" value="1"/>
            <column name="creator_id" value="1"/>
        </insert>
    </changeSet>

    <!-- 初始化角色权限关联 -->
    <changeSet id="005-init-role-permissions" author="pisp-system">
        <!-- 超级管理员拥有所有权限 -->
        <insert tableName="sys_role_permissions">
            <column name="role_id" value="1"/>
            <column name="permission_id" value="1"/>
            <column name="assigned_by" value="1"/>
            <column name="tenant_id" value="1"/>
            <column name="creator_id" value="1"/>
        </insert>
        <insert tableName="sys_role_permissions">
            <column name="role_id" value="1"/>
            <column name="permission_id" value="2"/>
            <column name="assigned_by" value="1"/>
            <column name="tenant_id" value="1"/>
            <column name="creator_id" value="1"/>
        </insert>
        <insert tableName="sys_role_permissions">
            <column name="role_id" value="1"/>
            <column name="permission_id" value="3"/>
            <column name="assigned_by" value="1"/>
            <column name="tenant_id" value="1"/>
            <column name="creator_id" value="1"/>
        </insert>
        <insert tableName="sys_role_permissions">
            <column name="role_id" value="1"/>
            <column name="permission_id" value="4"/>
            <column name="assigned_by" value="1"/>
            <column name="tenant_id" value="1"/>
            <column name="creator_id" value="1"/>
        </insert>
        <insert tableName="sys_role_permissions">
            <column name="role_id" value="1"/>
            <column name="permission_id" value="5"/>
            <column name="assigned_by" value="1"/>
            <column name="tenant_id" value="1"/>
            <column name="creator_id" value="1"/>
        </insert>
        <insert tableName="sys_role_permissions">
            <column name="role_id" value="1"/>
            <column name="permission_id" value="6"/>
            <column name="assigned_by" value="1"/>
            <column name="tenant_id" value="1"/>
            <column name="creator_id" value="1"/>
        </insert>
        <insert tableName="sys_role_permissions">
            <column name="role_id" value="1"/>
            <column name="permission_id" value="7"/>
            <column name="assigned_by" value="1"/>
            <column name="tenant_id" value="1"/>
            <column name="creator_id" value="1"/>
        </insert>
        <insert tableName="sys_role_permissions">
            <column name="role_id" value="1"/>
            <column name="permission_id" value="8"/>
            <column name="assigned_by" value="1"/>
            <column name="tenant_id" value="1"/>
            <column name="creator_id" value="1"/>
        </insert>
        <insert tableName="sys_role_permissions">
            <column name="role_id" value="1"/>
            <column name="permission_id" value="9"/>
            <column name="assigned_by" value="1"/>
            <column name="tenant_id" value="1"/>
            <column name="creator_id" value="1"/>
        </insert>
        <insert tableName="sys_role_permissions">
            <column name="role_id" value="1"/>
            <column name="permission_id" value="10"/>
            <column name="assigned_by" value="1"/>
            <column name="tenant_id" value="1"/>
            <column name="creator_id" value="1"/>
        </insert>
    </changeSet>

    <!-- 初始化用户角色关联 -->
    <changeSet id="006-init-user-roles" author="pisp-system">
        <!-- admin用户分配超级管理员角色 -->
        <insert tableName="sys_user_roles">
            <column name="user_id" value="1"/>
            <column name="role_id" value="1"/>
            <column name="assigned_by" value="1"/>
            <column name="tenant_id" value="1"/>
            <column name="creator_id" value="1"/>
        </insert>
    </changeSet>

</databaseChangeLog>
