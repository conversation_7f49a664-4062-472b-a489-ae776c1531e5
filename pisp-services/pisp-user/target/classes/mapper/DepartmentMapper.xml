<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdyl.erp.pisp.user.mapper.DepartmentMapper">

    <!-- Base Department Result Map -->
    <resultMap id="BaseDepartmentResult" type="com.bdyl.erp.pisp.user.entity.Department">
        <id property="id" column="id" />
        <result property="deptName" column="dept_name" />
        <result property="deptCode" column="dept_code" />
        <result property="parentId" column="parent_id" />
        <result property="deptLevel" column="dept_level" />
        <result property="deptPath" column="dept_path" />
        <result property="description" column="description" />
        <result property="leaderId" column="leader_id" />
        <result property="phone" column="phone" />
        <result property="email" column="email" />
        <result property="sortOrder" column="sort_order" />
        <result property="status" column="status" />
        <result property="remark" column="remark" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
        <result property="creatorId" column="creator_id" />
        <result property="updaterId" column="updater_id" />
        <result property="version" column="version" />
    </resultMap>

    <!-- Department with Leader Result Map -->
    <resultMap id="DepartmentWithLeaderResult" type="com.bdyl.erp.pisp.user.entity.Department"
        extends="BaseDepartmentResult">
        <association property="leader" javaType="com.bdyl.erp.pisp.user.entity.User">
            <id property="id" column="leader_user_id" />
            <result property="username" column="leader_username" />
            <result property="realName" column="leader_real_name" />
            <result property="email" column="leader_email" />
            <result property="phone" column="leader_phone" />
        </association>
    </resultMap>

    <!-- Department with Employee Count Result Map -->
    <resultMap id="DepartmentWithEmployeeCountResult"
        type="com.bdyl.erp.pisp.user.entity.Department" extends="DepartmentWithLeaderResult">
        <result property="employeeCount" column="employee_count" />
    </resultMap>

    <!-- Select department by code -->
    <select id="selectByDeptCode" parameterType="string" resultMap="BaseDepartmentResult"> SELECT *
        FROM sys_departments WHERE dept_code = #{deptCode} AND deleted = 0 </select>

    <!-- Select department by ID with leader info -->
    <select id="selectByIdWithLeader" parameterType="long" resultMap="DepartmentWithLeaderResult">
        SELECT d.*, u.id AS leader_user_id, u.username AS leader_username, u.real_name AS
        leader_real_name, u.email AS leader_email, u.phone AS leader_phone FROM sys_departments d
        LEFT JOIN sys_users u ON d.leader_id = u.id AND u.deleted = 0 WHERE d.id = #{deptId} AND
        d.deleted = 0 </select>

    <!-- Page query departments with leader info -->
    <select id="selectDepartmentPageWithLeader" resultMap="DepartmentWithLeaderResult"> SELECT d.*,
        u.id AS leader_user_id, u.username AS leader_username, u.real_name AS leader_real_name,
        u.email AS leader_email, u.phone AS leader_phone FROM sys_departments d LEFT JOIN sys_users
        u ON d.leader_id = u.id AND u.deleted = 0 WHERE d.deleted = 0 <if
            test="deptName != null and deptName != ''"> AND d.dept_name LIKE CONCAT('%',
        #{deptName}, '%') </if>
        <if test="deptCode != null and deptCode != ''"> AND d.dept_code LIKE
        CONCAT('%', #{deptCode}, '%') </if>
        <if test="parentId != null"> AND d.parent_id =
        #{parentId} </if>
        <if test="status != null and status != ''"> AND d.status = #{status} </if>
        ORDER BY d.sort_order ASC, d.create_time DESC </select>

    <!-- Select child departments by parent ID -->
    <select id="selectByParentId" parameterType="long" resultMap="BaseDepartmentResult"> SELECT *
        FROM sys_departments WHERE parent_id = #{parentId} AND deleted = 0 ORDER BY sort_order ASC,
        create_time ASC </select>

    <!-- Select department tree -->
    <select id="selectDepartmentTree" resultMap="DepartmentWithLeaderResult"> SELECT d.*, u.id AS
        leader_user_id, u.username AS leader_username, u.real_name AS leader_real_name, u.email AS
        leader_email, u.phone AS leader_phone FROM sys_departments d LEFT JOIN sys_users u ON
        d.leader_id = u.id AND u.deleted = 0 WHERE d.deleted = 0 AND d.status = 'ACTIVE' ORDER BY
        d.dept_level ASC, d.sort_order ASC, d.create_time ASC </select>

    <!-- Select department tree with employee count -->
    <select id="selectDepartmentTreeWithEmployeeCount" resultMap="DepartmentWithEmployeeCountResult">
        SELECT d.*, u.id AS leader_user_id, u.username AS leader_username, u.real_name AS
        leader_real_name, u.email AS leader_email, u.phone AS leader_phone,
        COALESCE(emp_count.employee_count, 0) AS employee_count FROM sys_departments d LEFT JOIN
        sys_users u ON d.leader_id = u.id AND u.deleted = 0 LEFT JOIN ( SELECT department_id,
        COUNT(*) AS employee_count FROM sys_users WHERE deleted = 0 AND status = 'ACTIVE' GROUP BY
        department_id ) emp_count ON d.id = emp_count.department_id WHERE d.deleted = 0 AND d.status
        = 'ACTIVE' ORDER BY d.dept_level ASC, d.sort_order ASC, d.create_time ASC </select>

    <!-- Check if department code exists -->
    <select id="checkDeptCodeExists" resultType="int"> SELECT COUNT(*) FROM sys_departments WHERE
        dept_code = #{deptCode} AND deleted = 0 <if test="excludeId != null"> AND id != #{excludeId} </if>
    </select>

    <!-- Check if department name exists -->
    <select id="checkDeptNameExists" resultType="int"> SELECT COUNT(*) FROM sys_departments WHERE
        dept_name = #{deptName} AND deleted = 0 <if test="parentId != null"> AND parent_id =
        #{parentId} </if>
        <if test="parentId == null"> AND parent_id IS NULL </if>
        <if
            test="excludeId != null"> AND id != #{excludeId} </if>
    </select>

    <!-- Batch update department status -->
    <update id="batchUpdateStatus"> UPDATE sys_departments SET status = #{status}, updater_id =
        #{updaterId}, update_time = NOW(), version = version + 1 WHERE id IN <foreach
            collection="deptIds" item="deptId" open="(" separator="," close=")"> #{deptId} </foreach>
        AND deleted = 0 </update>

    <!-- Select all active departments -->
    <select id="selectActiveDepartments" resultMap="BaseDepartmentResult"> SELECT * FROM
        sys_departments WHERE status = 'ACTIVE' AND deleted = 0 ORDER BY dept_level ASC, sort_order
        ASC, create_time ASC </select>

    <!-- Count employees by department ID -->
    <select id="countEmployeesByDeptId" parameterType="long" resultType="int"> SELECT COUNT(*) FROM
        sys_users WHERE department_id = #{deptId} AND deleted = 0 AND status = 'ACTIVE' </select>

    <!-- Select child department IDs -->
    <select id="selectChildDeptIds" parameterType="long" resultType="long"> SELECT id FROM
        sys_departments WHERE parent_id = #{deptId} AND deleted = 0 </select>

    <!-- Update department path -->
    <update id="updateDeptPath"> UPDATE sys_departments SET dept_path = #{deptPath}, update_time =
        NOW(), version = version + 1 WHERE id = #{deptId} AND deleted = 0 </update>

    <!-- Select department hierarchy -->
    <select id="selectDepartmentHierarchy" parameterType="long" resultMap="BaseDepartmentResult">
        SELECT * FROM sys_departments WHERE id = #{deptId} AND deleted = 0 </select>

    <!-- Select department by user ID -->
    <select id="selectDepartmentByUserId" parameterType="long" resultMap="BaseDepartmentResult">
        SELECT d.* FROM sys_departments d INNER JOIN sys_users u ON d.id = u.department_id WHERE
        u.id = #{userId} AND d.deleted = 0 AND u.deleted = 0 </select>

    <!-- Select root departments -->
    <select id="selectRootDepartments" resultMap="BaseDepartmentResult"> SELECT * FROM
        sys_departments WHERE parent_id IS NULL AND deleted = 0 AND status = 'ACTIVE' ORDER BY
        sort_order ASC, create_time ASC </select>

    <!-- Select departments by level -->
    <select id="selectDepartmentsByLevel" parameterType="int" resultMap="BaseDepartmentResult">
        SELECT * FROM sys_departments WHERE dept_level = #{level} AND deleted = 0 AND status =
        'ACTIVE' ORDER BY sort_order ASC, create_time ASC </select>

    <!-- Select department statistics -->
    <select id="selectDepartmentStatistics" resultType="map"> SELECT COUNT(*) AS total_count,
        COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) AS active_count, COUNT(CASE WHEN status =
        'INACTIVE' THEN 1 END) AS inactive_count, MAX(dept_level) AS max_level FROM sys_departments
        WHERE deleted = 0 </select>

</mapper> 