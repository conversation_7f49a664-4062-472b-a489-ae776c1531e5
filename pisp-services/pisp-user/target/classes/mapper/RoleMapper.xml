<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdyl.erp.pisp.user.mapper.RoleMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.bdyl.erp.pisp.user.entity.Role">
        <id column="id" property="id" />
        <result column="role_name" property="roleName" />
        <result column="role_code" property="roleCode" />
        <result column="description" property="description" />
        <result column="is_system" property="isSystem" />
        <result column="sort_order" property="sortOrder" />
        <result column="status" property="status" />
        <result column="version" property="version" />
        <result column="deleted" property="deleted" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator_id" property="creatorId" />
        <result column="updater_id" property="updaterId" />
        <result column="additional_info" property="additionalInfo" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 包含权限信息的结果映射 -->
    <resultMap id="RoleWithPermissionsResultMap" type="com.bdyl.erp.pisp.user.entity.Role" extends="BaseResultMap">
        <collection property="permissions" ofType="com.bdyl.erp.pisp.user.entity.Permission">
            <id column="perm_id" property="id" />
            <result column="permission_name" property="permissionName" />
            <result column="permission_code" property="permissionCode" />
            <result column="permission_type" property="permissionType" />
        </collection>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        r.id, r.role_name, r.role_code, r.description, r.is_system, r.sort_order,
        r.status, r.version, r.deleted, r.tenant_id, r.create_time, r.update_time,
        r.creator_id, r.updater_id, r.additional_info, r.remark
    </sql>

    <!-- 根据角色代码查询角色 -->
    <select id="selectByRoleCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_roles r
        WHERE r.role_code = #{roleCode} AND r.deleted = 0
    </select>

    <!-- 根据角色ID查询角色（包含权限信息） -->
    <select id="selectByIdWithPermissions" resultMap="RoleWithPermissionsResultMap">
        SELECT 
            <include refid="Base_Column_List" />,
            p.id as perm_id, p.permission_name, p.permission_code, p.permission_type
        FROM sys_roles r
        LEFT JOIN sys_role_permissions rp ON r.id = rp.role_id AND rp.deleted = 0
        LEFT JOIN sys_permissions p ON rp.permission_id = p.id AND p.deleted = 0
        WHERE r.id = #{roleId} AND r.deleted = 0
    </select>

    <!-- 分页查询角色列表 -->
    <select id="selectRolePage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_roles r
        <where>
            r.deleted = 0
            <if test="roleName != null and roleName != ''">
                AND r.role_name LIKE CONCAT('%', #{roleName}, '%')
            </if>
            <if test="roleCode != null and roleCode != ''">
                AND r.role_code LIKE CONCAT('%', #{roleCode}, '%')
            </if>
            <if test="status != null and status != ''">
                AND r.status = #{status}
            </if>
            <if test="isSystem != null">
                AND r.is_system = #{isSystem}
            </if>
        </where>
        ORDER BY r.sort_order, r.create_time DESC
    </select>

    <!-- 根据角色ID查询权限列表 -->
    <select id="selectPermissionsByRoleId" resultType="com.bdyl.erp.pisp.user.entity.Permission">
        SELECT p.id, p.permission_name, p.permission_code, p.description, p.permission_type,
               p.parent_id, p.permission_path, p.sort_order, p.is_system
        FROM sys_permissions p
        INNER JOIN sys_role_permissions rp ON p.id = rp.permission_id
        WHERE rp.role_id = #{roleId} AND p.deleted = 0 AND rp.deleted = 0
        ORDER BY p.sort_order, p.create_time
    </select>

    <!-- 根据用户ID查询角色列表 -->
    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_roles r
        INNER JOIN sys_user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = #{userId} AND r.deleted = 0 AND ur.deleted = 0
        ORDER BY r.sort_order, r.create_time
    </select>

    <!-- 检查角色代码是否存在 -->
    <select id="checkRoleCodeExists" resultType="int">
        SELECT COUNT(1)
        FROM sys_roles
        WHERE role_code = #{roleCode} AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查角色名称是否存在 -->
    <select id="checkRoleNameExists" resultType="int">
        SELECT COUNT(1)
        FROM sys_roles
        WHERE role_name = #{roleName} AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 批量更新角色状态 -->
    <update id="batchUpdateStatus">
        UPDATE sys_roles 
        SET status = #{status}, updater_id = #{updaterId}, update_time = NOW()
        WHERE id IN
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
        AND deleted = 0
    </update>

    <!-- 查询所有启用的角色 -->
    <select id="selectActiveRoles" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_roles r
        WHERE r.status = 'ACTIVE' AND r.deleted = 0
        ORDER BY r.sort_order, r.create_time
    </select>

    <!-- 根据角色ID列表查询用户数量 -->
    <select id="countUsersByRoleIds" resultType="int">
        SELECT COUNT(DISTINCT ur.user_id)
        FROM sys_user_roles ur
        INNER JOIN sys_users u ON ur.user_id = u.id
        WHERE ur.role_id IN
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
        AND ur.deleted = 0 AND u.deleted = 0
    </select>

</mapper>
