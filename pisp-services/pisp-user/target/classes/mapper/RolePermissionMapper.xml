<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdyl.erp.pisp.user.mapper.RolePermissionMapper">

    <!-- Base RolePermission Result Map -->
    <resultMap id="BaseRolePermissionResult" type="com.bdyl.erp.pisp.user.entity.RolePermission">
        <id property="id" column="id" />
        <result property="roleId" column="role_id" />
        <result property="permissionId" column="permission_id" />
        <result property="assignedAt" column="assigned_at" />
        <result property="assignedBy" column="assigned_by" />
        <result property="version" column="version" />
        <result property="deleted" column="deleted" />
        <result property="tenantId" column="tenant_id" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
        <result property="creatorId" column="creator_id" />
        <result property="updaterId" column="updater_id" />
        <result property="additionalInfo" column="additional_info" />
        <result property="remark" column="remark" />
    </resultMap>

    <!-- Role Permission Detail Result Map -->
    <resultMap id="RolePermissionDetailResult" type="com.bdyl.erp.pisp.user.entity.RolePermission"
        extends="BaseRolePermissionResult">
        <association property="role" javaType="com.bdyl.erp.pisp.user.entity.Role">
            <id property="id" column="role_id" />
            <result property="roleName" column="role_name" />
            <result property="roleCode" column="role_code" />
            <result property="description" column="role_description" />
        </association>
        <association property="permission" javaType="com.bdyl.erp.pisp.user.entity.Permission">
            <id property="id" column="permission_id" />
            <result property="permissionName" column="permission_name" />
            <result property="permissionCode" column="permission_code" />
            <result property="description" column="permission_description" />
        </association>
    </resultMap>

    <!-- 根据角色ID查询角色权限关联 -->
    <select id="selectByRoleId" resultMap="BaseRolePermissionResult"> SELECT id, role_id,
        permission_id, assigned_at, assigned_by, version, deleted, tenant_id, create_time,
        update_time, creator_id, updater_id, additional_info, remark FROM sys_role_permissions WHERE
        role_id = #{roleId} AND deleted = 0 ORDER BY create_time DESC </select>

    <!-- 根据权限ID查询角色权限关联 -->
    <select id="selectByPermissionId" resultMap="BaseRolePermissionResult"> SELECT id, role_id,
        permission_id, assigned_at, assigned_by, version, deleted, tenant_id, create_time,
        update_time, creator_id, updater_id, additional_info, remark FROM sys_role_permissions WHERE
        permission_id = #{permissionId} AND deleted = 0 ORDER BY create_time DESC </select>

    <!-- 根据用户ID查询角色权限关联 -->
    <select id="selectByUserId" resultMap="BaseRolePermissionResult"> SELECT DISTINCT rp.id,
        rp.role_id, rp.permission_id, rp.assigned_at, rp.assigned_by, rp.version, rp.deleted,
        rp.tenant_id, rp.create_time, rp.update_time, rp.creator_id, rp.updater_id,
        rp.additional_info, rp.remark FROM sys_role_permissions rp INNER JOIN sys_user_roles ur ON
        rp.role_id = ur.role_id WHERE ur.user_id = #{userId} AND rp.deleted = 0 AND ur.deleted = 0
        ORDER BY rp.create_time DESC </select>

    <!-- 查询角色权限详情（带角色和权限信息） -->
    <select id="selectRolePermissionDetails" resultMap="RolePermissionDetailResult"> SELECT rp.id,
        rp.role_id, rp.permission_id, rp.assigned_at, rp.assigned_by, rp.version, rp.deleted,
        rp.tenant_id, rp.create_time, rp.update_time, rp.creator_id, rp.updater_id,
        rp.additional_info, rp.remark, r.role_name, r.role_code, r.description as role_description,
        p.permission_name, p.permission_code, p.description as permission_description FROM
        sys_role_permissions rp LEFT JOIN sys_roles r ON rp.role_id = r.id AND r.deleted = 0 LEFT
        JOIN sys_permissions p ON rp.permission_id = p.id AND p.deleted = 0 WHERE rp.deleted = 0 <if
            test="roleId != null"> AND rp.role_id = #{roleId} </if>
        <if test="permissionId != null">
        AND rp.permission_id = #{permissionId} </if> ORDER BY rp.create_time DESC </select>

    <!-- 检查角色权限关联是否存在 -->
    <select id="checkRolePermissionExists" resultType="Integer"> SELECT COUNT(*) FROM
        sys_role_permissions WHERE role_id = #{roleId} AND permission_id = #{permissionId} AND
        deleted = 0 </select>

    <!-- 批量插入角色权限关联 -->
    <insert id="batchInsertRolePermissions"> INSERT INTO sys_role_permissions ( id, role_id,
        permission_id, assigned_at, assigned_by, create_time, update_time, creator_id, updater_id,
        tenant_id ) VALUES <foreach collection="rolePermissions" item="item" separator=","> (
        #{item.id}, #{item.roleId}, #{item.permissionId}, #{item.assignedAt}, #{item.assignedBy},
        #{item.createTime}, #{item.updateTime}, #{item.creatorId}, #{item.updaterId},
        #{item.tenantId} ) </foreach>
    </insert>

    <!-- 批量删除角色权限关联（逻辑删除） -->
    <update id="batchDeleteRolePermissions"> UPDATE sys_role_permissions SET deleted = 1,
        update_time = CURRENT_TIMESTAMP, updater_id = #{updaterId} WHERE id IN <foreach
            collection="ids" item="id" open="(" separator="," close=")"> #{id} </foreach> AND
        deleted = 0 </update>

    <!-- 根据角色ID删除所有权限关联（逻辑删除） -->
    <update id="deleteByRoleId"> UPDATE sys_role_permissions SET deleted = 1, update_time =
        CURRENT_TIMESTAMP, updater_id = #{updaterId} WHERE role_id = #{roleId} AND deleted = 0 </update>

    <!-- 根据权限ID删除所有角色关联（逻辑删除） -->
    <update id="deleteByPermissionId"> UPDATE sys_role_permissions SET deleted = 1, update_time =
        CURRENT_TIMESTAMP, updater_id = #{updaterId} WHERE permission_id = #{permissionId} AND
        deleted = 0 </update>

    <!-- 获取角色的权限数量 -->
    <select id="getRolePermissionCount" resultType="Integer"> SELECT COUNT(*) FROM
        sys_role_permissions WHERE role_id = #{roleId} AND deleted = 0 </select>

    <!-- 获取权限的角色数量 -->
    <select id="getPermissionRoleCount" resultType="Integer"> SELECT COUNT(*) FROM
        sys_role_permissions WHERE permission_id = #{permissionId} AND deleted = 0 </select>

</mapper> 