<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.bdyl.erp.pisp.user.controller.UserControllerTest" time="0.398" tests="15" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="21"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/target/test-classes:/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.4.7/spring-boot-starter-web-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.4.7/spring-boot-starter-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.4.7/spring-boot-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.4.7/spring-boot-starter-logging-3.4.7.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.5.18/logback-classic-1.5.18.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.4.7/spring-boot-starter-json-3.4.7.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.18.4/jackson-datatype-jdk8-2.18.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.18.4/jackson-module-parameter-names-2.18.4.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.2.8/spring-web-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.2.8/spring-beans-6.2.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.14.8/micrometer-observation-1.14.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.14.8/micrometer-commons-1.14.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.2.8/spring-webmvc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.2.8/spring-context-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.2.8/spring-expression-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-undertow/3.4.7/spring-boot-starter-undertow-3.4.7.jar:/Users/<USER>/.m2/repository/io/undertow/undertow-core/2.3.18.Final/undertow-core-2.3.18.Final.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.6.1.Final/jboss-logging-3.6.1.Final.jar:/Users/<USER>/.m2/repository/org/jboss/xnio/xnio-api/3.8.16.Final/xnio-api-3.8.16.Final.jar:/Users/<USER>/.m2/repository/org/wildfly/common/wildfly-common/1.5.4.Final/wildfly-common-1.5.4.Final.jar:/Users/<USER>/.m2/repository/org/wildfly/client/wildfly-client-config/1.0.1.Final/wildfly-client-config-1.0.1.Final.jar:/Users/<USER>/.m2/repository/org/jboss/xnio/xnio-nio/3.8.16.Final/xnio-nio-3.8.16.Final.jar:/Users/<USER>/.m2/repository/org/jboss/threads/jboss-threads/3.5.0.Final/jboss-threads-3.5.0.Final.jar:/Users/<USER>/.m2/repository/io/undertow/undertow-servlet/2.3.18.Final/undertow-servlet-2.3.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/servlet/jakarta.servlet-api/6.0.0/jakarta.servlet-api-6.0.0.jar:/Users/<USER>/.m2/repository/io/undertow/undertow-websockets-jsr/2.3.18.Final/undertow-websockets-jsr-2.3.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/websocket/jakarta.websocket-api/2.1.1/jakarta.websocket-api-2.1.1.jar:/Users/<USER>/.m2/repository/jakarta/websocket/jakarta.websocket-client-api/2.1.1/jakarta.websocket-client-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.42/tomcat-embed-el-10.1.42.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.4.7/spring-boot-starter-validation-3.4.7.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.2.Final/hibernate-validator-8.0.2.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.7.0/classmate-1.7.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.4.7/spring-boot-starter-security-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.2.8/spring-aop-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.4.7/spring-security-config-6.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.4.7/spring-security-web-6.4.7.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-discovery/2023.0.3.3/spring-cloud-starter-alibaba-nacos-discovery-2023.0.3.3.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-alibaba-commons/2023.0.3.3/spring-cloud-alibaba-commons-2023.0.3.3.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.4.2/nacos-client-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-auth-plugin/2.4.2/nacos-auth-plugin-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-encryption-plugin/2.4.2/nacos-encryption-plugin-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-logback-adapter-12/2.4.2/nacos-logback-adapter-12-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/logback-adapter/1.1.3/logback-adapter-1.1.3.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-log4j2-adapter/2.4.2/nacos-log4j2-adapter-2.4.2.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.17.2/commons-codec-1.17.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.18.4.1/jackson-core-2.18.4.1.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpasyncclient/4.1.5/httpasyncclient-4.1.5.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore-nio/4.4.16/httpcore-nio-4.4.16.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient/0.16.0/simpleclient-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_otel/0.16.0/simpleclient_tracer_otel-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_common/0.16.0/simpleclient_tracer_common-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_otel_agent/0.16.0/simpleclient_tracer_otel_agent-0.16.0.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.14.8/micrometer-core-1.14.8.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.2.2/HdrHistogram-2.2.2.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-commons/4.2.1/spring-cloud-commons-4.2.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.4.7/spring-security-crypto-6.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-context/4.2.1/spring-cloud-context-4.2.1.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-config/2023.0.3.3/spring-cloud-starter-alibaba-nacos-config-2023.0.3.3.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-alibaba-nacos-config/2023.0.3.3/spring-alibaba-nacos-config-2023.0.3.3.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jdbc/3.4.7/spring-boot-starter-data-jdbc-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.4.7/spring-boot-starter-jdbc-3.4.7.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/5.1.0/HikariCP-5.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.2.8/spring-jdbc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jdbc/3.4.7/spring-data-jdbc-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-relational/3.4.7/spring-data-relational-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.4.7/spring-data-commons-3.4.7.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter/3.5.12/mybatis-plus-spring-boot3-starter-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.12/mybatis-plus-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.12/mybatis-plus-core-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.12/mybatis-plus-annotation-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring/3.5.12/mybatis-plus-spring-3.5.12.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.19/mybatis-3.5.19.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/3.0.4/mybatis-spring-3.0.4.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.12/mybatis-plus-spring-boot-autoconfigure-3.5.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.4.7/spring-boot-autoconfigure-3.4.7.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-jsqlparser/3.5.12/mybatis-plus-jsqlparser-3.5.12.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/5.1/jsqlparser-5.1.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-jsqlparser-common/3.5.12/mybatis-plus-jsqlparser-common-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.12/mybatis-plus-extension-3.5.12.jar:/Users/<USER>/.m2/repository/org/postgresql/postgresql/42.7.7/postgresql-42.7.7.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.49.3/checker-qual-3.49.3.jar:/Users/<USER>/.m2/repository/org/liquibase/liquibase-core/4.29.2/liquibase-core-4.29.2.jar:/Users/<USER>/.m2/repository/com/opencsv/opencsv/5.9/opencsv-5.9.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.3/snakeyaml-2.3.jar:/Users/<USER>/.m2/repository/javax/xml/bind/jaxb-api/2.3.1/jaxb-api-2.3.1.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.17.0/commons-io-2.17.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.5.0/commons-collections4-4.5.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.13.1/commons-text-1.13.1.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar:/Users/<USER>/.m2/repository/com/bdyl/erp/pisp/pisp-common-core/1.0.0-SNAPSHOT/pisp-common-core-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.38/hutool-all-5.8.38.jar:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.6.3/mapstruct-1.6.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.18.4/jackson-databind-2.18.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.18.4/jackson-annotations-2.18.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.18.4/jackson-datatype-jsr310-2.18.4.jar:/Users/<USER>/.m2/repository/com/bdyl/erp/pisp/pisp-common-security/1.0.0-SNAPSHOT/pisp-common-security-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.12.6/jjwt-api-0.12.6.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.12.6/jjwt-impl-0.12.6.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.12.6/jjwt-jackson-0.12.6.jar:/Users/<USER>/.m2/repository/com/bdyl/erp/pisp/pisp-common-web/1.0.0-SNAPSHOT/pisp-common-web-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/com/bdyl/erp/pisp/pisp-common-redis/1.0.0-SNAPSHOT/pisp-common-redis-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/3.4.7/spring-boot-starter-data-redis-3.4.7.jar:/Users/<USER>/.m2/repository/io/lettuce/lettuce-core/6.4.2.RELEASE/lettuce-core-6.4.2.RELEASE.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.122.Final/netty-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.122.Final/netty-handler-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.122.Final/netty-resolver-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.122.Final/netty-buffer-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.122.Final/netty-transport-native-unix-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.122.Final/netty-codec-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.122.Final/netty-transport-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.7.7/reactor-core-3.7.7.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/3.4.7/spring-data-redis-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/3.4.7/spring-data-keyvalue-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.2.8/spring-oxm-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.2.8/spring-context-support-6.2.8.jar:/Users/<USER>/.m2/repository/com/bdyl/erp/pisp/pisp-api/1.0.0-SNAPSHOT/pisp-api-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.4.7/spring-boot-starter-test-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.4.7/spring-boot-test-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.4.7/spring-boot-test-autoconfigure-3.4.7.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/9.7.1/asm-9.7.1.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.26.3/assertj-core-3.26.3.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.15.11/byte-buddy-1.15.11.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.11.4/junit-jupiter-5.11.4.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.11.4/junit-jupiter-api-5.11.4.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.11.4/junit-platform-commons-1.11.4.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.11.4/junit-jupiter-params-5.11.4.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.11.4/junit-jupiter-engine-5.11.4.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.11.4/junit-platform-engine-1.11.4.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.14.2/mockito-core-5.14.2.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.15.11/byte-buddy-agent-1.15.11.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.14.2/mockito-junit-jupiter-5.14.2.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.2.8/spring-core-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.2.8/spring-jcl-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.2.8/spring-test-6.2.8.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.10.2/xmlunit-core-2.10.2.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-test/6.4.7/spring-security-test-6.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.4.7/spring-security-core-6.4.7.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter-test/3.5.12/mybatis-plus-spring-boot3-starter-test-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-test-autoconfigure/3.5.12/mybatis-plus-spring-boot-test-autoconfigure-3.5.12.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.2.8/spring-tx-6.2.8.jar:/Users/<USER>/.m2/repository/org/testcontainers/postgresql/1.20.6/postgresql-1.20.6.jar:/Users/<USER>/.m2/repository/org/testcontainers/jdbc/1.20.6/jdbc-1.20.6.jar:/Users/<USER>/.m2/repository/org/testcontainers/database-commons/1.20.6/database-commons-1.20.6.jar:/Users/<USER>/.m2/repository/org/testcontainers/testcontainers/1.20.6/testcontainers-1.20.6.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/repository/org/rnorth/duct-tape/duct-tape/1.0.8/duct-tape-1.0.8.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/17.0.0/annotations-17.0.0.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-api/3.4.1/docker-java-api-3.4.1.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-transport-zerodep/3.4.1/docker-java-transport-zerodep-3.4.1.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-transport/3.4.1/docker-java-transport-3.4.1.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.13.0/jna-5.13.0.jar:/Users/<USER>/.m2/repository/com/h2database/h2/2.3.232/h2-2.3.232.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.38/lombok-1.18.38.jar:"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="org.jboss.logging.provider" value="slf4j"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="21"/>
    <property name="APPLICATION_NAME" value="pisp-user-service-test"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="/Users/<USER>/.sdkman/candidates/java/21.0.7-oracle/lib"/>
    <property name="sun.java.command" value="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/target/surefire/surefirebooter-20250630213518713_3.jar /Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/target/surefire 2025-06-30T21-35-18_678-jvmRun1 surefire-20250630213518713_1tmp surefire_0-20250630213518713_2tmp"/>
    <property name="http.nonProxyHosts" value="127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|&lt;local&gt;|*.&lt;local&gt;"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/target/test-classes:/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.4.7/spring-boot-starter-web-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.4.7/spring-boot-starter-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.4.7/spring-boot-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.4.7/spring-boot-starter-logging-3.4.7.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.5.18/logback-classic-1.5.18.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.4.7/spring-boot-starter-json-3.4.7.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.18.4/jackson-datatype-jdk8-2.18.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.18.4/jackson-module-parameter-names-2.18.4.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.2.8/spring-web-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.2.8/spring-beans-6.2.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.14.8/micrometer-observation-1.14.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.14.8/micrometer-commons-1.14.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.2.8/spring-webmvc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.2.8/spring-context-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.2.8/spring-expression-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-undertow/3.4.7/spring-boot-starter-undertow-3.4.7.jar:/Users/<USER>/.m2/repository/io/undertow/undertow-core/2.3.18.Final/undertow-core-2.3.18.Final.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.6.1.Final/jboss-logging-3.6.1.Final.jar:/Users/<USER>/.m2/repository/org/jboss/xnio/xnio-api/3.8.16.Final/xnio-api-3.8.16.Final.jar:/Users/<USER>/.m2/repository/org/wildfly/common/wildfly-common/1.5.4.Final/wildfly-common-1.5.4.Final.jar:/Users/<USER>/.m2/repository/org/wildfly/client/wildfly-client-config/1.0.1.Final/wildfly-client-config-1.0.1.Final.jar:/Users/<USER>/.m2/repository/org/jboss/xnio/xnio-nio/3.8.16.Final/xnio-nio-3.8.16.Final.jar:/Users/<USER>/.m2/repository/org/jboss/threads/jboss-threads/3.5.0.Final/jboss-threads-3.5.0.Final.jar:/Users/<USER>/.m2/repository/io/undertow/undertow-servlet/2.3.18.Final/undertow-servlet-2.3.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/servlet/jakarta.servlet-api/6.0.0/jakarta.servlet-api-6.0.0.jar:/Users/<USER>/.m2/repository/io/undertow/undertow-websockets-jsr/2.3.18.Final/undertow-websockets-jsr-2.3.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/websocket/jakarta.websocket-api/2.1.1/jakarta.websocket-api-2.1.1.jar:/Users/<USER>/.m2/repository/jakarta/websocket/jakarta.websocket-client-api/2.1.1/jakarta.websocket-client-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.42/tomcat-embed-el-10.1.42.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.4.7/spring-boot-starter-validation-3.4.7.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.2.Final/hibernate-validator-8.0.2.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.7.0/classmate-1.7.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.4.7/spring-boot-starter-security-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.2.8/spring-aop-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.4.7/spring-security-config-6.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.4.7/spring-security-web-6.4.7.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-discovery/2023.0.3.3/spring-cloud-starter-alibaba-nacos-discovery-2023.0.3.3.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-alibaba-commons/2023.0.3.3/spring-cloud-alibaba-commons-2023.0.3.3.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.4.2/nacos-client-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-auth-plugin/2.4.2/nacos-auth-plugin-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-encryption-plugin/2.4.2/nacos-encryption-plugin-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-logback-adapter-12/2.4.2/nacos-logback-adapter-12-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/logback-adapter/1.1.3/logback-adapter-1.1.3.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-log4j2-adapter/2.4.2/nacos-log4j2-adapter-2.4.2.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.17.2/commons-codec-1.17.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.18.4.1/jackson-core-2.18.4.1.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpasyncclient/4.1.5/httpasyncclient-4.1.5.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore-nio/4.4.16/httpcore-nio-4.4.16.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient/0.16.0/simpleclient-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_otel/0.16.0/simpleclient_tracer_otel-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_common/0.16.0/simpleclient_tracer_common-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_otel_agent/0.16.0/simpleclient_tracer_otel_agent-0.16.0.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.14.8/micrometer-core-1.14.8.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.2.2/HdrHistogram-2.2.2.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-commons/4.2.1/spring-cloud-commons-4.2.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.4.7/spring-security-crypto-6.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-context/4.2.1/spring-cloud-context-4.2.1.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-config/2023.0.3.3/spring-cloud-starter-alibaba-nacos-config-2023.0.3.3.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-alibaba-nacos-config/2023.0.3.3/spring-alibaba-nacos-config-2023.0.3.3.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jdbc/3.4.7/spring-boot-starter-data-jdbc-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.4.7/spring-boot-starter-jdbc-3.4.7.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/5.1.0/HikariCP-5.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.2.8/spring-jdbc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jdbc/3.4.7/spring-data-jdbc-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-relational/3.4.7/spring-data-relational-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.4.7/spring-data-commons-3.4.7.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter/3.5.12/mybatis-plus-spring-boot3-starter-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.12/mybatis-plus-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.12/mybatis-plus-core-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.12/mybatis-plus-annotation-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring/3.5.12/mybatis-plus-spring-3.5.12.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.19/mybatis-3.5.19.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/3.0.4/mybatis-spring-3.0.4.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.12/mybatis-plus-spring-boot-autoconfigure-3.5.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.4.7/spring-boot-autoconfigure-3.4.7.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-jsqlparser/3.5.12/mybatis-plus-jsqlparser-3.5.12.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/5.1/jsqlparser-5.1.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-jsqlparser-common/3.5.12/mybatis-plus-jsqlparser-common-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.12/mybatis-plus-extension-3.5.12.jar:/Users/<USER>/.m2/repository/org/postgresql/postgresql/42.7.7/postgresql-42.7.7.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.49.3/checker-qual-3.49.3.jar:/Users/<USER>/.m2/repository/org/liquibase/liquibase-core/4.29.2/liquibase-core-4.29.2.jar:/Users/<USER>/.m2/repository/com/opencsv/opencsv/5.9/opencsv-5.9.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.3/snakeyaml-2.3.jar:/Users/<USER>/.m2/repository/javax/xml/bind/jaxb-api/2.3.1/jaxb-api-2.3.1.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.17.0/commons-io-2.17.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.5.0/commons-collections4-4.5.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.13.1/commons-text-1.13.1.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar:/Users/<USER>/.m2/repository/com/bdyl/erp/pisp/pisp-common-core/1.0.0-SNAPSHOT/pisp-common-core-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.38/hutool-all-5.8.38.jar:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.6.3/mapstruct-1.6.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.18.4/jackson-databind-2.18.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.18.4/jackson-annotations-2.18.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.18.4/jackson-datatype-jsr310-2.18.4.jar:/Users/<USER>/.m2/repository/com/bdyl/erp/pisp/pisp-common-security/1.0.0-SNAPSHOT/pisp-common-security-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.12.6/jjwt-api-0.12.6.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.12.6/jjwt-impl-0.12.6.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.12.6/jjwt-jackson-0.12.6.jar:/Users/<USER>/.m2/repository/com/bdyl/erp/pisp/pisp-common-web/1.0.0-SNAPSHOT/pisp-common-web-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/com/bdyl/erp/pisp/pisp-common-redis/1.0.0-SNAPSHOT/pisp-common-redis-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/3.4.7/spring-boot-starter-data-redis-3.4.7.jar:/Users/<USER>/.m2/repository/io/lettuce/lettuce-core/6.4.2.RELEASE/lettuce-core-6.4.2.RELEASE.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.122.Final/netty-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.122.Final/netty-handler-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.122.Final/netty-resolver-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.122.Final/netty-buffer-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.122.Final/netty-transport-native-unix-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.122.Final/netty-codec-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.122.Final/netty-transport-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.7.7/reactor-core-3.7.7.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/3.4.7/spring-data-redis-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/3.4.7/spring-data-keyvalue-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.2.8/spring-oxm-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.2.8/spring-context-support-6.2.8.jar:/Users/<USER>/.m2/repository/com/bdyl/erp/pisp/pisp-api/1.0.0-SNAPSHOT/pisp-api-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.4.7/spring-boot-starter-test-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.4.7/spring-boot-test-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.4.7/spring-boot-test-autoconfigure-3.4.7.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/9.7.1/asm-9.7.1.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.26.3/assertj-core-3.26.3.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.15.11/byte-buddy-1.15.11.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.11.4/junit-jupiter-5.11.4.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.11.4/junit-jupiter-api-5.11.4.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.11.4/junit-platform-commons-1.11.4.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.11.4/junit-jupiter-params-5.11.4.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.11.4/junit-jupiter-engine-5.11.4.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.11.4/junit-platform-engine-1.11.4.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.14.2/mockito-core-5.14.2.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.15.11/byte-buddy-agent-1.15.11.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.14.2/mockito-junit-jupiter-5.14.2.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.2.8/spring-core-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.2.8/spring-jcl-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.2.8/spring-test-6.2.8.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.10.2/xmlunit-core-2.10.2.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-test/6.4.7/spring-security-test-6.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.4.7/spring-security-core-6.4.7.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter-test/3.5.12/mybatis-plus-spring-boot3-starter-test-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-test-autoconfigure/3.5.12/mybatis-plus-spring-boot-test-autoconfigure-3.5.12.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.2.8/spring-tx-6.2.8.jar:/Users/<USER>/.m2/repository/org/testcontainers/postgresql/1.20.6/postgresql-1.20.6.jar:/Users/<USER>/.m2/repository/org/testcontainers/jdbc/1.20.6/jdbc-1.20.6.jar:/Users/<USER>/.m2/repository/org/testcontainers/database-commons/1.20.6/database-commons-1.20.6.jar:/Users/<USER>/.m2/repository/org/testcontainers/testcontainers/1.20.6/testcontainers-1.20.6.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/repository/org/rnorth/duct-tape/duct-tape/1.0.8/duct-tape-1.0.8.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/17.0.0/annotations-17.0.0.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-api/3.4.1/docker-java-api-3.4.1.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-transport-zerodep/3.4.1/docker-java-transport-zerodep-3.4.1.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-transport/3.4.1/docker-java-transport-3.4.1.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.13.0/jna-5.13.0.jar:/Users/<USER>/.m2/repository/com/h2database/h2/2.3.232/h2-2.3.232.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.38/lombok-1.18.38.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="zh"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-04-15"/>
    <property name="java.home" value="/Users/<USER>/.sdkman/candidates/java/21.0.7-oracle"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user"/>
    <property name="java.vm.compressedOopsMode" value="Non-zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="checkstyle.skip" value="true"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="FILE_LOG_CHARSET" value="UTF-8"/>
    <property name="java.awt.headless" value="true"/>
    <property name="apple.awt.application.name" value="ForkedBooter"/>
    <property name="surefire.real.class.path" value="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/target/surefire/surefirebooter-20250630213518713_3.jar"/>
    <property name="user.script" value="Hans"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|&lt;local&gt;|*.&lt;local&gt;"/>
    <property name="java.runtime.version" value="21.0.7+8-LTS-245"/>
    <property name="user.name" value="jeffery"/>
    <property name="stdout.encoding" value="UTF-8"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.5"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="/var/folders/2c/k8bwfr1j5cz_4rmd02kztnph0000gn/T/"/>
    <property name="com.zaxxer.hikari.pool_number" value="1"/>
    <property name="java.version" value="21.0.7"/>
    <property name="user.dir" value="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user"/>
    <property name="os.arch" value="aarch64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="PID" value="13932"/>
    <property name="CONSOLE_LOG_CHARSET" value="UTF-8"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="UTF-8"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="21.0.7+8-LTS-245"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="socksNonProxyHosts" value="127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|&lt;local&gt;|*.&lt;local&gt;"/>
    <property name="java.class.version" value="65.0"/>
    <property name="LOGGED_APPLICATION_NAME" value="[pisp-user-service-test] "/>
  </properties>
  <testcase name="testLockUser" classname="com.bdyl.erp.pisp.user.controller.UserControllerTest" time="0.227">
    <system-out><![CDATA[2025-06-30T21:35:22.598+08:00  INFO 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Initializing Servlet ''
2025-06-30T21:35:22.599+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected AcceptHeaderLocaleResolver
2025-06-30T21:35:22.599+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected FixedThemeResolver
2025-06-30T21:35:22.599+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@4606e1a6
2025-06-30T21:35:22.599+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected org.springframework.web.servlet.support.SessionFlashMapManager@69a8617c
2025-06-30T21:35:22.599+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-30T21:35:22.599+08:00  INFO 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Completed initialization in 1 ms
2025-06-30T21:35:22.613+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : PUT "/api/users/1/lock", parameters={}
2025-06-30T21:35:22.624+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.user.controller.UserController   : 锁定用户请求: userId=1
2025-06-30T21:35:22.646+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Completed 200 OK
]]></system-out>
  </testcase>
  <testcase name="testDeactivateUser" classname="com.bdyl.erp.pisp.user.controller.UserControllerTest" time="0.011">
    <system-out><![CDATA[2025-06-30T21:35:22.675+08:00  INFO 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Initializing Servlet ''
2025-06-30T21:35:22.675+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected AcceptHeaderLocaleResolver
2025-06-30T21:35:22.675+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected FixedThemeResolver
2025-06-30T21:35:22.675+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@2b1aa390
2025-06-30T21:35:22.675+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected org.springframework.web.servlet.support.SessionFlashMapManager@5e1d6ace
2025-06-30T21:35:22.675+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-30T21:35:22.675+08:00  INFO 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Completed initialization in 0 ms
2025-06-30T21:35:22.676+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : PUT "/api/users/1/deactivate", parameters={}
2025-06-30T21:35:22.676+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.user.controller.UserController   : 停用用户请求: userId=1
2025-06-30T21:35:22.678+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Completed 200 OK
]]></system-out>
  </testcase>
  <testcase name="testUpdateUser" classname="com.bdyl.erp.pisp.user.controller.UserControllerTest" time="0.055">
    <system-out><![CDATA[2025-06-30T21:35:22.685+08:00  INFO 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Initializing Servlet ''
2025-06-30T21:35:22.685+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected AcceptHeaderLocaleResolver
2025-06-30T21:35:22.685+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected FixedThemeResolver
2025-06-30T21:35:22.685+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@2574123a
2025-06-30T21:35:22.685+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected org.springframework.web.servlet.support.SessionFlashMapManager@1bc467bd
2025-06-30T21:35:22.685+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-30T21:35:22.685+08:00  INFO 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Completed initialization in 0 ms
2025-06-30T21:35:22.691+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : PUT "/api/users/1", parameters={}
2025-06-30T21:35:22.728+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.user.controller.UserController   : 更新用户请求: userId=1
2025-06-30T21:35:22.732+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Completed 200 OK
]]></system-out>
  </testcase>
  <testcase name="testCheckEmailExists" classname="com.bdyl.erp.pisp.user.controller.UserControllerTest" time="0.009">
    <system-out><![CDATA[2025-06-30T21:35:22.740+08:00  INFO 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Initializing Servlet ''
2025-06-30T21:35:22.740+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected AcceptHeaderLocaleResolver
2025-06-30T21:35:22.740+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected FixedThemeResolver
2025-06-30T21:35:22.740+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@7bec83d5
2025-06-30T21:35:22.740+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected org.springframework.web.servlet.support.SessionFlashMapManager@4485ca30
2025-06-30T21:35:22.740+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-30T21:35:22.740+08:00  INFO 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Completed initialization in 0 ms
2025-06-30T21:35:22.740+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : GET "/api/users/check/email", parameters={masked}
2025-06-30T21:35:22.742+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Completed 200 OK
]]></system-out>
  </testcase>
  <testcase name="testCheckUsernameExists" classname="com.bdyl.erp.pisp.user.controller.UserControllerTest" time="0.007">
    <system-out><![CDATA[2025-06-30T21:35:22.747+08:00  INFO 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Initializing Servlet ''
2025-06-30T21:35:22.747+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected AcceptHeaderLocaleResolver
2025-06-30T21:35:22.747+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected FixedThemeResolver
2025-06-30T21:35:22.747+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@1c7aa676
2025-06-30T21:35:22.747+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected org.springframework.web.servlet.support.SessionFlashMapManager@fa200ab
2025-06-30T21:35:22.748+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-30T21:35:22.748+08:00  INFO 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Completed initialization in 1 ms
2025-06-30T21:35:22.748+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : GET "/api/users/check/username", parameters={masked}
2025-06-30T21:35:22.749+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Completed 200 OK
]]></system-out>
  </testcase>
  <testcase name="testChangePassword" classname="com.bdyl.erp.pisp.user.controller.UserControllerTest" time="0.009">
    <system-out><![CDATA[2025-06-30T21:35:22.754+08:00  INFO 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Initializing Servlet ''
2025-06-30T21:35:22.754+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected AcceptHeaderLocaleResolver
2025-06-30T21:35:22.754+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected FixedThemeResolver
2025-06-30T21:35:22.754+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@428943a4
2025-06-30T21:35:22.754+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected org.springframework.web.servlet.support.SessionFlashMapManager@6ca95b1e
2025-06-30T21:35:22.754+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-30T21:35:22.754+08:00  INFO 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Completed initialization in 0 ms
2025-06-30T21:35:22.756+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : PUT "/api/users/password/change", parameters={}
2025-06-30T21:35:22.758+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.user.controller.UserController   : 修改密码请求: userId=1
2025-06-30T21:35:22.759+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Completed 200 OK
]]></system-out>
  </testcase>
  <testcase name="testActivateUser" classname="com.bdyl.erp.pisp.user.controller.UserControllerTest" time="0.007">
    <system-out><![CDATA[2025-06-30T21:35:22.764+08:00  INFO 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Initializing Servlet ''
2025-06-30T21:35:22.764+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected AcceptHeaderLocaleResolver
2025-06-30T21:35:22.764+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected FixedThemeResolver
2025-06-30T21:35:22.764+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@4a842e8a
2025-06-30T21:35:22.764+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected org.springframework.web.servlet.support.SessionFlashMapManager@6b909973
2025-06-30T21:35:22.764+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-30T21:35:22.764+08:00  INFO 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Completed initialization in 0 ms
2025-06-30T21:35:22.765+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : PUT "/api/users/1/activate", parameters={}
2025-06-30T21:35:22.765+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.user.controller.UserController   : 激活用户请求: userId=1
2025-06-30T21:35:22.766+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Completed 200 OK
]]></system-out>
  </testcase>
  <testcase name="testBatchDeleteUsers" classname="com.bdyl.erp.pisp.user.controller.UserControllerTest" time="0.009">
    <system-out><![CDATA[2025-06-30T21:35:22.771+08:00  INFO 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Initializing Servlet ''
2025-06-30T21:35:22.771+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected AcceptHeaderLocaleResolver
2025-06-30T21:35:22.771+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected FixedThemeResolver
2025-06-30T21:35:22.771+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@59f2a9e9
2025-06-30T21:35:22.771+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected org.springframework.web.servlet.support.SessionFlashMapManager@6c37b0de
2025-06-30T21:35:22.771+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-30T21:35:22.771+08:00  INFO 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Completed initialization in 0 ms
2025-06-30T21:35:22.774+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : DELETE "/api/users/batch", parameters={}
2025-06-30T21:35:22.775+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.user.controller.UserController   : 批量删除用户请求: userIds=[1, 2, 3]
2025-06-30T21:35:22.776+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Completed 200 OK
]]></system-out>
  </testcase>
  <testcase name="testGetUsersByDepartmentId" classname="com.bdyl.erp.pisp.user.controller.UserControllerTest" time="0.014">
    <system-out><![CDATA[2025-06-30T21:35:22.780+08:00  INFO 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Initializing Servlet ''
2025-06-30T21:35:22.781+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected AcceptHeaderLocaleResolver
2025-06-30T21:35:22.781+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected FixedThemeResolver
2025-06-30T21:35:22.781+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@3094111c
2025-06-30T21:35:22.781+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected org.springframework.web.servlet.support.SessionFlashMapManager@776e4088
2025-06-30T21:35:22.781+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-30T21:35:22.781+08:00  INFO 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Completed initialization in 1 ms
2025-06-30T21:35:22.781+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : GET "/api/users/department/1", parameters={}
2025-06-30T21:35:22.784+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Completed 200 OK
]]></system-out>
  </testcase>
  <testcase name="testCheckPhoneExists" classname="com.bdyl.erp.pisp.user.controller.UserControllerTest" time="0.006">
    <system-out><![CDATA[2025-06-30T21:35:22.795+08:00  INFO 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Initializing Servlet ''
2025-06-30T21:35:22.795+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected AcceptHeaderLocaleResolver
2025-06-30T21:35:22.795+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected FixedThemeResolver
2025-06-30T21:35:22.795+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@524ba12b
2025-06-30T21:35:22.795+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected org.springframework.web.servlet.support.SessionFlashMapManager@685783b1
2025-06-30T21:35:22.795+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-30T21:35:22.795+08:00  INFO 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Completed initialization in 0 ms
2025-06-30T21:35:22.796+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : GET "/api/users/check/phone", parameters={masked}
2025-06-30T21:35:22.797+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Completed 200 OK
]]></system-out>
  </testcase>
  <testcase name="testCreateUser" classname="com.bdyl.erp.pisp.user.controller.UserControllerTest" time="0.011">
    <system-out><![CDATA[2025-06-30T21:35:22.801+08:00  INFO 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Initializing Servlet ''
2025-06-30T21:35:22.801+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected AcceptHeaderLocaleResolver
2025-06-30T21:35:22.801+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected FixedThemeResolver
2025-06-30T21:35:22.801+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@67dc7d0
2025-06-30T21:35:22.801+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected org.springframework.web.servlet.support.SessionFlashMapManager@46b2e0e4
2025-06-30T21:35:22.801+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-30T21:35:22.802+08:00  INFO 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Completed initialization in 1 ms
2025-06-30T21:35:22.803+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : POST "/api/users", parameters={}
2025-06-30T21:35:22.807+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.user.controller.UserController   : 创建用户请求: newuser
2025-06-30T21:35:22.809+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Completed 200 OK
]]></system-out>
  </testcase>
  <testcase name="testDeleteUser" classname="com.bdyl.erp.pisp.user.controller.UserControllerTest" time="0.006">
    <system-out><![CDATA[2025-06-30T21:35:22.813+08:00  INFO 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Initializing Servlet ''
2025-06-30T21:35:22.813+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected AcceptHeaderLocaleResolver
2025-06-30T21:35:22.813+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected FixedThemeResolver
2025-06-30T21:35:22.813+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@47d0127f
2025-06-30T21:35:22.813+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected org.springframework.web.servlet.support.SessionFlashMapManager@c4ef770
2025-06-30T21:35:22.813+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-30T21:35:22.813+08:00  INFO 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Completed initialization in 0 ms
2025-06-30T21:35:22.813+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : DELETE "/api/users/1", parameters={}
2025-06-30T21:35:22.814+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.user.controller.UserController   : 删除用户请求: userId=1
2025-06-30T21:35:22.814+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Completed 200 OK
]]></system-out>
  </testcase>
  <testcase name="testGetUserById" classname="com.bdyl.erp.pisp.user.controller.UserControllerTest" time="0.005">
    <system-out><![CDATA[2025-06-30T21:35:22.818+08:00  INFO 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Initializing Servlet ''
2025-06-30T21:35:22.818+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected AcceptHeaderLocaleResolver
2025-06-30T21:35:22.818+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected FixedThemeResolver
2025-06-30T21:35:22.818+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@7a6433f3
2025-06-30T21:35:22.818+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected org.springframework.web.servlet.support.SessionFlashMapManager@78b906d
2025-06-30T21:35:22.818+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-30T21:35:22.818+08:00  INFO 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Completed initialization in 0 ms
2025-06-30T21:35:22.818+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : GET "/api/users/1", parameters={}
2025-06-30T21:35:22.820+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Completed 200 OK
]]></system-out>
  </testcase>
  <testcase name="testGetUserPage" classname="com.bdyl.erp.pisp.user.controller.UserControllerTest" time="0.012">
    <system-out><![CDATA[2025-06-30T21:35:22.824+08:00  INFO 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Initializing Servlet ''
2025-06-30T21:35:22.824+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected AcceptHeaderLocaleResolver
2025-06-30T21:35:22.824+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected FixedThemeResolver
2025-06-30T21:35:22.824+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@52d04234
2025-06-30T21:35:22.824+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected org.springframework.web.servlet.support.SessionFlashMapManager@75f5293c
2025-06-30T21:35:22.824+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-30T21:35:22.824+08:00  INFO 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Completed initialization in 0 ms
2025-06-30T21:35:22.824+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : GET "/api/users", parameters={masked}
2025-06-30T21:35:22.831+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Completed 200 OK
]]></system-out>
  </testcase>
  <testcase name="testResetPassword" classname="com.bdyl.erp.pisp.user.controller.UserControllerTest" time="0.005">
    <system-out><![CDATA[2025-06-30T21:35:22.835+08:00  INFO 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Initializing Servlet ''
2025-06-30T21:35:22.835+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected AcceptHeaderLocaleResolver
2025-06-30T21:35:22.835+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected FixedThemeResolver
2025-06-30T21:35:22.835+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@38f0de07
2025-06-30T21:35:22.835+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Detected org.springframework.web.servlet.support.SessionFlashMapManager@21e5feb
2025-06-30T21:35:22.835+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-30T21:35:22.835+08:00  INFO 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Completed initialization in 0 ms
2025-06-30T21:35:22.835+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : PUT "/api/users/1/password/reset", parameters={masked}
2025-06-30T21:35:22.836+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.user.controller.UserController   : 重置密码请求: userId=1
2025-06-30T21:35:22.837+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.web.servlet.TestDispatcherServlet  : Completed 200 OK
]]></system-out>
  </testcase>
</testsuite>