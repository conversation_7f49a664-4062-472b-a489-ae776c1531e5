<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest" time="3.318" tests="8" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="21"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/target/test-classes:/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.4.7/spring-boot-starter-web-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.4.7/spring-boot-starter-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.4.7/spring-boot-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.4.7/spring-boot-starter-logging-3.4.7.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.5.18/logback-classic-1.5.18.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.4.7/spring-boot-starter-json-3.4.7.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.18.4/jackson-datatype-jdk8-2.18.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.18.4/jackson-module-parameter-names-2.18.4.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.2.8/spring-web-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.2.8/spring-beans-6.2.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.14.8/micrometer-observation-1.14.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.14.8/micrometer-commons-1.14.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.2.8/spring-webmvc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.2.8/spring-context-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.2.8/spring-expression-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-undertow/3.4.7/spring-boot-starter-undertow-3.4.7.jar:/Users/<USER>/.m2/repository/io/undertow/undertow-core/2.3.18.Final/undertow-core-2.3.18.Final.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.6.1.Final/jboss-logging-3.6.1.Final.jar:/Users/<USER>/.m2/repository/org/jboss/xnio/xnio-api/3.8.16.Final/xnio-api-3.8.16.Final.jar:/Users/<USER>/.m2/repository/org/wildfly/common/wildfly-common/1.5.4.Final/wildfly-common-1.5.4.Final.jar:/Users/<USER>/.m2/repository/org/wildfly/client/wildfly-client-config/1.0.1.Final/wildfly-client-config-1.0.1.Final.jar:/Users/<USER>/.m2/repository/org/jboss/xnio/xnio-nio/3.8.16.Final/xnio-nio-3.8.16.Final.jar:/Users/<USER>/.m2/repository/org/jboss/threads/jboss-threads/3.5.0.Final/jboss-threads-3.5.0.Final.jar:/Users/<USER>/.m2/repository/io/undertow/undertow-servlet/2.3.18.Final/undertow-servlet-2.3.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/servlet/jakarta.servlet-api/6.0.0/jakarta.servlet-api-6.0.0.jar:/Users/<USER>/.m2/repository/io/undertow/undertow-websockets-jsr/2.3.18.Final/undertow-websockets-jsr-2.3.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/websocket/jakarta.websocket-api/2.1.1/jakarta.websocket-api-2.1.1.jar:/Users/<USER>/.m2/repository/jakarta/websocket/jakarta.websocket-client-api/2.1.1/jakarta.websocket-client-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.42/tomcat-embed-el-10.1.42.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.4.7/spring-boot-starter-validation-3.4.7.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.2.Final/hibernate-validator-8.0.2.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.7.0/classmate-1.7.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.4.7/spring-boot-starter-security-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.2.8/spring-aop-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.4.7/spring-security-config-6.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.4.7/spring-security-web-6.4.7.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-discovery/2023.0.3.3/spring-cloud-starter-alibaba-nacos-discovery-2023.0.3.3.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-alibaba-commons/2023.0.3.3/spring-cloud-alibaba-commons-2023.0.3.3.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.4.2/nacos-client-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-auth-plugin/2.4.2/nacos-auth-plugin-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-encryption-plugin/2.4.2/nacos-encryption-plugin-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-logback-adapter-12/2.4.2/nacos-logback-adapter-12-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/logback-adapter/1.1.3/logback-adapter-1.1.3.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-log4j2-adapter/2.4.2/nacos-log4j2-adapter-2.4.2.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.17.2/commons-codec-1.17.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.18.4.1/jackson-core-2.18.4.1.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpasyncclient/4.1.5/httpasyncclient-4.1.5.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore-nio/4.4.16/httpcore-nio-4.4.16.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient/0.16.0/simpleclient-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_otel/0.16.0/simpleclient_tracer_otel-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_common/0.16.0/simpleclient_tracer_common-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_otel_agent/0.16.0/simpleclient_tracer_otel_agent-0.16.0.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.14.8/micrometer-core-1.14.8.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.2.2/HdrHistogram-2.2.2.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-commons/4.2.1/spring-cloud-commons-4.2.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.4.7/spring-security-crypto-6.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-context/4.2.1/spring-cloud-context-4.2.1.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-config/2023.0.3.3/spring-cloud-starter-alibaba-nacos-config-2023.0.3.3.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-alibaba-nacos-config/2023.0.3.3/spring-alibaba-nacos-config-2023.0.3.3.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jdbc/3.4.7/spring-boot-starter-data-jdbc-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.4.7/spring-boot-starter-jdbc-3.4.7.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/5.1.0/HikariCP-5.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.2.8/spring-jdbc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jdbc/3.4.7/spring-data-jdbc-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-relational/3.4.7/spring-data-relational-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.4.7/spring-data-commons-3.4.7.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter/3.5.12/mybatis-plus-spring-boot3-starter-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.12/mybatis-plus-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.12/mybatis-plus-core-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.12/mybatis-plus-annotation-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring/3.5.12/mybatis-plus-spring-3.5.12.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.19/mybatis-3.5.19.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/3.0.4/mybatis-spring-3.0.4.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.12/mybatis-plus-spring-boot-autoconfigure-3.5.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.4.7/spring-boot-autoconfigure-3.4.7.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-jsqlparser/3.5.12/mybatis-plus-jsqlparser-3.5.12.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/5.1/jsqlparser-5.1.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-jsqlparser-common/3.5.12/mybatis-plus-jsqlparser-common-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.12/mybatis-plus-extension-3.5.12.jar:/Users/<USER>/.m2/repository/org/postgresql/postgresql/42.7.7/postgresql-42.7.7.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.49.3/checker-qual-3.49.3.jar:/Users/<USER>/.m2/repository/org/liquibase/liquibase-core/4.29.2/liquibase-core-4.29.2.jar:/Users/<USER>/.m2/repository/com/opencsv/opencsv/5.9/opencsv-5.9.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.3/snakeyaml-2.3.jar:/Users/<USER>/.m2/repository/javax/xml/bind/jaxb-api/2.3.1/jaxb-api-2.3.1.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.17.0/commons-io-2.17.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.5.0/commons-collections4-4.5.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.13.1/commons-text-1.13.1.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar:/Users/<USER>/work/bdyl/projects/pisp/pisp-common/pisp-common-core/target/pisp-common-core-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.38/hutool-all-5.8.38.jar:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.6.3/mapstruct-1.6.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.18.4/jackson-databind-2.18.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.18.4/jackson-annotations-2.18.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.18.4/jackson-datatype-jsr310-2.18.4.jar:/Users/<USER>/work/bdyl/projects/pisp/pisp-common/pisp-common-security/target/pisp-common-security-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.12.6/jjwt-api-0.12.6.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.12.6/jjwt-impl-0.12.6.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.12.6/jjwt-jackson-0.12.6.jar:/Users/<USER>/work/bdyl/projects/pisp/pisp-common/pisp-common-web/target/pisp-common-web-1.0.0-SNAPSHOT.jar:/Users/<USER>/work/bdyl/projects/pisp/pisp-common/pisp-common-redis/target/pisp-common-redis-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/3.4.7/spring-boot-starter-data-redis-3.4.7.jar:/Users/<USER>/.m2/repository/io/lettuce/lettuce-core/6.4.2.RELEASE/lettuce-core-6.4.2.RELEASE.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.122.Final/netty-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.122.Final/netty-handler-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.122.Final/netty-resolver-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.122.Final/netty-buffer-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.122.Final/netty-transport-native-unix-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.122.Final/netty-codec-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.122.Final/netty-transport-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.7.7/reactor-core-3.7.7.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/3.4.7/spring-data-redis-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/3.4.7/spring-data-keyvalue-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.2.8/spring-oxm-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.2.8/spring-context-support-6.2.8.jar:/Users/<USER>/work/bdyl/projects/pisp/pisp-api/target/pisp-api-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.4.7/spring-boot-starter-test-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.4.7/spring-boot-test-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.4.7/spring-boot-test-autoconfigure-3.4.7.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/9.7.1/asm-9.7.1.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.26.3/assertj-core-3.26.3.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.15.11/byte-buddy-1.15.11.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.11.4/junit-jupiter-5.11.4.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.11.4/junit-jupiter-api-5.11.4.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.11.4/junit-platform-commons-1.11.4.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.11.4/junit-jupiter-params-5.11.4.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.11.4/junit-jupiter-engine-5.11.4.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.11.4/junit-platform-engine-1.11.4.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.14.2/mockito-core-5.14.2.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.15.11/byte-buddy-agent-1.15.11.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.14.2/mockito-junit-jupiter-5.14.2.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.2.8/spring-core-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.2.8/spring-jcl-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.2.8/spring-test-6.2.8.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.10.2/xmlunit-core-2.10.2.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-test/6.4.7/spring-security-test-6.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.4.7/spring-security-core-6.4.7.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter-test/3.5.12/mybatis-plus-spring-boot3-starter-test-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-test-autoconfigure/3.5.12/mybatis-plus-spring-boot-test-autoconfigure-3.5.12.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.2.8/spring-tx-6.2.8.jar:/Users/<USER>/.m2/repository/org/testcontainers/postgresql/1.20.6/postgresql-1.20.6.jar:/Users/<USER>/.m2/repository/org/testcontainers/jdbc/1.20.6/jdbc-1.20.6.jar:/Users/<USER>/.m2/repository/org/testcontainers/database-commons/1.20.6/database-commons-1.20.6.jar:/Users/<USER>/.m2/repository/org/testcontainers/testcontainers/1.20.6/testcontainers-1.20.6.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/repository/org/rnorth/duct-tape/duct-tape/1.0.8/duct-tape-1.0.8.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/17.0.0/annotations-17.0.0.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-api/3.4.1/docker-java-api-3.4.1.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-transport-zerodep/3.4.1/docker-java-transport-zerodep-3.4.1.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-transport/3.4.1/docker-java-transport-3.4.1.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.13.0/jna-5.13.0.jar:/Users/<USER>/.m2/repository/com/h2database/h2/2.3.232/h2-2.3.232.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.38/lombok-1.18.38.jar:"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="org.jboss.logging.provider" value="slf4j"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="21"/>
    <property name="APPLICATION_NAME" value="pisp-user-service-test"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="/Users/<USER>/.sdkman/candidates/java/21.0.7-oracle/lib"/>
    <property name="sun.java.command" value="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/target/surefire/surefirebooter-20250630213937880_13.jar /Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/target/surefire 2025-06-30T21-39-33_933-jvmRun1 surefire-20250630213937880_11tmp surefire_3-20250630213937880_12tmp"/>
    <property name="http.nonProxyHosts" value="127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|&lt;local&gt;|*.&lt;local&gt;"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/target/test-classes:/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.4.7/spring-boot-starter-web-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.4.7/spring-boot-starter-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.4.7/spring-boot-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.4.7/spring-boot-starter-logging-3.4.7.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.5.18/logback-classic-1.5.18.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.4.7/spring-boot-starter-json-3.4.7.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.18.4/jackson-datatype-jdk8-2.18.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.18.4/jackson-module-parameter-names-2.18.4.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.2.8/spring-web-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.2.8/spring-beans-6.2.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.14.8/micrometer-observation-1.14.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.14.8/micrometer-commons-1.14.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.2.8/spring-webmvc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.2.8/spring-context-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.2.8/spring-expression-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-undertow/3.4.7/spring-boot-starter-undertow-3.4.7.jar:/Users/<USER>/.m2/repository/io/undertow/undertow-core/2.3.18.Final/undertow-core-2.3.18.Final.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.6.1.Final/jboss-logging-3.6.1.Final.jar:/Users/<USER>/.m2/repository/org/jboss/xnio/xnio-api/3.8.16.Final/xnio-api-3.8.16.Final.jar:/Users/<USER>/.m2/repository/org/wildfly/common/wildfly-common/1.5.4.Final/wildfly-common-1.5.4.Final.jar:/Users/<USER>/.m2/repository/org/wildfly/client/wildfly-client-config/1.0.1.Final/wildfly-client-config-1.0.1.Final.jar:/Users/<USER>/.m2/repository/org/jboss/xnio/xnio-nio/3.8.16.Final/xnio-nio-3.8.16.Final.jar:/Users/<USER>/.m2/repository/org/jboss/threads/jboss-threads/3.5.0.Final/jboss-threads-3.5.0.Final.jar:/Users/<USER>/.m2/repository/io/undertow/undertow-servlet/2.3.18.Final/undertow-servlet-2.3.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/servlet/jakarta.servlet-api/6.0.0/jakarta.servlet-api-6.0.0.jar:/Users/<USER>/.m2/repository/io/undertow/undertow-websockets-jsr/2.3.18.Final/undertow-websockets-jsr-2.3.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/websocket/jakarta.websocket-api/2.1.1/jakarta.websocket-api-2.1.1.jar:/Users/<USER>/.m2/repository/jakarta/websocket/jakarta.websocket-client-api/2.1.1/jakarta.websocket-client-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.42/tomcat-embed-el-10.1.42.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.4.7/spring-boot-starter-validation-3.4.7.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.2.Final/hibernate-validator-8.0.2.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.7.0/classmate-1.7.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.4.7/spring-boot-starter-security-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.2.8/spring-aop-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.4.7/spring-security-config-6.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.4.7/spring-security-web-6.4.7.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-discovery/2023.0.3.3/spring-cloud-starter-alibaba-nacos-discovery-2023.0.3.3.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-alibaba-commons/2023.0.3.3/spring-cloud-alibaba-commons-2023.0.3.3.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.4.2/nacos-client-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-auth-plugin/2.4.2/nacos-auth-plugin-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-encryption-plugin/2.4.2/nacos-encryption-plugin-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-logback-adapter-12/2.4.2/nacos-logback-adapter-12-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/logback-adapter/1.1.3/logback-adapter-1.1.3.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-log4j2-adapter/2.4.2/nacos-log4j2-adapter-2.4.2.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.17.2/commons-codec-1.17.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.18.4.1/jackson-core-2.18.4.1.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpasyncclient/4.1.5/httpasyncclient-4.1.5.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore-nio/4.4.16/httpcore-nio-4.4.16.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient/0.16.0/simpleclient-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_otel/0.16.0/simpleclient_tracer_otel-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_common/0.16.0/simpleclient_tracer_common-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_otel_agent/0.16.0/simpleclient_tracer_otel_agent-0.16.0.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.14.8/micrometer-core-1.14.8.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.2.2/HdrHistogram-2.2.2.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-commons/4.2.1/spring-cloud-commons-4.2.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.4.7/spring-security-crypto-6.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-context/4.2.1/spring-cloud-context-4.2.1.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-config/2023.0.3.3/spring-cloud-starter-alibaba-nacos-config-2023.0.3.3.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-alibaba-nacos-config/2023.0.3.3/spring-alibaba-nacos-config-2023.0.3.3.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jdbc/3.4.7/spring-boot-starter-data-jdbc-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.4.7/spring-boot-starter-jdbc-3.4.7.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/5.1.0/HikariCP-5.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.2.8/spring-jdbc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jdbc/3.4.7/spring-data-jdbc-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-relational/3.4.7/spring-data-relational-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.4.7/spring-data-commons-3.4.7.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter/3.5.12/mybatis-plus-spring-boot3-starter-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.12/mybatis-plus-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.12/mybatis-plus-core-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.12/mybatis-plus-annotation-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring/3.5.12/mybatis-plus-spring-3.5.12.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.19/mybatis-3.5.19.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/3.0.4/mybatis-spring-3.0.4.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.12/mybatis-plus-spring-boot-autoconfigure-3.5.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.4.7/spring-boot-autoconfigure-3.4.7.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-jsqlparser/3.5.12/mybatis-plus-jsqlparser-3.5.12.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/5.1/jsqlparser-5.1.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-jsqlparser-common/3.5.12/mybatis-plus-jsqlparser-common-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.12/mybatis-plus-extension-3.5.12.jar:/Users/<USER>/.m2/repository/org/postgresql/postgresql/42.7.7/postgresql-42.7.7.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.49.3/checker-qual-3.49.3.jar:/Users/<USER>/.m2/repository/org/liquibase/liquibase-core/4.29.2/liquibase-core-4.29.2.jar:/Users/<USER>/.m2/repository/com/opencsv/opencsv/5.9/opencsv-5.9.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.3/snakeyaml-2.3.jar:/Users/<USER>/.m2/repository/javax/xml/bind/jaxb-api/2.3.1/jaxb-api-2.3.1.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.17.0/commons-io-2.17.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.5.0/commons-collections4-4.5.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.13.1/commons-text-1.13.1.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar:/Users/<USER>/work/bdyl/projects/pisp/pisp-common/pisp-common-core/target/pisp-common-core-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.38/hutool-all-5.8.38.jar:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.6.3/mapstruct-1.6.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.18.4/jackson-databind-2.18.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.18.4/jackson-annotations-2.18.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.18.4/jackson-datatype-jsr310-2.18.4.jar:/Users/<USER>/work/bdyl/projects/pisp/pisp-common/pisp-common-security/target/pisp-common-security-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.12.6/jjwt-api-0.12.6.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.12.6/jjwt-impl-0.12.6.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.12.6/jjwt-jackson-0.12.6.jar:/Users/<USER>/work/bdyl/projects/pisp/pisp-common/pisp-common-web/target/pisp-common-web-1.0.0-SNAPSHOT.jar:/Users/<USER>/work/bdyl/projects/pisp/pisp-common/pisp-common-redis/target/pisp-common-redis-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/3.4.7/spring-boot-starter-data-redis-3.4.7.jar:/Users/<USER>/.m2/repository/io/lettuce/lettuce-core/6.4.2.RELEASE/lettuce-core-6.4.2.RELEASE.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.122.Final/netty-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.122.Final/netty-handler-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.122.Final/netty-resolver-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.122.Final/netty-buffer-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.122.Final/netty-transport-native-unix-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.122.Final/netty-codec-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.122.Final/netty-transport-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.7.7/reactor-core-3.7.7.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/3.4.7/spring-data-redis-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/3.4.7/spring-data-keyvalue-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.2.8/spring-oxm-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.2.8/spring-context-support-6.2.8.jar:/Users/<USER>/work/bdyl/projects/pisp/pisp-api/target/pisp-api-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.4.7/spring-boot-starter-test-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.4.7/spring-boot-test-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.4.7/spring-boot-test-autoconfigure-3.4.7.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/9.7.1/asm-9.7.1.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.26.3/assertj-core-3.26.3.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.15.11/byte-buddy-1.15.11.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.11.4/junit-jupiter-5.11.4.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.11.4/junit-jupiter-api-5.11.4.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.11.4/junit-platform-commons-1.11.4.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.11.4/junit-jupiter-params-5.11.4.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.11.4/junit-jupiter-engine-5.11.4.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.11.4/junit-platform-engine-1.11.4.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.14.2/mockito-core-5.14.2.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.15.11/byte-buddy-agent-1.15.11.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.14.2/mockito-junit-jupiter-5.14.2.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.2.8/spring-core-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.2.8/spring-jcl-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.2.8/spring-test-6.2.8.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.10.2/xmlunit-core-2.10.2.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-test/6.4.7/spring-security-test-6.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.4.7/spring-security-core-6.4.7.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter-test/3.5.12/mybatis-plus-spring-boot3-starter-test-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-test-autoconfigure/3.5.12/mybatis-plus-spring-boot-test-autoconfigure-3.5.12.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.2.8/spring-tx-6.2.8.jar:/Users/<USER>/.m2/repository/org/testcontainers/postgresql/1.20.6/postgresql-1.20.6.jar:/Users/<USER>/.m2/repository/org/testcontainers/jdbc/1.20.6/jdbc-1.20.6.jar:/Users/<USER>/.m2/repository/org/testcontainers/database-commons/1.20.6/database-commons-1.20.6.jar:/Users/<USER>/.m2/repository/org/testcontainers/testcontainers/1.20.6/testcontainers-1.20.6.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/repository/org/rnorth/duct-tape/duct-tape/1.0.8/duct-tape-1.0.8.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/17.0.0/annotations-17.0.0.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-api/3.4.1/docker-java-api-3.4.1.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-transport-zerodep/3.4.1/docker-java-transport-zerodep-3.4.1.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-transport/3.4.1/docker-java-transport-3.4.1.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.13.0/jna-5.13.0.jar:/Users/<USER>/.m2/repository/com/h2database/h2/2.3.232/h2-2.3.232.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.38/lombok-1.18.38.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="zh"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-04-15"/>
    <property name="java.home" value="/Users/<USER>/.sdkman/candidates/java/21.0.7-oracle"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user"/>
    <property name="java.vm.compressedOopsMode" value="Non-zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="FILE_LOG_CHARSET" value="UTF-8"/>
    <property name="java.awt.headless" value="true"/>
    <property name="apple.awt.application.name" value="ForkedBooter"/>
    <property name="surefire.real.class.path" value="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/target/surefire/surefirebooter-20250630213937880_13.jar"/>
    <property name="user.script" value="Hans"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|&lt;local&gt;|*.&lt;local&gt;"/>
    <property name="java.runtime.version" value="21.0.7+8-LTS-245"/>
    <property name="user.name" value="jeffery"/>
    <property name="stdout.encoding" value="UTF-8"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.5"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="/var/folders/2c/k8bwfr1j5cz_4rmd02kztnph0000gn/T/"/>
    <property name="com.zaxxer.hikari.pool_number" value="1"/>
    <property name="java.version" value="21.0.7"/>
    <property name="user.dir" value="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user"/>
    <property name="os.arch" value="aarch64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="PID" value="17236"/>
    <property name="CONSOLE_LOG_CHARSET" value="UTF-8"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="UTF-8"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="21.0.7+8-LTS-245"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="socksNonProxyHosts" value="127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|&lt;local&gt;|*.&lt;local&gt;"/>
    <property name="java.class.version" value="65.0"/>
    <property name="LOGGED_APPLICATION_NAME" value="[pisp-user-service-test] "/>
  </properties>
  <testcase name="testUserStatusOperations" classname="com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest" time="0.43">
    <system-out><![CDATA[21:39:38.145 [main] INFO org.springframework.test.context.support.AnnotationConfigContextLoaderUtils -- Could not detect default configuration classes for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]: UserServiceIntegrationTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
21:39:38.183 [main] INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper -- Found @SpringBootConfiguration com.bdyl.erp.pisp.user.UserServiceApplication for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.7)

2025-06-30T21:39:38.400+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.i.UserServiceIntegrationTest   : Starting UserServiceIntegrationTest using Java 21.0.7 with PID 17236 (started by jeffery in /Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user)
2025-06-30T21:39:38.400+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.i.UserServiceIntegrationTest   : Running with Spring Boot v3.4.7, Spring v6.2.8
2025-06-30T21:39:38.400+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.i.UserServiceIntegrationTest   : The following 1 profile is active: "test"
Logging initialized using 'class org.apache.ibatis.logging.stdout.StdOutImpl' adapter.
Get /192.168.2.219 network interface 
Get network interface info: name:en0 (en0)
Initialization Sequence datacenterId:20 workerId:17
2025-06-30T21:39:39.818+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.erp.pisp.user.config.SecurityConfig  : 配置Spring Security过滤器链
2025-06-30T21:39:39.828+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.erp.pisp.user.config.SecurityConfig  : Spring Security配置完成
2025-06-30T21:39:39.999+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.i.UserServiceIntegrationTest   : Started UserServiceIntegrationTest in 1.769 seconds (process running for 2.103)
2025-06-30T21:39:40.001+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 0, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.001+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:39:40.004+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:39:40.004+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 1, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.004+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 2, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.006+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [UserServiceIntegrationTest], method [testUserStatusOperations], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:39:40.007+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest] and test method [testUserStatusOperations]
2025-06-30T21:39:40.007+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 3, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.007+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest.testUserStatusOperations()]
2025-06-30T21:39:40.008+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testUserStatusOperations]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@534c3b9d]; rollback [true]
2025-06-30T21:39:40.009+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 4, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.010+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 5, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.323+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 6, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.326+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 7, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.327+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 创建用户: statustest
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1455d0f5]
2025-06-30T21:39:40.400+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:39:40.400+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
JDBC Connection [HikariProxyConnection@1565987193 wrapping conn0: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: INSERT INTO sys_users ( id, username, password_hash, email, phone, real_name, status, department_id, login_count, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939680235849584642(Long), statustest(String), $2a$10$67msNCdjg/Cqq9o/pNMAx.WDLGEqXYZlmntW2kmgV9DV1LBwBm.2u(String), <EMAIL>(String), 13800139002(String), 状态测试用户(String), ACTIVE(String), 1(Long), 0(Integer), 2025-06-30T21:39:40.400211(LocalDateTime), 2025-06-30T21:39:40.400704(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1455d0f5]
2025-06-30T21:39:40.412+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户创建成功: userId=1939680235849584642
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1455d0f5] from current transaction
==>  Preparing: SELECT id,username,password_hash AS password,email,phone,real_name,avatar_url,status,department_id,last_login_time,last_login_ip,login_count,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_users WHERE id=? AND deleted=0
==> Parameters: 1939680235849584642(Long)
<==    Columns: ID, USERNAME, PASSWORD, EMAIL, PHONE, REAL_NAME, AVATAR_URL, STATUS, DEPARTMENT_ID, LAST_LOGIN_TIME, LAST_LOGIN_IP, LOGIN_COUNT, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939680235849584642, statustest, $2a$10$67msNCdjg/Cqq9o/pNMAx.WDLGEqXYZlmntW2kmgV9DV1LBwBm.2u, <EMAIL>, 13800139002, 状态测试用户, null, ACTIVE, 1, null, null, 0, 0, 0, 2025-06-30 21:39:40.400211, 2025-06-30 21:39:40.400704, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1455d0f5]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1455d0f5] from current transaction
2025-06-30T21:39:40.425+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始更新填充...
2025-06-30T21:39:40.425+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 更新填充完成
==>  Preparing: UPDATE sys_users SET username=?, password_hash=?, email=?, phone=?, real_name=?, status=?, department_id=?, login_count=?, version=?, create_time=?, update_time=?, creator_id=?, updater_id=? WHERE id=? AND version=? AND deleted=0
==> Parameters: statustest(String), $2a$10$67msNCdjg/Cqq9o/pNMAx.WDLGEqXYZlmntW2kmgV9DV1LBwBm.2u(String), <EMAIL>(String), 13800139002(String), 状态测试用户(String), INACTIVE(String), 1(Long), 0(Integer), 1(Integer), 2025-06-30T21:39:40.400211(LocalDateTime), 2025-06-30T21:39:40.400704(LocalDateTime), 1(Long), 1(Long), 1939680235849584642(Long), 0(Integer)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1455d0f5]
2025-06-30T21:39:40.426+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户状态更新成功: userId=1939680235849584642, status=INACTIVE
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1455d0f5] from current transaction
==>  Preparing: SELECT id,username,password_hash AS password,email,phone,real_name,avatar_url,status,department_id,last_login_time,last_login_ip,login_count,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_users WHERE id=? AND deleted=0
==> Parameters: 1939680235849584642(Long)
<==    Columns: ID, USERNAME, PASSWORD, EMAIL, PHONE, REAL_NAME, AVATAR_URL, STATUS, DEPARTMENT_ID, LAST_LOGIN_TIME, LAST_LOGIN_IP, LOGIN_COUNT, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939680235849584642, statustest, $2a$10$67msNCdjg/Cqq9o/pNMAx.WDLGEqXYZlmntW2kmgV9DV1LBwBm.2u, <EMAIL>, 13800139002, 状态测试用户, null, INACTIVE, 1, null, null, 0, 1, 0, 2025-06-30 21:39:40.400211, 2025-06-30 21:39:40.400704, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1455d0f5]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1455d0f5] from current transaction
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1455d0f5]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1455d0f5] from current transaction
2025-06-30T21:39:40.427+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始更新填充...
2025-06-30T21:39:40.427+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 更新填充完成
==>  Preparing: UPDATE sys_users SET username=?, password_hash=?, email=?, phone=?, real_name=?, status=?, department_id=?, login_count=?, version=?, create_time=?, update_time=?, creator_id=?, updater_id=? WHERE id=? AND version=? AND deleted=0
==> Parameters: statustest(String), $2a$10$67msNCdjg/Cqq9o/pNMAx.WDLGEqXYZlmntW2kmgV9DV1LBwBm.2u(String), <EMAIL>(String), 13800139002(String), 状态测试用户(String), LOCKED(String), 1(Long), 0(Integer), 2(Integer), 2025-06-30T21:39:40.400211(LocalDateTime), 2025-06-30T21:39:40.400704(LocalDateTime), 1(Long), 1(Long), 1939680235849584642(Long), 1(Integer)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1455d0f5]
2025-06-30T21:39:40.428+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户状态更新成功: userId=1939680235849584642, status=LOCKED
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1455d0f5] from current transaction
==>  Preparing: SELECT id,username,password_hash AS password,email,phone,real_name,avatar_url,status,department_id,last_login_time,last_login_ip,login_count,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_users WHERE id=? AND deleted=0
==> Parameters: 1939680235849584642(Long)
<==    Columns: ID, USERNAME, PASSWORD, EMAIL, PHONE, REAL_NAME, AVATAR_URL, STATUS, DEPARTMENT_ID, LAST_LOGIN_TIME, LAST_LOGIN_IP, LOGIN_COUNT, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939680235849584642, statustest, $2a$10$67msNCdjg/Cqq9o/pNMAx.WDLGEqXYZlmntW2kmgV9DV1LBwBm.2u, <EMAIL>, 13800139002, 状态测试用户, null, LOCKED, 1, null, null, 0, 2, 0, 2025-06-30 21:39:40.400211, 2025-06-30 21:39:40.400704, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1455d0f5]
2025-06-30T21:39:40.428+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 激活用户: userId=1939680235849584642
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1455d0f5] from current transaction
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1455d0f5]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1455d0f5] from current transaction
2025-06-30T21:39:40.429+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始更新填充...
2025-06-30T21:39:40.429+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 更新填充完成
==>  Preparing: UPDATE sys_users SET username=?, password_hash=?, email=?, phone=?, real_name=?, status=?, department_id=?, login_count=?, version=?, create_time=?, update_time=?, creator_id=?, updater_id=? WHERE id=? AND version=? AND deleted=0
==> Parameters: statustest(String), $2a$10$67msNCdjg/Cqq9o/pNMAx.WDLGEqXYZlmntW2kmgV9DV1LBwBm.2u(String), <EMAIL>(String), 13800139002(String), 状态测试用户(String), ACTIVE(String), 1(Long), 0(Integer), 3(Integer), 2025-06-30T21:39:40.400211(LocalDateTime), 2025-06-30T21:39:40.400704(LocalDateTime), 1(Long), 1(Long), 1939680235849584642(Long), 2(Integer)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1455d0f5]
2025-06-30T21:39:40.429+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户激活成功: userId=1939680235849584642
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1455d0f5] from current transaction
==>  Preparing: SELECT id,username,password_hash AS password,email,phone,real_name,avatar_url,status,department_id,last_login_time,last_login_ip,login_count,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_users WHERE id=? AND deleted=0
==> Parameters: 1939680235849584642(Long)
<==    Columns: ID, USERNAME, PASSWORD, EMAIL, PHONE, REAL_NAME, AVATAR_URL, STATUS, DEPARTMENT_ID, LAST_LOGIN_TIME, LAST_LOGIN_IP, LOGIN_COUNT, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939680235849584642, statustest, $2a$10$67msNCdjg/Cqq9o/pNMAx.WDLGEqXYZlmntW2kmgV9DV1LBwBm.2u, <EMAIL>, 13800139002, 状态测试用户, null, ACTIVE, 1, null, null, 0, 3, 0, 2025-06-30 21:39:40.400211, 2025-06-30 21:39:40.400704, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1455d0f5]
2025-06-30T21:39:40.430+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 8, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.431+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 9, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.431+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 10, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.431+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 11, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.431+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 12, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.431+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 13, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.432+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 14, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.432+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 15, missCount = 1, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1455d0f5]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1455d0f5]
2025-06-30T21:39:40.433+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testUserStatusOperations]
2025-06-30T21:39:40.433+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [UserServiceIntegrationTest], method [testUserStatusOperations], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:39:40.433+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
]]></system-out>
    <system-err><![CDATA[Mockito is currently self-attaching to enable the inline-mock-maker. This will no longer work in future releases of the JDK. Please add Mockito as an agent to your build what is described in Mockito's documentation: https://javadoc.io/doc/org.mockito/mockito-core/latest/org/mockito/Mockito.html#0.3
WARNING: A Java agent has been loaded dynamically (/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.15.11/byte-buddy-agent-1.15.11.jar)
WARNING: If a serviceability tool is in use, please run with -XX:+EnableDynamicAgentLoading to hide this warning
WARNING: If a serviceability tool is not in use, please run with -Djdk.instrument.traceUsage for more information
WARNING: Dynamic loading of agents will be disallowed by default in a future release
]]></system-err>
  </testcase>
  <testcase name="testUpdateUser" classname="com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest" time="0.065">
    <system-out><![CDATA[2025-06-30T21:39:40.435+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 16, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.435+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:39:40.435+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:39:40.435+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 17, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.436+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 18, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.436+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [UserServiceIntegrationTest], method [testUpdateUser], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:39:40.436+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest] and test method [testUpdateUser]
2025-06-30T21:39:40.436+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 19, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.436+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest.testUpdateUser()]
2025-06-30T21:39:40.436+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testUpdateUser]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@534c3b9d]; rollback [true]
2025-06-30T21:39:40.436+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 20, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.436+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 21, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.437+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 22, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.437+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 23, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.437+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 创建用户: updatetest
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@77a27356]
2025-06-30T21:39:40.496+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:39:40.497+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
JDBC Connection [HikariProxyConnection@1726408169 wrapping conn0: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: INSERT INTO sys_users ( id, username, password_hash, email, phone, real_name, status, department_id, login_count, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939680236256432130(Long), updatetest(String), $2a$10$Wv3FGIfStrkFZRFP0YZIdunfxam9LanLoaIvIZqrTgDjdfyTqa9FK(String), <EMAIL>(String), 13800138999(String), 更新测试用户(String), ACTIVE(String), 1(Long), 0(Integer), 2025-06-30T21:39:40.497054(LocalDateTime), 2025-06-30T21:39:40.497076(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@77a27356]
2025-06-30T21:39:40.497+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户创建成功: userId=1939680236256432130
2025-06-30T21:39:40.497+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 更新用户: userId=1939680236256432130
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@77a27356] from current transaction
==>  Preparing: SELECT id,username,password_hash AS password,email,phone,real_name,avatar_url,status,department_id,last_login_time,last_login_ip,login_count,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_users WHERE id=? AND deleted=0
==> Parameters: 1939680236256432130(Long)
<==    Columns: ID, USERNAME, PASSWORD, EMAIL, PHONE, REAL_NAME, AVATAR_URL, STATUS, DEPARTMENT_ID, LAST_LOGIN_TIME, LAST_LOGIN_IP, LOGIN_COUNT, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939680236256432130, updatetest, $2a$10$Wv3FGIfStrkFZRFP0YZIdunfxam9LanLoaIvIZqrTgDjdfyTqa9FK, <EMAIL>, 13800138999, 更新测试用户, null, ACTIVE, 1, null, null, 0, 0, 0, 2025-06-30 21:39:40.497054, 2025-06-30 21:39:40.497076, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@77a27356]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@77a27356] from current transaction
2025-06-30T21:39:40.499+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始更新填充...
2025-06-30T21:39:40.499+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 更新填充完成
==>  Preparing: UPDATE sys_users SET username=?, password_hash=?, email=?, phone=?, real_name=?, department_id=?, login_count=?, create_time=?, update_time=?, creator_id=?, updater_id=? WHERE id=? AND deleted=0
==> Parameters: updatetest(String), $2a$10$Wv3FGIfStrkFZRFP0YZIdunfxam9LanLoaIvIZqrTgDjdfyTqa9FK(String), <EMAIL>(String), 13800139000(String), 已更新用户(String), 2(Long), 0(Integer), 2025-06-30T21:39:40.497054(LocalDateTime), 2025-06-30T21:39:40.497076(LocalDateTime), 1(Long), 1(Long), 1939680236256432130(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@77a27356]
2025-06-30T21:39:40.500+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户更新成功: userId=1939680236256432130
2025-06-30T21:39:40.500+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 24, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.500+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 25, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.500+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 26, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.500+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 27, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.501+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 28, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.501+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 29, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.501+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 30, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.501+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 31, missCount = 1, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@77a27356]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@77a27356]
2025-06-30T21:39:40.501+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testUpdateUser]
2025-06-30T21:39:40.501+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [UserServiceIntegrationTest], method [testUpdateUser], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:39:40.501+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
]]></system-out>
  </testcase>
  <testcase name="testUniqueConstraints" classname="com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest" time="0.073">
    <system-out><![CDATA[2025-06-30T21:39:40.502+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 32, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.502+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:39:40.502+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:39:40.502+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 33, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.502+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 34, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.502+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [UserServiceIntegrationTest], method [testUniqueConstraints], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:39:40.502+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest] and test method [testUniqueConstraints]
2025-06-30T21:39:40.502+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 35, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.502+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest.testUniqueConstraints()]
2025-06-30T21:39:40.502+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testUniqueConstraints]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@534c3b9d]; rollback [true]
2025-06-30T21:39:40.502+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 36, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.503+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 37, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.503+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 38, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.503+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 39, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.503+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 创建用户: uniquetest
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@654ab198]
2025-06-30T21:39:40.563+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:39:40.563+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
JDBC Connection [HikariProxyConnection@1470844979 wrapping conn0: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: INSERT INTO sys_users ( id, username, password_hash, email, phone, real_name, status, department_id, login_count, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939680236533256194(Long), uniquetest(String), $2a$10$kDBravQMC.cFaTPcnbRigehNedwMcHl7A8S2oJBwcPGWWKaX/fsAS(String), <EMAIL>(String), 13800139010(String), 唯一性测试用户(String), ACTIVE(String), 1(Long), 0(Integer), 2025-06-30T21:39:40.563137(LocalDateTime), 2025-06-30T21:39:40.563164(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@654ab198]
2025-06-30T21:39:40.563+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户创建成功: userId=1939680236533256194
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@654ab198] from current transaction
==>  Preparing: SELECT COUNT( * ) AS total FROM sys_users WHERE deleted=0 AND (username = ?)
==> Parameters: uniquetest(String)
<==    Columns: TOTAL
<==        Row: 1
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@654ab198]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@654ab198] from current transaction
==>  Preparing: SELECT COUNT( * ) AS total FROM sys_users WHERE deleted=0 AND (username = ?)
==> Parameters: nonexistent(String)
<==    Columns: TOTAL
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@654ab198]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@654ab198] from current transaction
==>  Preparing: SELECT COUNT( * ) AS total FROM sys_users WHERE deleted=0 AND (email = ?)
==> Parameters: <EMAIL>(String)
<==    Columns: TOTAL
<==        Row: 1
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@654ab198]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@654ab198] from current transaction
==>  Preparing: SELECT COUNT( * ) AS total FROM sys_users WHERE deleted=0 AND (email = ?)
==> Parameters: <EMAIL>(String)
<==    Columns: TOTAL
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@654ab198]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@654ab198] from current transaction
==>  Preparing: SELECT COUNT( * ) AS total FROM sys_users WHERE deleted=0 AND (phone = ?)
==> Parameters: 13800139010(String)
<==    Columns: TOTAL
<==        Row: 1
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@654ab198]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@654ab198] from current transaction
==>  Preparing: SELECT COUNT( * ) AS total FROM sys_users WHERE deleted=0 AND (phone = ?)
==> Parameters: 13800139999(String)
<==    Columns: TOTAL
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@654ab198]
2025-06-30T21:39:40.573+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 40, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.573+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 41, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.573+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 42, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.574+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 43, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.574+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 44, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.574+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 45, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.574+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 46, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.574+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 47, missCount = 1, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@654ab198]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@654ab198]
2025-06-30T21:39:40.574+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testUniqueConstraints]
2025-06-30T21:39:40.574+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [UserServiceIntegrationTest], method [testUniqueConstraints], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:39:40.574+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
]]></system-out>
  </testcase>
  <testcase name="testChangePassword" classname="com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest" time="0.3">
    <system-out><![CDATA[2025-06-30T21:39:40.575+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 48, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.575+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:39:40.575+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:39:40.575+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 49, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.575+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 50, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.575+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [UserServiceIntegrationTest], method [testChangePassword], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:39:40.575+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest] and test method [testChangePassword]
2025-06-30T21:39:40.575+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 51, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.575+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest.testChangePassword()]
2025-06-30T21:39:40.575+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testChangePassword]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@534c3b9d]; rollback [true]
2025-06-30T21:39:40.575+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 52, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.576+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 53, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.576+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 54, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.576+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 55, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.576+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 创建用户: passwordtest
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f6af096]
2025-06-30T21:39:40.637+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:39:40.637+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
JDBC Connection [HikariProxyConnection@801544361 wrapping conn0: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: INSERT INTO sys_users ( id, username, password_hash, email, phone, real_name, status, department_id, login_count, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939680236847828993(Long), passwordtest(String), $2a$10$8kDeWZSLB1QpahttKb/tUubnnlrfGcFlY33b9V2dnr9UmR/5P0PZe(String), <EMAIL>(String), 13800139001(String), 密码测试用户(String), ACTIVE(String), 1(Long), 0(Integer), 2025-06-30T21:39:40.637241(LocalDateTime), 2025-06-30T21:39:40.637265(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f6af096]
2025-06-30T21:39:40.637+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户创建成功: userId=1939680236847828993
2025-06-30T21:39:40.638+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 修改密码: userId=1939680236847828993
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f6af096] from current transaction
==>  Preparing: SELECT id,username,password_hash AS password,email,phone,real_name,avatar_url,status,department_id,last_login_time,last_login_ip,login_count,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_users WHERE id=? AND deleted=0
==> Parameters: 1939680236847828993(Long)
<==    Columns: ID, USERNAME, PASSWORD, EMAIL, PHONE, REAL_NAME, AVATAR_URL, STATUS, DEPARTMENT_ID, LAST_LOGIN_TIME, LAST_LOGIN_IP, LOGIN_COUNT, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939680236847828993, passwordtest, $2a$10$8kDeWZSLB1QpahttKb/tUubnnlrfGcFlY33b9V2dnr9UmR/5P0PZe, <EMAIL>, 13800139001, 密码测试用户, null, ACTIVE, 1, null, null, 0, 0, 0, 2025-06-30 21:39:40.637241, 2025-06-30 21:39:40.637265, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f6af096]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f6af096] from current transaction
2025-06-30T21:39:40.755+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始更新填充...
2025-06-30T21:39:40.755+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 更新填充完成
==>  Preparing: UPDATE sys_users SET username=?, password_hash=?, email=?, phone=?, real_name=?, status=?, department_id=?, login_count=?, version=?, create_time=?, update_time=?, creator_id=?, updater_id=? WHERE id=? AND version=? AND deleted=0
==> Parameters: passwordtest(String), $2a$10$h1FAZTJlZyik2joyuO7xA.n6UfnbqN2/RpT280CB4zTgGo4eD.SMS(String), <EMAIL>(String), 13800139001(String), 密码测试用户(String), ACTIVE(String), 1(Long), 0(Integer), 1(Integer), 2025-06-30T21:39:40.637241(LocalDateTime), 2025-06-30T21:39:40.637265(LocalDateTime), 1(Long), 1(Long), 1939680236847828993(Long), 0(Integer)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f6af096]
2025-06-30T21:39:40.756+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 密码修改成功: userId=1939680236847828993
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f6af096] from current transaction
==>  Preparing: SELECT id,username,password_hash AS password,email,phone,real_name,avatar_url,status,department_id,last_login_time,last_login_ip,login_count,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_users WHERE id=? AND deleted=0
==> Parameters: 1939680236847828993(Long)
<==    Columns: ID, USERNAME, PASSWORD, EMAIL, PHONE, REAL_NAME, AVATAR_URL, STATUS, DEPARTMENT_ID, LAST_LOGIN_TIME, LAST_LOGIN_IP, LOGIN_COUNT, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939680236847828993, passwordtest, $2a$10$h1FAZTJlZyik2joyuO7xA.n6UfnbqN2/RpT280CB4zTgGo4eD.SMS, <EMAIL>, 13800139001, 密码测试用户, null, ACTIVE, 1, null, null, 0, 1, 0, 2025-06-30 21:39:40.637241, 2025-06-30 21:39:40.637265, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f6af096]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f6af096] from current transaction
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f6af096]
2025-06-30T21:39:40.873+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 56, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.873+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 57, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.874+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 58, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.874+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 59, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.874+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 60, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.874+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 61, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.874+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 62, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.874+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 63, missCount = 1, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f6af096]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f6af096]
2025-06-30T21:39:40.874+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testChangePassword]
2025-06-30T21:39:40.875+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [UserServiceIntegrationTest], method [testChangePassword], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:39:40.875+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
]]></system-out>
  </testcase>
  <testcase name="testBatchOperations" classname="com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest" time="0.131">
    <system-out><![CDATA[2025-06-30T21:39:40.875+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 64, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.875+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:39:40.875+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:39:40.875+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 65, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.875+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 66, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.875+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [UserServiceIntegrationTest], method [testBatchOperations], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:39:40.875+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest] and test method [testBatchOperations]
2025-06-30T21:39:40.875+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 67, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.875+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest.testBatchOperations()]
2025-06-30T21:39:40.875+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testBatchOperations]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@534c3b9d]; rollback [true]
2025-06-30T21:39:40.875+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 68, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.876+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 69, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.876+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 70, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.877+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 71, missCount = 1, failureCount = 0]
2025-06-30T21:39:40.877+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 创建用户: batchtest1
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@751636da]
2025-06-30T21:39:40.935+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:39:40.936+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
JDBC Connection [HikariProxyConnection@977595349 wrapping conn0: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: INSERT INTO sys_users ( id, username, password_hash, email, phone, real_name, status, department_id, login_count, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939680238097731585(Long), batchtest1(String), $2a$10$fFDqoXr.O72KvAKCLKYhe.wfkN.REsKUVI6wZf9dcXPPzy5bG884S(String), <EMAIL>(String), 13800139008(String), 批量测试用户1(String), ACTIVE(String), 1(Long), 0(Integer), 2025-06-30T21:39:40.936106(LocalDateTime), 2025-06-30T21:39:40.936138(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@751636da]
2025-06-30T21:39:40.936+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户创建成功: userId=1939680238097731585
2025-06-30T21:39:40.936+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 创建用户: batchtest2
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@751636da] from current transaction
2025-06-30T21:39:40.994+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:39:40.995+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_users ( id, username, password_hash, email, phone, real_name, status, department_id, login_count, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939680238345195522(Long), batchtest2(String), $2a$10$bFmU6vTVfO5NuSJ.X/nfw.vYLnKan38mQwrHVCmQMQrAsMP3L3XKa(String), <EMAIL>(String), 13800139009(String), 批量测试用户2(String), ACTIVE(String), 1(Long), 0(Integer), 2025-06-30T21:39:40.994986(LocalDateTime), 2025-06-30T21:39:40.995008(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@751636da]
2025-06-30T21:39:40.995+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户创建成功: userId=1939680238345195522
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@751636da] from current transaction
==>  Preparing: UPDATE sys_users SET status = ?, updater_id = ?, update_time = NOW() WHERE id IN ( ? , ? ) AND deleted = 0
==> Parameters: INACTIVE(String), 1(Long), 1939680238097731585(Long), 1939680238345195522(Long)
<==    Updates: 2
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@751636da]
2025-06-30T21:39:40.997+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 批量更新用户状态成功: count=2, status=INACTIVE
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@751636da] from current transaction
==>  Preparing: SELECT id,username,password_hash AS password,email,phone,real_name,avatar_url,status,department_id,last_login_time,last_login_ip,login_count,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_users WHERE id=? AND deleted=0
==> Parameters: 1939680238097731585(Long)
<==    Columns: ID, USERNAME, PASSWORD, EMAIL, PHONE, REAL_NAME, AVATAR_URL, STATUS, DEPARTMENT_ID, LAST_LOGIN_TIME, LAST_LOGIN_IP, LOGIN_COUNT, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939680238097731585, batchtest1, $2a$10$fFDqoXr.O72KvAKCLKYhe.wfkN.REsKUVI6wZf9dcXPPzy5bG884S, <EMAIL>, 13800139008, 批量测试用户1, null, INACTIVE, 1, null, null, 0, 0, 0, 2025-06-30 21:39:40.936106, 2025-06-30 21:39:40.99707, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@751636da]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@751636da] from current transaction
==>  Preparing: SELECT id,username,password_hash AS password,email,phone,real_name,avatar_url,status,department_id,last_login_time,last_login_ip,login_count,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_users WHERE id=? AND deleted=0
==> Parameters: 1939680238345195522(Long)
<==    Columns: ID, USERNAME, PASSWORD, EMAIL, PHONE, REAL_NAME, AVATAR_URL, STATUS, DEPARTMENT_ID, LAST_LOGIN_TIME, LAST_LOGIN_IP, LOGIN_COUNT, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939680238345195522, batchtest2, $2a$10$bFmU6vTVfO5NuSJ.X/nfw.vYLnKan38mQwrHVCmQMQrAsMP3L3XKa, <EMAIL>, 13800139009, 批量测试用户2, null, INACTIVE, 1, null, null, 0, 0, 0, 2025-06-30 21:39:40.994986, 2025-06-30 21:39:40.99707, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@751636da]
2025-06-30T21:39:40.999+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 批量删除用户: userIds=[1939680238097731585, 1939680238345195522]
Load compatibleSet: com.baomidou.mybatisplus.extension.spi.SpringCompatibleSet@2008dfa7
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@751636da] from current transaction
2025-06-30T21:39:41.003+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始更新填充...
2025-06-30T21:39:41.003+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 更新填充完成
==>  Preparing: UPDATE sys_users SET update_time=?,updater_id=?, deleted=1 WHERE id IN ( ? , ? ) AND deleted=0
==> Parameters: 2025-06-30T21:39:41.003140(LocalDateTime), 1(Long), 1939680238097731585(Long), 1939680238345195522(Long)
<==    Updates: 2
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@751636da]
2025-06-30T21:39:41.003+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 批量删除用户成功: count=2
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@751636da] from current transaction
==>  Preparing: SELECT id,username,password_hash AS password,email,phone,real_name,avatar_url,status,department_id,last_login_time,last_login_ip,login_count,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_users WHERE id=? AND deleted=0
==> Parameters: 1939680238097731585(Long)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@751636da]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@751636da] from current transaction
==>  Preparing: SELECT id,username,password_hash AS password,email,phone,real_name,avatar_url,status,department_id,last_login_time,last_login_ip,login_count,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_users WHERE id=? AND deleted=0
==> Parameters: 1939680238345195522(Long)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@751636da]
2025-06-30T21:39:41.005+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 72, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.005+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 73, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.005+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 74, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.005+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 75, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.005+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 76, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.005+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 77, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.006+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 78, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.006+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 79, missCount = 1, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@751636da]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@751636da]
2025-06-30T21:39:41.006+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testBatchOperations]
2025-06-30T21:39:41.006+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [UserServiceIntegrationTest], method [testBatchOperations], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:39:41.006+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
]]></system-out>
  </testcase>
  <testcase name="testUserQuery" classname="com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest" time="0.16">
    <system-out><![CDATA[2025-06-30T21:39:41.006+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 80, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.006+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:39:41.006+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:39:41.006+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 81, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.007+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 82, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.007+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [UserServiceIntegrationTest], method [testUserQuery], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:39:41.007+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest] and test method [testUserQuery]
2025-06-30T21:39:41.007+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 83, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.007+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest.testUserQuery()]
2025-06-30T21:39:41.007+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testUserQuery]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@534c3b9d]; rollback [true]
2025-06-30T21:39:41.007+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 84, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.007+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 85, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.007+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 86, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.008+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 87, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.008+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 创建用户: querytest1
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6222a343]
2025-06-30T21:39:41.066+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:39:41.066+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
JDBC Connection [HikariProxyConnection@1203771863 wrapping conn0: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: INSERT INTO sys_users ( id, username, password_hash, email, phone, real_name, status, department_id, login_count, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939680238647185410(Long), querytest1(String), $2a$10$GTaE1LVh9WL6/KDs0NqulefOjyci0N7k3J790GLqeO4l97l1R.Pge(String), <EMAIL>(String), 13800139003(String), 查询测试用户1(String), ACTIVE(String), 1(Long), 0(Integer), 2025-06-30T21:39:41.066689(LocalDateTime), 2025-06-30T21:39:41.066708(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6222a343]
2025-06-30T21:39:41.067+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户创建成功: userId=1939680238647185410
2025-06-30T21:39:41.067+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 创建用户: querytest2
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6222a343] from current transaction
2025-06-30T21:39:41.126+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:39:41.126+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_users ( id, username, password_hash, email, phone, real_name, status, department_id, login_count, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939680238898843650(Long), querytest2(String), $2a$10$mIsrd73yDtrNLjlCsVkbiefAyIaJu7pwDucUPhZcR2kgMI6ApM9iK(String), <EMAIL>(String), 13800139004(String), 查询测试用户2(String), ACTIVE(String), 2(Long), 0(Integer), 2025-06-30T21:39:41.126259(LocalDateTime), 2025-06-30T21:39:41.126275(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6222a343]
2025-06-30T21:39:41.126+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户创建成功: userId=1939680238898843650
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6222a343] from current transaction
==>  Preparing: SELECT COUNT(*) AS total FROM sys_users WHERE deleted = 0 AND (username LIKE ?)
==> Parameters: %querytest%(String)
<==    Columns: TOTAL
<==        Row: 2
<==      Total: 1
==>  Preparing: SELECT id,username,password_hash AS password,email,phone,real_name,avatar_url,status,department_id,last_login_time,last_login_ip,login_count,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_users WHERE deleted=0 AND (username LIKE ?) ORDER BY create_time DESC LIMIT ?
==> Parameters: %querytest%(String), 10(Long)
<==    Columns: ID, USERNAME, PASSWORD, EMAIL, PHONE, REAL_NAME, AVATAR_URL, STATUS, DEPARTMENT_ID, LAST_LOGIN_TIME, LAST_LOGIN_IP, LOGIN_COUNT, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939680238898843650, querytest2, $2a$10$mIsrd73yDtrNLjlCsVkbiefAyIaJu7pwDucUPhZcR2kgMI6ApM9iK, <EMAIL>, 13800139004, 查询测试用户2, null, ACTIVE, 2, null, null, 0, 0, 0, 2025-06-30 21:39:41.126259, 2025-06-30 21:39:41.126275, 1, 1, <<BLOB>>, null
<==        Row: 1939680238647185410, querytest1, $2a$10$GTaE1LVh9WL6/KDs0NqulefOjyci0N7k3J790GLqeO4l97l1R.Pge, <EMAIL>, 13800139003, 查询测试用户1, null, ACTIVE, 1, null, null, 0, 0, 0, 2025-06-30 21:39:41.066689, 2025-06-30 21:39:41.066708, 1, 1, <<BLOB>>, null
<==      Total: 2
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6222a343]
2025-06-30T21:39:41.166+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 88, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.166+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 89, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.166+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 90, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.166+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 91, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.166+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 92, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.167+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 93, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.167+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 94, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.167+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 95, missCount = 1, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6222a343]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6222a343]
2025-06-30T21:39:41.167+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testUserQuery]
2025-06-30T21:39:41.167+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [UserServiceIntegrationTest], method [testUserQuery], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:39:41.167+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
]]></system-out>
  </testcase>
  <testcase name="testGetUsersByDepartment" classname="com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest" time="0.183">
    <system-out><![CDATA[2025-06-30T21:39:41.167+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 96, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.167+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:39:41.167+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:39:41.167+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 97, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.168+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 98, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.168+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [UserServiceIntegrationTest], method [testGetUsersByDepartment], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:39:41.168+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest] and test method [testGetUsersByDepartment]
2025-06-30T21:39:41.168+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 99, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.168+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest.testGetUsersByDepartment()]
2025-06-30T21:39:41.168+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testGetUsersByDepartment]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@534c3b9d]; rollback [true]
2025-06-30T21:39:41.168+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 100, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.168+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 101, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.168+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 102, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.169+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 103, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.169+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 创建用户: depttest1
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@27bd124a]
2025-06-30T21:39:41.227+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:39:41.227+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
JDBC Connection [HikariProxyConnection@964325592 wrapping conn0: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: INSERT INTO sys_users ( id, username, password_hash, email, phone, real_name, status, department_id, login_count, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939680239322468353(Long), depttest1(String), $2a$10$bt6TLgo9P8DW6woqJAsRyulUnmHRUfYoDZW5FOEqxWzJ0r/y/BhUa(String), <EMAIL>(String), 13800139005(String), 部门测试用户1(String), ACTIVE(String), 1(Long), 0(Integer), 2025-06-30T21:39:41.227784(LocalDateTime), 2025-06-30T21:39:41.227800(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@27bd124a]
2025-06-30T21:39:41.228+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户创建成功: userId=1939680239322468353
2025-06-30T21:39:41.228+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 创建用户: depttest2
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@27bd124a] from current transaction
2025-06-30T21:39:41.286+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:39:41.286+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_users ( id, username, password_hash, email, phone, real_name, status, department_id, login_count, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939680239569932289(Long), depttest2(String), $2a$10$VNSZUUrum8dIt.g4qqordOskpR8dOFvCveQdmGQTV2yKAvMP.qTPq(String), <EMAIL>(String), 13800139006(String), 部门测试用户2(String), ACTIVE(String), 1(Long), 0(Integer), 2025-06-30T21:39:41.286813(LocalDateTime), 2025-06-30T21:39:41.286833(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@27bd124a]
2025-06-30T21:39:41.287+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户创建成功: userId=1939680239569932289
2025-06-30T21:39:41.287+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 创建用户: depttest3
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@27bd124a] from current transaction
2025-06-30T21:39:41.346+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:39:41.346+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_users ( id, username, password_hash, email, phone, real_name, status, department_id, login_count, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939680239821590529(Long), depttest3(String), $2a$10$fAKFApHCdJGWYVglonMQNeoToTBgfqGyNuL1326tdzS23Vp4lET/G(String), <EMAIL>(String), 13800139007(String), 部门测试用户3(String), ACTIVE(String), 2(Long), 0(Integer), 2025-06-30T21:39:41.346489(LocalDateTime), 2025-06-30T21:39:41.346512(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@27bd124a]
2025-06-30T21:39:41.347+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户创建成功: userId=1939680239821590529
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@27bd124a] from current transaction
==>  Preparing: SELECT id,username,password_hash AS password,email,phone,real_name,avatar_url,status,department_id,last_login_time,last_login_ip,login_count,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_users WHERE deleted=0 AND (department_id = ?) ORDER BY create_time DESC
==> Parameters: 1(Long)
<==    Columns: ID, USERNAME, PASSWORD, EMAIL, PHONE, REAL_NAME, AVATAR_URL, STATUS, DEPARTMENT_ID, LAST_LOGIN_TIME, LAST_LOGIN_IP, LOGIN_COUNT, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939680239569932289, depttest2, $2a$10$VNSZUUrum8dIt.g4qqordOskpR8dOFvCveQdmGQTV2yKAvMP.qTPq, <EMAIL>, 13800139006, 部门测试用户2, null, ACTIVE, 1, null, null, 0, 0, 0, 2025-06-30 21:39:41.286813, 2025-06-30 21:39:41.286833, 1, 1, <<BLOB>>, null
<==        Row: 1939680239322468353, depttest1, $2a$10$bt6TLgo9P8DW6woqJAsRyulUnmHRUfYoDZW5FOEqxWzJ0r/y/BhUa, <EMAIL>, 13800139005, 部门测试用户1, null, ACTIVE, 1, null, null, 0, 0, 0, 2025-06-30 21:39:41.227784, 2025-06-30 21:39:41.2278, 1, 1, <<BLOB>>, null
<==      Total: 2
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@27bd124a]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@27bd124a] from current transaction
==>  Preparing: SELECT id,username,password_hash AS password,email,phone,real_name,avatar_url,status,department_id,last_login_time,last_login_ip,login_count,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_users WHERE deleted=0 AND (department_id = ?) ORDER BY create_time DESC
==> Parameters: 2(Long)
<==    Columns: ID, USERNAME, PASSWORD, EMAIL, PHONE, REAL_NAME, AVATAR_URL, STATUS, DEPARTMENT_ID, LAST_LOGIN_TIME, LAST_LOGIN_IP, LOGIN_COUNT, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939680239821590529, depttest3, $2a$10$fAKFApHCdJGWYVglonMQNeoToTBgfqGyNuL1326tdzS23Vp4lET/G, <EMAIL>, 13800139007, 部门测试用户3, null, ACTIVE, 2, null, null, 0, 0, 0, 2025-06-30 21:39:41.346489, 2025-06-30 21:39:41.346512, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@27bd124a]
2025-06-30T21:39:41.349+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 104, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.349+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 105, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.350+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 106, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.350+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 107, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.350+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 108, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.350+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 109, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.350+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 110, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.350+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 111, missCount = 1, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@27bd124a]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@27bd124a]
2025-06-30T21:39:41.350+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testGetUsersByDepartment]
2025-06-30T21:39:41.350+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [UserServiceIntegrationTest], method [testGetUsersByDepartment], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:39:41.350+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
]]></system-out>
  </testcase>
  <testcase name="testCreateAndGetUser" classname="com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest" time="0.063">
    <system-out><![CDATA[2025-06-30T21:39:41.351+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 112, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.351+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:39:41.351+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:39:41.351+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 113, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.351+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 114, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.351+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [UserServiceIntegrationTest], method [testCreateAndGetUser], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:39:41.351+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest] and test method [testCreateAndGetUser]
2025-06-30T21:39:41.351+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 115, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.351+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest.testCreateAndGetUser()]
2025-06-30T21:39:41.351+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testCreateAndGetUser]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@534c3b9d]; rollback [true]
2025-06-30T21:39:41.352+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 116, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.352+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 117, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.352+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 118, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.352+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 119, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.352+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 创建用户: integrationtest
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62bcfa01]
2025-06-30T21:39:41.411+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:39:41.411+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
JDBC Connection [HikariProxyConnection@1916873789 wrapping conn0: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: INSERT INTO sys_users ( id, username, password_hash, email, phone, real_name, status, department_id, login_count, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939680240094220289(Long), integrationtest(String), $2a$10$A0/G46bllWlZUijGePUPmexJD/bDaW3OpoLzzM.CxH/.YXDNkSnIK(String), <EMAIL>(String), 13800138888(String), 集成测试用户(String), ACTIVE(String), 1(Long), 0(Integer), 2025-06-30T21:39:41.411830(LocalDateTime), 2025-06-30T21:39:41.411848(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62bcfa01]
2025-06-30T21:39:41.412+08:00  INFO 17236 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户创建成功: userId=1939680240094220289
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62bcfa01] from current transaction
==>  Preparing: SELECT id,username,password_hash AS password,email,phone,real_name,avatar_url,status,department_id,last_login_time,last_login_ip,login_count,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_users WHERE id=? AND deleted=0
==> Parameters: 1939680240094220289(Long)
<==    Columns: ID, USERNAME, PASSWORD, EMAIL, PHONE, REAL_NAME, AVATAR_URL, STATUS, DEPARTMENT_ID, LAST_LOGIN_TIME, LAST_LOGIN_IP, LOGIN_COUNT, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939680240094220289, integrationtest, $2a$10$A0/G46bllWlZUijGePUPmexJD/bDaW3OpoLzzM.CxH/.YXDNkSnIK, <EMAIL>, 13800138888, 集成测试用户, null, ACTIVE, 1, null, null, 0, 0, 0, 2025-06-30 21:39:41.41183, 2025-06-30 21:39:41.411848, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62bcfa01]
2025-06-30T21:39:41.413+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 120, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.413+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 121, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.413+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 122, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.413+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 123, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.413+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 124, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.413+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 125, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.413+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 126, missCount = 1, failureCount = 0]
2025-06-30T21:39:41.413+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@369d624d size = 1, maxSize = 32, parentContextCount = 0, hitCount = 127, missCount = 1, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62bcfa01]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62bcfa01]
2025-06-30T21:39:41.414+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testCreateAndGetUser]
2025-06-30T21:39:41.414+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [UserServiceIntegrationTest], method [testCreateAndGetUser], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:39:41.414+08:00 DEBUG 17236 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
]]></system-out>
  </testcase>
</testsuite>