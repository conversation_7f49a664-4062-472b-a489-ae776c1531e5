<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest" time="3.473" tests="8" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="21"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/target/test-classes:/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.4.7/spring-boot-starter-web-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.4.7/spring-boot-starter-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.4.7/spring-boot-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.4.7/spring-boot-starter-logging-3.4.7.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.5.18/logback-classic-1.5.18.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.4.7/spring-boot-starter-json-3.4.7.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.18.4/jackson-datatype-jdk8-2.18.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.18.4/jackson-module-parameter-names-2.18.4.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.2.8/spring-web-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.2.8/spring-beans-6.2.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.14.8/micrometer-observation-1.14.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.14.8/micrometer-commons-1.14.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.2.8/spring-webmvc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.2.8/spring-context-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.2.8/spring-expression-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-undertow/3.4.7/spring-boot-starter-undertow-3.4.7.jar:/Users/<USER>/.m2/repository/io/undertow/undertow-core/2.3.18.Final/undertow-core-2.3.18.Final.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.6.1.Final/jboss-logging-3.6.1.Final.jar:/Users/<USER>/.m2/repository/org/jboss/xnio/xnio-api/3.8.16.Final/xnio-api-3.8.16.Final.jar:/Users/<USER>/.m2/repository/org/wildfly/common/wildfly-common/1.5.4.Final/wildfly-common-1.5.4.Final.jar:/Users/<USER>/.m2/repository/org/wildfly/client/wildfly-client-config/1.0.1.Final/wildfly-client-config-1.0.1.Final.jar:/Users/<USER>/.m2/repository/org/jboss/xnio/xnio-nio/3.8.16.Final/xnio-nio-3.8.16.Final.jar:/Users/<USER>/.m2/repository/org/jboss/threads/jboss-threads/3.5.0.Final/jboss-threads-3.5.0.Final.jar:/Users/<USER>/.m2/repository/io/undertow/undertow-servlet/2.3.18.Final/undertow-servlet-2.3.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/servlet/jakarta.servlet-api/6.0.0/jakarta.servlet-api-6.0.0.jar:/Users/<USER>/.m2/repository/io/undertow/undertow-websockets-jsr/2.3.18.Final/undertow-websockets-jsr-2.3.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/websocket/jakarta.websocket-api/2.1.1/jakarta.websocket-api-2.1.1.jar:/Users/<USER>/.m2/repository/jakarta/websocket/jakarta.websocket-client-api/2.1.1/jakarta.websocket-client-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.42/tomcat-embed-el-10.1.42.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.4.7/spring-boot-starter-validation-3.4.7.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.2.Final/hibernate-validator-8.0.2.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.7.0/classmate-1.7.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.4.7/spring-boot-starter-security-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.2.8/spring-aop-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.4.7/spring-security-config-6.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.4.7/spring-security-web-6.4.7.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-discovery/2023.0.3.3/spring-cloud-starter-alibaba-nacos-discovery-2023.0.3.3.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-alibaba-commons/2023.0.3.3/spring-cloud-alibaba-commons-2023.0.3.3.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.4.2/nacos-client-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-auth-plugin/2.4.2/nacos-auth-plugin-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-encryption-plugin/2.4.2/nacos-encryption-plugin-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-logback-adapter-12/2.4.2/nacos-logback-adapter-12-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/logback-adapter/1.1.3/logback-adapter-1.1.3.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-log4j2-adapter/2.4.2/nacos-log4j2-adapter-2.4.2.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.17.2/commons-codec-1.17.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.18.4.1/jackson-core-2.18.4.1.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpasyncclient/4.1.5/httpasyncclient-4.1.5.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore-nio/4.4.16/httpcore-nio-4.4.16.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient/0.16.0/simpleclient-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_otel/0.16.0/simpleclient_tracer_otel-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_common/0.16.0/simpleclient_tracer_common-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_otel_agent/0.16.0/simpleclient_tracer_otel_agent-0.16.0.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.14.8/micrometer-core-1.14.8.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.2.2/HdrHistogram-2.2.2.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-commons/4.2.1/spring-cloud-commons-4.2.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.4.7/spring-security-crypto-6.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-context/4.2.1/spring-cloud-context-4.2.1.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-config/2023.0.3.3/spring-cloud-starter-alibaba-nacos-config-2023.0.3.3.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-alibaba-nacos-config/2023.0.3.3/spring-alibaba-nacos-config-2023.0.3.3.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jdbc/3.4.7/spring-boot-starter-data-jdbc-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.4.7/spring-boot-starter-jdbc-3.4.7.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/5.1.0/HikariCP-5.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.2.8/spring-jdbc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jdbc/3.4.7/spring-data-jdbc-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-relational/3.4.7/spring-data-relational-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.4.7/spring-data-commons-3.4.7.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter/3.5.12/mybatis-plus-spring-boot3-starter-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.12/mybatis-plus-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.12/mybatis-plus-core-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.12/mybatis-plus-annotation-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring/3.5.12/mybatis-plus-spring-3.5.12.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.19/mybatis-3.5.19.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/3.0.4/mybatis-spring-3.0.4.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.12/mybatis-plus-spring-boot-autoconfigure-3.5.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.4.7/spring-boot-autoconfigure-3.4.7.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-jsqlparser/3.5.12/mybatis-plus-jsqlparser-3.5.12.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/5.1/jsqlparser-5.1.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-jsqlparser-common/3.5.12/mybatis-plus-jsqlparser-common-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.12/mybatis-plus-extension-3.5.12.jar:/Users/<USER>/.m2/repository/org/postgresql/postgresql/42.7.7/postgresql-42.7.7.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.49.3/checker-qual-3.49.3.jar:/Users/<USER>/.m2/repository/org/liquibase/liquibase-core/4.29.2/liquibase-core-4.29.2.jar:/Users/<USER>/.m2/repository/com/opencsv/opencsv/5.9/opencsv-5.9.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.3/snakeyaml-2.3.jar:/Users/<USER>/.m2/repository/javax/xml/bind/jaxb-api/2.3.1/jaxb-api-2.3.1.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.17.0/commons-io-2.17.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.5.0/commons-collections4-4.5.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.13.1/commons-text-1.13.1.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar:/Users/<USER>/.m2/repository/com/bdyl/erp/pisp/pisp-common-core/1.0.0-SNAPSHOT/pisp-common-core-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.38/hutool-all-5.8.38.jar:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.6.3/mapstruct-1.6.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.18.4/jackson-databind-2.18.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.18.4/jackson-annotations-2.18.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.18.4/jackson-datatype-jsr310-2.18.4.jar:/Users/<USER>/.m2/repository/com/bdyl/erp/pisp/pisp-common-security/1.0.0-SNAPSHOT/pisp-common-security-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.12.6/jjwt-api-0.12.6.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.12.6/jjwt-impl-0.12.6.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.12.6/jjwt-jackson-0.12.6.jar:/Users/<USER>/.m2/repository/com/bdyl/erp/pisp/pisp-common-web/1.0.0-SNAPSHOT/pisp-common-web-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/com/bdyl/erp/pisp/pisp-common-redis/1.0.0-SNAPSHOT/pisp-common-redis-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/3.4.7/spring-boot-starter-data-redis-3.4.7.jar:/Users/<USER>/.m2/repository/io/lettuce/lettuce-core/6.4.2.RELEASE/lettuce-core-6.4.2.RELEASE.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.122.Final/netty-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.122.Final/netty-handler-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.122.Final/netty-resolver-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.122.Final/netty-buffer-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.122.Final/netty-transport-native-unix-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.122.Final/netty-codec-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.122.Final/netty-transport-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.7.7/reactor-core-3.7.7.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/3.4.7/spring-data-redis-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/3.4.7/spring-data-keyvalue-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.2.8/spring-oxm-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.2.8/spring-context-support-6.2.8.jar:/Users/<USER>/.m2/repository/com/bdyl/erp/pisp/pisp-api/1.0.0-SNAPSHOT/pisp-api-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.4.7/spring-boot-starter-test-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.4.7/spring-boot-test-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.4.7/spring-boot-test-autoconfigure-3.4.7.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/9.7.1/asm-9.7.1.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.26.3/assertj-core-3.26.3.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.15.11/byte-buddy-1.15.11.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.11.4/junit-jupiter-5.11.4.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.11.4/junit-jupiter-api-5.11.4.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.11.4/junit-platform-commons-1.11.4.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.11.4/junit-jupiter-params-5.11.4.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.11.4/junit-jupiter-engine-5.11.4.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.11.4/junit-platform-engine-1.11.4.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.14.2/mockito-core-5.14.2.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.15.11/byte-buddy-agent-1.15.11.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.14.2/mockito-junit-jupiter-5.14.2.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.2.8/spring-core-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.2.8/spring-jcl-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.2.8/spring-test-6.2.8.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.10.2/xmlunit-core-2.10.2.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-test/6.4.7/spring-security-test-6.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.4.7/spring-security-core-6.4.7.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter-test/3.5.12/mybatis-plus-spring-boot3-starter-test-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-test-autoconfigure/3.5.12/mybatis-plus-spring-boot-test-autoconfigure-3.5.12.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.2.8/spring-tx-6.2.8.jar:/Users/<USER>/.m2/repository/org/testcontainers/postgresql/1.20.6/postgresql-1.20.6.jar:/Users/<USER>/.m2/repository/org/testcontainers/jdbc/1.20.6/jdbc-1.20.6.jar:/Users/<USER>/.m2/repository/org/testcontainers/database-commons/1.20.6/database-commons-1.20.6.jar:/Users/<USER>/.m2/repository/org/testcontainers/testcontainers/1.20.6/testcontainers-1.20.6.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/repository/org/rnorth/duct-tape/duct-tape/1.0.8/duct-tape-1.0.8.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/17.0.0/annotations-17.0.0.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-api/3.4.1/docker-java-api-3.4.1.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-transport-zerodep/3.4.1/docker-java-transport-zerodep-3.4.1.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-transport/3.4.1/docker-java-transport-3.4.1.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.13.0/jna-5.13.0.jar:/Users/<USER>/.m2/repository/com/h2database/h2/2.3.232/h2-2.3.232.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.38/lombok-1.18.38.jar:"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="org.jboss.logging.provider" value="slf4j"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="21"/>
    <property name="APPLICATION_NAME" value="pisp-user-service-test"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="/Users/<USER>/.sdkman/candidates/java/21.0.7-oracle/lib"/>
    <property name="sun.java.command" value="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/target/surefire/surefirebooter-20250630213518713_3.jar /Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/target/surefire 2025-06-30T21-35-18_678-jvmRun1 surefire-20250630213518713_1tmp surefire_0-20250630213518713_2tmp"/>
    <property name="http.nonProxyHosts" value="127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|&lt;local&gt;|*.&lt;local&gt;"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/target/test-classes:/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.4.7/spring-boot-starter-web-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.4.7/spring-boot-starter-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.4.7/spring-boot-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.4.7/spring-boot-starter-logging-3.4.7.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.5.18/logback-classic-1.5.18.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.4.7/spring-boot-starter-json-3.4.7.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.18.4/jackson-datatype-jdk8-2.18.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.18.4/jackson-module-parameter-names-2.18.4.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.2.8/spring-web-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.2.8/spring-beans-6.2.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.14.8/micrometer-observation-1.14.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.14.8/micrometer-commons-1.14.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.2.8/spring-webmvc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.2.8/spring-context-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.2.8/spring-expression-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-undertow/3.4.7/spring-boot-starter-undertow-3.4.7.jar:/Users/<USER>/.m2/repository/io/undertow/undertow-core/2.3.18.Final/undertow-core-2.3.18.Final.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.6.1.Final/jboss-logging-3.6.1.Final.jar:/Users/<USER>/.m2/repository/org/jboss/xnio/xnio-api/3.8.16.Final/xnio-api-3.8.16.Final.jar:/Users/<USER>/.m2/repository/org/wildfly/common/wildfly-common/1.5.4.Final/wildfly-common-1.5.4.Final.jar:/Users/<USER>/.m2/repository/org/wildfly/client/wildfly-client-config/1.0.1.Final/wildfly-client-config-1.0.1.Final.jar:/Users/<USER>/.m2/repository/org/jboss/xnio/xnio-nio/3.8.16.Final/xnio-nio-3.8.16.Final.jar:/Users/<USER>/.m2/repository/org/jboss/threads/jboss-threads/3.5.0.Final/jboss-threads-3.5.0.Final.jar:/Users/<USER>/.m2/repository/io/undertow/undertow-servlet/2.3.18.Final/undertow-servlet-2.3.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/servlet/jakarta.servlet-api/6.0.0/jakarta.servlet-api-6.0.0.jar:/Users/<USER>/.m2/repository/io/undertow/undertow-websockets-jsr/2.3.18.Final/undertow-websockets-jsr-2.3.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/websocket/jakarta.websocket-api/2.1.1/jakarta.websocket-api-2.1.1.jar:/Users/<USER>/.m2/repository/jakarta/websocket/jakarta.websocket-client-api/2.1.1/jakarta.websocket-client-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.42/tomcat-embed-el-10.1.42.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.4.7/spring-boot-starter-validation-3.4.7.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.2.Final/hibernate-validator-8.0.2.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.7.0/classmate-1.7.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.4.7/spring-boot-starter-security-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.2.8/spring-aop-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.4.7/spring-security-config-6.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.4.7/spring-security-web-6.4.7.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-discovery/2023.0.3.3/spring-cloud-starter-alibaba-nacos-discovery-2023.0.3.3.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-alibaba-commons/2023.0.3.3/spring-cloud-alibaba-commons-2023.0.3.3.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.4.2/nacos-client-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-auth-plugin/2.4.2/nacos-auth-plugin-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-encryption-plugin/2.4.2/nacos-encryption-plugin-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-logback-adapter-12/2.4.2/nacos-logback-adapter-12-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/logback-adapter/1.1.3/logback-adapter-1.1.3.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-log4j2-adapter/2.4.2/nacos-log4j2-adapter-2.4.2.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.17.2/commons-codec-1.17.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.18.4.1/jackson-core-2.18.4.1.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpasyncclient/4.1.5/httpasyncclient-4.1.5.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore-nio/4.4.16/httpcore-nio-4.4.16.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient/0.16.0/simpleclient-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_otel/0.16.0/simpleclient_tracer_otel-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_common/0.16.0/simpleclient_tracer_common-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_otel_agent/0.16.0/simpleclient_tracer_otel_agent-0.16.0.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.14.8/micrometer-core-1.14.8.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.2.2/HdrHistogram-2.2.2.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-commons/4.2.1/spring-cloud-commons-4.2.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.4.7/spring-security-crypto-6.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-context/4.2.1/spring-cloud-context-4.2.1.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-config/2023.0.3.3/spring-cloud-starter-alibaba-nacos-config-2023.0.3.3.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-alibaba-nacos-config/2023.0.3.3/spring-alibaba-nacos-config-2023.0.3.3.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jdbc/3.4.7/spring-boot-starter-data-jdbc-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.4.7/spring-boot-starter-jdbc-3.4.7.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/5.1.0/HikariCP-5.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.2.8/spring-jdbc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jdbc/3.4.7/spring-data-jdbc-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-relational/3.4.7/spring-data-relational-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.4.7/spring-data-commons-3.4.7.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter/3.5.12/mybatis-plus-spring-boot3-starter-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.12/mybatis-plus-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.12/mybatis-plus-core-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.12/mybatis-plus-annotation-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring/3.5.12/mybatis-plus-spring-3.5.12.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.19/mybatis-3.5.19.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/3.0.4/mybatis-spring-3.0.4.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.12/mybatis-plus-spring-boot-autoconfigure-3.5.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.4.7/spring-boot-autoconfigure-3.4.7.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-jsqlparser/3.5.12/mybatis-plus-jsqlparser-3.5.12.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/5.1/jsqlparser-5.1.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-jsqlparser-common/3.5.12/mybatis-plus-jsqlparser-common-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.12/mybatis-plus-extension-3.5.12.jar:/Users/<USER>/.m2/repository/org/postgresql/postgresql/42.7.7/postgresql-42.7.7.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.49.3/checker-qual-3.49.3.jar:/Users/<USER>/.m2/repository/org/liquibase/liquibase-core/4.29.2/liquibase-core-4.29.2.jar:/Users/<USER>/.m2/repository/com/opencsv/opencsv/5.9/opencsv-5.9.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.3/snakeyaml-2.3.jar:/Users/<USER>/.m2/repository/javax/xml/bind/jaxb-api/2.3.1/jaxb-api-2.3.1.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.17.0/commons-io-2.17.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.5.0/commons-collections4-4.5.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.13.1/commons-text-1.13.1.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar:/Users/<USER>/.m2/repository/com/bdyl/erp/pisp/pisp-common-core/1.0.0-SNAPSHOT/pisp-common-core-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.38/hutool-all-5.8.38.jar:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.6.3/mapstruct-1.6.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.18.4/jackson-databind-2.18.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.18.4/jackson-annotations-2.18.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.18.4/jackson-datatype-jsr310-2.18.4.jar:/Users/<USER>/.m2/repository/com/bdyl/erp/pisp/pisp-common-security/1.0.0-SNAPSHOT/pisp-common-security-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.12.6/jjwt-api-0.12.6.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.12.6/jjwt-impl-0.12.6.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.12.6/jjwt-jackson-0.12.6.jar:/Users/<USER>/.m2/repository/com/bdyl/erp/pisp/pisp-common-web/1.0.0-SNAPSHOT/pisp-common-web-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/com/bdyl/erp/pisp/pisp-common-redis/1.0.0-SNAPSHOT/pisp-common-redis-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/3.4.7/spring-boot-starter-data-redis-3.4.7.jar:/Users/<USER>/.m2/repository/io/lettuce/lettuce-core/6.4.2.RELEASE/lettuce-core-6.4.2.RELEASE.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.122.Final/netty-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.122.Final/netty-handler-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.122.Final/netty-resolver-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.122.Final/netty-buffer-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.122.Final/netty-transport-native-unix-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.122.Final/netty-codec-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.122.Final/netty-transport-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.7.7/reactor-core-3.7.7.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/3.4.7/spring-data-redis-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/3.4.7/spring-data-keyvalue-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.2.8/spring-oxm-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.2.8/spring-context-support-6.2.8.jar:/Users/<USER>/.m2/repository/com/bdyl/erp/pisp/pisp-api/1.0.0-SNAPSHOT/pisp-api-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.4.7/spring-boot-starter-test-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.4.7/spring-boot-test-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.4.7/spring-boot-test-autoconfigure-3.4.7.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/9.7.1/asm-9.7.1.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.26.3/assertj-core-3.26.3.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.15.11/byte-buddy-1.15.11.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.11.4/junit-jupiter-5.11.4.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.11.4/junit-jupiter-api-5.11.4.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.11.4/junit-platform-commons-1.11.4.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.11.4/junit-jupiter-params-5.11.4.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.11.4/junit-jupiter-engine-5.11.4.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.11.4/junit-platform-engine-1.11.4.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.14.2/mockito-core-5.14.2.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.15.11/byte-buddy-agent-1.15.11.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.14.2/mockito-junit-jupiter-5.14.2.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.2.8/spring-core-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.2.8/spring-jcl-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.2.8/spring-test-6.2.8.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.10.2/xmlunit-core-2.10.2.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-test/6.4.7/spring-security-test-6.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.4.7/spring-security-core-6.4.7.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter-test/3.5.12/mybatis-plus-spring-boot3-starter-test-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-test-autoconfigure/3.5.12/mybatis-plus-spring-boot-test-autoconfigure-3.5.12.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.2.8/spring-tx-6.2.8.jar:/Users/<USER>/.m2/repository/org/testcontainers/postgresql/1.20.6/postgresql-1.20.6.jar:/Users/<USER>/.m2/repository/org/testcontainers/jdbc/1.20.6/jdbc-1.20.6.jar:/Users/<USER>/.m2/repository/org/testcontainers/database-commons/1.20.6/database-commons-1.20.6.jar:/Users/<USER>/.m2/repository/org/testcontainers/testcontainers/1.20.6/testcontainers-1.20.6.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/repository/org/rnorth/duct-tape/duct-tape/1.0.8/duct-tape-1.0.8.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/17.0.0/annotations-17.0.0.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-api/3.4.1/docker-java-api-3.4.1.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-transport-zerodep/3.4.1/docker-java-transport-zerodep-3.4.1.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-transport/3.4.1/docker-java-transport-3.4.1.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.13.0/jna-5.13.0.jar:/Users/<USER>/.m2/repository/com/h2database/h2/2.3.232/h2-2.3.232.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.38/lombok-1.18.38.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="zh"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-04-15"/>
    <property name="java.home" value="/Users/<USER>/.sdkman/candidates/java/21.0.7-oracle"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user"/>
    <property name="java.vm.compressedOopsMode" value="Non-zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="checkstyle.skip" value="true"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="FILE_LOG_CHARSET" value="UTF-8"/>
    <property name="java.awt.headless" value="true"/>
    <property name="apple.awt.application.name" value="ForkedBooter"/>
    <property name="surefire.real.class.path" value="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/target/surefire/surefirebooter-20250630213518713_3.jar"/>
    <property name="user.script" value="Hans"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|&lt;local&gt;|*.&lt;local&gt;"/>
    <property name="java.runtime.version" value="21.0.7+8-LTS-245"/>
    <property name="user.name" value="jeffery"/>
    <property name="stdout.encoding" value="UTF-8"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.5"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="/var/folders/2c/k8bwfr1j5cz_4rmd02kztnph0000gn/T/"/>
    <property name="com.zaxxer.hikari.pool_number" value="1"/>
    <property name="java.version" value="21.0.7"/>
    <property name="user.dir" value="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user"/>
    <property name="os.arch" value="aarch64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="PID" value="13932"/>
    <property name="CONSOLE_LOG_CHARSET" value="UTF-8"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="UTF-8"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="21.0.7+8-LTS-245"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="socksNonProxyHosts" value="127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|&lt;local&gt;|*.&lt;local&gt;"/>
    <property name="java.class.version" value="65.0"/>
    <property name="LOGGED_APPLICATION_NAME" value="[pisp-user-service-test] "/>
  </properties>
  <testcase name="testUserStatusOperations" classname="com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest" time="0.494">
    <system-out><![CDATA[21:35:19.014 [main] INFO org.springframework.test.context.support.AnnotationConfigContextLoaderUtils -- Could not detect default configuration classes for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]: UserServiceIntegrationTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
21:35:19.054 [main] INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper -- Found @SpringBootConfiguration com.bdyl.erp.pisp.user.UserServiceApplication for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.7)

2025-06-30T21:35:19.347+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.i.UserServiceIntegrationTest   : Starting UserServiceIntegrationTest using Java 21.0.7 with PID 13932 (started by jeffery in /Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user)
2025-06-30T21:35:19.347+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.i.UserServiceIntegrationTest   : Running with Spring Boot v3.4.7, Spring v6.2.8
2025-06-30T21:35:19.348+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.i.UserServiceIntegrationTest   : The following 1 profile is active: "test"
Logging initialized using 'class org.apache.ibatis.logging.stdout.StdOutImpl' adapter.
Get /192.168.2.219 network interface 
Get network interface info: name:en0 (en0)
Initialization Sequence datacenterId:20 workerId:24
2025-06-30T21:35:20.783+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.erp.pisp.user.config.SecurityConfig  : 配置Spring Security过滤器链
2025-06-30T21:35:20.791+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.erp.pisp.user.config.SecurityConfig  : Spring Security配置完成
2025-06-30T21:35:20.954+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.i.UserServiceIntegrationTest   : Started UserServiceIntegrationTest in 1.849 seconds (process running for 2.204)
2025-06-30T21:35:20.956+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 0, missCount = 1, failureCount = 0]
2025-06-30T21:35:20.956+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:35:20.959+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:35:20.959+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 1, missCount = 1, failureCount = 0]
2025-06-30T21:35:20.960+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 2, missCount = 1, failureCount = 0]
2025-06-30T21:35:20.961+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [UserServiceIntegrationTest], method [testUserStatusOperations], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:20.962+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest] and test method [testUserStatusOperations]
2025-06-30T21:35:20.962+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 3, missCount = 1, failureCount = 0]
2025-06-30T21:35:20.963+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest.testUserStatusOperations()]
2025-06-30T21:35:20.964+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testUserStatusOperations]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@52e5ad71]; rollback [true]
2025-06-30T21:35:20.964+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 4, missCount = 1, failureCount = 0]
2025-06-30T21:35:20.966+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 5, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.335+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 6, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.338+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 7, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.340+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 创建用户: statustest
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f5a23c1]
2025-06-30T21:35:21.414+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:21.415+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
JDBC Connection [HikariProxyConnection@1247358242 wrapping conn0: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: INSERT INTO sys_users ( id, username, password_hash, email, phone, real_name, status, department_id, login_count, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679149587791874(Long), statustest(String), $2a$10$h2p44miN2uwT5xaBeNXdte3vCX7r5mtx.6ha3g7aZL99S6plCbet6(String), <EMAIL>(String), 13800139002(String), 状态测试用户(String), ACTIVE(String), 1(Long), 0(Integer), 2025-06-30T21:35:21.414510(LocalDateTime), 2025-06-30T21:35:21.414953(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f5a23c1]
2025-06-30T21:35:21.427+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户创建成功: userId=1939679149587791874
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f5a23c1] from current transaction
==>  Preparing: SELECT id,username,password_hash AS password,email,phone,real_name,avatar_url,status,department_id,last_login_time,last_login_ip,login_count,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_users WHERE id=? AND deleted=0
==> Parameters: 1939679149587791874(Long)
<==    Columns: ID, USERNAME, PASSWORD, EMAIL, PHONE, REAL_NAME, AVATAR_URL, STATUS, DEPARTMENT_ID, LAST_LOGIN_TIME, LAST_LOGIN_IP, LOGIN_COUNT, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679149587791874, statustest, $2a$10$h2p44miN2uwT5xaBeNXdte3vCX7r5mtx.6ha3g7aZL99S6plCbet6, <EMAIL>, 13800139002, 状态测试用户, null, ACTIVE, 1, null, null, 0, 0, 0, 2025-06-30 21:35:21.41451, 2025-06-30 21:35:21.414953, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f5a23c1]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f5a23c1] from current transaction
2025-06-30T21:35:21.441+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始更新填充...
2025-06-30T21:35:21.442+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 更新填充完成
==>  Preparing: UPDATE sys_users SET username=?, password_hash=?, email=?, phone=?, real_name=?, status=?, department_id=?, login_count=?, version=?, create_time=?, update_time=?, creator_id=?, updater_id=? WHERE id=? AND version=? AND deleted=0
==> Parameters: statustest(String), $2a$10$h2p44miN2uwT5xaBeNXdte3vCX7r5mtx.6ha3g7aZL99S6plCbet6(String), <EMAIL>(String), 13800139002(String), 状态测试用户(String), INACTIVE(String), 1(Long), 0(Integer), 1(Integer), 2025-06-30T21:35:21.414510(LocalDateTime), 2025-06-30T21:35:21.414953(LocalDateTime), 1(Long), 1(Long), 1939679149587791874(Long), 0(Integer)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f5a23c1]
2025-06-30T21:35:21.443+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户状态更新成功: userId=1939679149587791874, status=INACTIVE
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f5a23c1] from current transaction
==>  Preparing: SELECT id,username,password_hash AS password,email,phone,real_name,avatar_url,status,department_id,last_login_time,last_login_ip,login_count,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_users WHERE id=? AND deleted=0
==> Parameters: 1939679149587791874(Long)
<==    Columns: ID, USERNAME, PASSWORD, EMAIL, PHONE, REAL_NAME, AVATAR_URL, STATUS, DEPARTMENT_ID, LAST_LOGIN_TIME, LAST_LOGIN_IP, LOGIN_COUNT, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679149587791874, statustest, $2a$10$h2p44miN2uwT5xaBeNXdte3vCX7r5mtx.6ha3g7aZL99S6plCbet6, <EMAIL>, 13800139002, 状态测试用户, null, INACTIVE, 1, null, null, 0, 1, 0, 2025-06-30 21:35:21.41451, 2025-06-30 21:35:21.414953, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f5a23c1]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f5a23c1] from current transaction
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f5a23c1]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f5a23c1] from current transaction
2025-06-30T21:35:21.444+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始更新填充...
2025-06-30T21:35:21.444+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 更新填充完成
==>  Preparing: UPDATE sys_users SET username=?, password_hash=?, email=?, phone=?, real_name=?, status=?, department_id=?, login_count=?, version=?, create_time=?, update_time=?, creator_id=?, updater_id=? WHERE id=? AND version=? AND deleted=0
==> Parameters: statustest(String), $2a$10$h2p44miN2uwT5xaBeNXdte3vCX7r5mtx.6ha3g7aZL99S6plCbet6(String), <EMAIL>(String), 13800139002(String), 状态测试用户(String), LOCKED(String), 1(Long), 0(Integer), 2(Integer), 2025-06-30T21:35:21.414510(LocalDateTime), 2025-06-30T21:35:21.414953(LocalDateTime), 1(Long), 1(Long), 1939679149587791874(Long), 1(Integer)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f5a23c1]
2025-06-30T21:35:21.445+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户状态更新成功: userId=1939679149587791874, status=LOCKED
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f5a23c1] from current transaction
==>  Preparing: SELECT id,username,password_hash AS password,email,phone,real_name,avatar_url,status,department_id,last_login_time,last_login_ip,login_count,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_users WHERE id=? AND deleted=0
==> Parameters: 1939679149587791874(Long)
<==    Columns: ID, USERNAME, PASSWORD, EMAIL, PHONE, REAL_NAME, AVATAR_URL, STATUS, DEPARTMENT_ID, LAST_LOGIN_TIME, LAST_LOGIN_IP, LOGIN_COUNT, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679149587791874, statustest, $2a$10$h2p44miN2uwT5xaBeNXdte3vCX7r5mtx.6ha3g7aZL99S6plCbet6, <EMAIL>, 13800139002, 状态测试用户, null, LOCKED, 1, null, null, 0, 2, 0, 2025-06-30 21:35:21.41451, 2025-06-30 21:35:21.414953, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f5a23c1]
2025-06-30T21:35:21.446+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 激活用户: userId=1939679149587791874
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f5a23c1] from current transaction
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f5a23c1]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f5a23c1] from current transaction
2025-06-30T21:35:21.446+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始更新填充...
2025-06-30T21:35:21.446+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 更新填充完成
==>  Preparing: UPDATE sys_users SET username=?, password_hash=?, email=?, phone=?, real_name=?, status=?, department_id=?, login_count=?, version=?, create_time=?, update_time=?, creator_id=?, updater_id=? WHERE id=? AND version=? AND deleted=0
==> Parameters: statustest(String), $2a$10$h2p44miN2uwT5xaBeNXdte3vCX7r5mtx.6ha3g7aZL99S6plCbet6(String), <EMAIL>(String), 13800139002(String), 状态测试用户(String), ACTIVE(String), 1(Long), 0(Integer), 3(Integer), 2025-06-30T21:35:21.414510(LocalDateTime), 2025-06-30T21:35:21.414953(LocalDateTime), 1(Long), 1(Long), 1939679149587791874(Long), 2(Integer)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f5a23c1]
2025-06-30T21:35:21.447+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户激活成功: userId=1939679149587791874
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f5a23c1] from current transaction
==>  Preparing: SELECT id,username,password_hash AS password,email,phone,real_name,avatar_url,status,department_id,last_login_time,last_login_ip,login_count,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_users WHERE id=? AND deleted=0
==> Parameters: 1939679149587791874(Long)
<==    Columns: ID, USERNAME, PASSWORD, EMAIL, PHONE, REAL_NAME, AVATAR_URL, STATUS, DEPARTMENT_ID, LAST_LOGIN_TIME, LAST_LOGIN_IP, LOGIN_COUNT, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679149587791874, statustest, $2a$10$h2p44miN2uwT5xaBeNXdte3vCX7r5mtx.6ha3g7aZL99S6plCbet6, <EMAIL>, 13800139002, 状态测试用户, null, ACTIVE, 1, null, null, 0, 3, 0, 2025-06-30 21:35:21.41451, 2025-06-30 21:35:21.414953, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f5a23c1]
2025-06-30T21:35:21.448+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 8, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.448+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 9, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.449+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 10, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.449+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 11, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.449+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 12, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.450+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 13, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.450+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 14, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.450+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 15, missCount = 1, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f5a23c1]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f5a23c1]
2025-06-30T21:35:21.451+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testUserStatusOperations]
2025-06-30T21:35:21.451+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [UserServiceIntegrationTest], method [testUserStatusOperations], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:21.451+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
]]></system-out>
    <system-err><![CDATA[Mockito is currently self-attaching to enable the inline-mock-maker. This will no longer work in future releases of the JDK. Please add Mockito as an agent to your build what is described in Mockito's documentation: https://javadoc.io/doc/org.mockito/mockito-core/latest/org/mockito/Mockito.html#0.3
WARNING: A Java agent has been loaded dynamically (/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.15.11/byte-buddy-agent-1.15.11.jar)
WARNING: If a serviceability tool is in use, please run with -XX:+EnableDynamicAgentLoading to hide this warning
WARNING: If a serviceability tool is not in use, please run with -Djdk.instrument.traceUsage for more information
WARNING: Dynamic loading of agents will be disallowed by default in a future release
]]></system-err>
  </testcase>
  <testcase name="testUpdateUser" classname="com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest" time="0.065">
    <system-out><![CDATA[2025-06-30T21:35:21.454+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 16, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.454+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:35:21.454+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:35:21.454+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 17, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.454+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 18, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.455+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [UserServiceIntegrationTest], method [testUpdateUser], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:21.455+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest] and test method [testUpdateUser]
2025-06-30T21:35:21.455+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 19, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.455+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest.testUpdateUser()]
2025-06-30T21:35:21.455+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testUpdateUser]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@52e5ad71]; rollback [true]
2025-06-30T21:35:21.455+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 20, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.455+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 21, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.456+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 22, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.456+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 23, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.456+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 创建用户: updatetest
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@15d04c41]
2025-06-30T21:35:21.515+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:21.515+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
JDBC Connection [HikariProxyConnection@1116746541 wrapping conn0: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: INSERT INTO sys_users ( id, username, password_hash, email, phone, real_name, status, department_id, login_count, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679150007222273(Long), updatetest(String), $2a$10$qy.2ln8rZKo8wMnJw4JTAuzuNsvFXccA0vB4BAie7iS1vypuoj.Mm(String), <EMAIL>(String), 13800138999(String), 更新测试用户(String), ACTIVE(String), 1(Long), 0(Integer), 2025-06-30T21:35:21.515121(LocalDateTime), 2025-06-30T21:35:21.515142(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@15d04c41]
2025-06-30T21:35:21.515+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户创建成功: userId=1939679150007222273
2025-06-30T21:35:21.515+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 更新用户: userId=1939679150007222273
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@15d04c41] from current transaction
==>  Preparing: SELECT id,username,password_hash AS password,email,phone,real_name,avatar_url,status,department_id,last_login_time,last_login_ip,login_count,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_users WHERE id=? AND deleted=0
==> Parameters: 1939679150007222273(Long)
<==    Columns: ID, USERNAME, PASSWORD, EMAIL, PHONE, REAL_NAME, AVATAR_URL, STATUS, DEPARTMENT_ID, LAST_LOGIN_TIME, LAST_LOGIN_IP, LOGIN_COUNT, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679150007222273, updatetest, $2a$10$qy.2ln8rZKo8wMnJw4JTAuzuNsvFXccA0vB4BAie7iS1vypuoj.Mm, <EMAIL>, 13800138999, 更新测试用户, null, ACTIVE, 1, null, null, 0, 0, 0, 2025-06-30 21:35:21.515121, 2025-06-30 21:35:21.515142, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@15d04c41]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@15d04c41] from current transaction
2025-06-30T21:35:21.517+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始更新填充...
2025-06-30T21:35:21.517+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 更新填充完成
==>  Preparing: UPDATE sys_users SET username=?, password_hash=?, email=?, phone=?, real_name=?, department_id=?, login_count=?, create_time=?, update_time=?, creator_id=?, updater_id=? WHERE id=? AND deleted=0
==> Parameters: updatetest(String), $2a$10$qy.2ln8rZKo8wMnJw4JTAuzuNsvFXccA0vB4BAie7iS1vypuoj.Mm(String), <EMAIL>(String), 13800139000(String), 已更新用户(String), 2(Long), 0(Integer), 2025-06-30T21:35:21.515121(LocalDateTime), 2025-06-30T21:35:21.515142(LocalDateTime), 1(Long), 1(Long), 1939679150007222273(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@15d04c41]
2025-06-30T21:35:21.517+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户更新成功: userId=1939679150007222273
2025-06-30T21:35:21.518+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 24, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.518+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 25, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.518+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 26, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.518+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 27, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.518+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 28, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.519+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 29, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.519+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 30, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.519+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 31, missCount = 1, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@15d04c41]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@15d04c41]
2025-06-30T21:35:21.519+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testUpdateUser]
2025-06-30T21:35:21.519+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [UserServiceIntegrationTest], method [testUpdateUser], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:21.519+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
]]></system-out>
  </testcase>
  <testcase name="testUniqueConstraints" classname="com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest" time="0.072">
    <system-out><![CDATA[2025-06-30T21:35:21.520+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 32, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.520+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:35:21.520+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:35:21.520+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 33, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.520+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 34, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.520+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [UserServiceIntegrationTest], method [testUniqueConstraints], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:21.520+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest] and test method [testUniqueConstraints]
2025-06-30T21:35:21.520+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 35, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.520+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest.testUniqueConstraints()]
2025-06-30T21:35:21.521+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testUniqueConstraints]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@52e5ad71]; rollback [true]
2025-06-30T21:35:21.521+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 36, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.521+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 37, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.521+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 38, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.521+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 39, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.521+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 创建用户: uniquetest
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@63a833c5]
2025-06-30T21:35:21.580+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:21.580+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
JDBC Connection [HikariProxyConnection@1333479763 wrapping conn0: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: INSERT INTO sys_users ( id, username, password_hash, email, phone, real_name, status, department_id, login_count, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679150284046337(Long), uniquetest(String), $2a$10$AnpC2Z2W.3Fy.7copBm2rONUcusprJfG5/lEgcaNsyLMvpxPL.Q.W(String), <EMAIL>(String), 13800139010(String), 唯一性测试用户(String), ACTIVE(String), 1(Long), 0(Integer), 2025-06-30T21:35:21.580704(LocalDateTime), 2025-06-30T21:35:21.580739(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@63a833c5]
2025-06-30T21:35:21.581+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户创建成功: userId=1939679150284046337
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@63a833c5] from current transaction
==>  Preparing: SELECT COUNT( * ) AS total FROM sys_users WHERE deleted=0 AND (username = ?)
==> Parameters: uniquetest(String)
<==    Columns: TOTAL
<==        Row: 1
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@63a833c5]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@63a833c5] from current transaction
==>  Preparing: SELECT COUNT( * ) AS total FROM sys_users WHERE deleted=0 AND (username = ?)
==> Parameters: nonexistent(String)
<==    Columns: TOTAL
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@63a833c5]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@63a833c5] from current transaction
==>  Preparing: SELECT COUNT( * ) AS total FROM sys_users WHERE deleted=0 AND (email = ?)
==> Parameters: <EMAIL>(String)
<==    Columns: TOTAL
<==        Row: 1
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@63a833c5]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@63a833c5] from current transaction
==>  Preparing: SELECT COUNT( * ) AS total FROM sys_users WHERE deleted=0 AND (email = ?)
==> Parameters: <EMAIL>(String)
<==    Columns: TOTAL
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@63a833c5]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@63a833c5] from current transaction
==>  Preparing: SELECT COUNT( * ) AS total FROM sys_users WHERE deleted=0 AND (phone = ?)
==> Parameters: 13800139010(String)
<==    Columns: TOTAL
<==        Row: 1
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@63a833c5]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@63a833c5] from current transaction
==>  Preparing: SELECT COUNT( * ) AS total FROM sys_users WHERE deleted=0 AND (phone = ?)
==> Parameters: 13800139999(String)
<==    Columns: TOTAL
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@63a833c5]
2025-06-30T21:35:21.591+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 40, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.591+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 41, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.591+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 42, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.591+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 43, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.591+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 44, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.591+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 45, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.592+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 46, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.592+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 47, missCount = 1, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@63a833c5]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@63a833c5]
2025-06-30T21:35:21.592+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testUniqueConstraints]
2025-06-30T21:35:21.592+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [UserServiceIntegrationTest], method [testUniqueConstraints], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:21.592+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
]]></system-out>
  </testcase>
  <testcase name="testChangePassword" classname="com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest" time="0.299">
    <system-out><![CDATA[2025-06-30T21:35:21.592+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 48, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.592+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:35:21.593+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:35:21.593+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 49, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.593+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 50, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.593+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [UserServiceIntegrationTest], method [testChangePassword], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:21.593+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest] and test method [testChangePassword]
2025-06-30T21:35:21.593+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 51, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.593+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest.testChangePassword()]
2025-06-30T21:35:21.593+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testChangePassword]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@52e5ad71]; rollback [true]
2025-06-30T21:35:21.594+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 52, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.594+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 53, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.594+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 54, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.594+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 55, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.594+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 创建用户: passwordtest
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@693676d]
2025-06-30T21:35:21.653+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:21.653+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
JDBC Connection [HikariProxyConnection@1437069487 wrapping conn0: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: INSERT INTO sys_users ( id, username, password_hash, email, phone, real_name, status, department_id, login_count, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679150590230530(Long), passwordtest(String), $2a$10$1zq6kkPjsVteLMOebndgDOYFyt6XzES7.RyOO1s9OYNGrxCuQ6Hf2(String), <EMAIL>(String), 13800139001(String), 密码测试用户(String), ACTIVE(String), 1(Long), 0(Integer), 2025-06-30T21:35:21.653779(LocalDateTime), 2025-06-30T21:35:21.653805(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@693676d]
2025-06-30T21:35:21.654+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户创建成功: userId=1939679150590230530
2025-06-30T21:35:21.654+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 修改密码: userId=1939679150590230530
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@693676d] from current transaction
==>  Preparing: SELECT id,username,password_hash AS password,email,phone,real_name,avatar_url,status,department_id,last_login_time,last_login_ip,login_count,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_users WHERE id=? AND deleted=0
==> Parameters: 1939679150590230530(Long)
<==    Columns: ID, USERNAME, PASSWORD, EMAIL, PHONE, REAL_NAME, AVATAR_URL, STATUS, DEPARTMENT_ID, LAST_LOGIN_TIME, LAST_LOGIN_IP, LOGIN_COUNT, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679150590230530, passwordtest, $2a$10$1zq6kkPjsVteLMOebndgDOYFyt6XzES7.RyOO1s9OYNGrxCuQ6Hf2, <EMAIL>, 13800139001, 密码测试用户, null, ACTIVE, 1, null, null, 0, 0, 0, 2025-06-30 21:35:21.653779, 2025-06-30 21:35:21.653805, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@693676d]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@693676d] from current transaction
2025-06-30T21:35:21.772+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始更新填充...
2025-06-30T21:35:21.772+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 更新填充完成
==>  Preparing: UPDATE sys_users SET username=?, password_hash=?, email=?, phone=?, real_name=?, status=?, department_id=?, login_count=?, version=?, create_time=?, update_time=?, creator_id=?, updater_id=? WHERE id=? AND version=? AND deleted=0
==> Parameters: passwordtest(String), $2a$10$/dEnt2Wdt24Q73ZeyB1D5uQLQq03ix7zzKMoAufZ6jU3DrLo.bvyW(String), <EMAIL>(String), 13800139001(String), 密码测试用户(String), ACTIVE(String), 1(Long), 0(Integer), 1(Integer), 2025-06-30T21:35:21.653779(LocalDateTime), 2025-06-30T21:35:21.653805(LocalDateTime), 1(Long), 1(Long), 1939679150590230530(Long), 0(Integer)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@693676d]
2025-06-30T21:35:21.773+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 密码修改成功: userId=1939679150590230530
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@693676d] from current transaction
==>  Preparing: SELECT id,username,password_hash AS password,email,phone,real_name,avatar_url,status,department_id,last_login_time,last_login_ip,login_count,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_users WHERE id=? AND deleted=0
==> Parameters: 1939679150590230530(Long)
<==    Columns: ID, USERNAME, PASSWORD, EMAIL, PHONE, REAL_NAME, AVATAR_URL, STATUS, DEPARTMENT_ID, LAST_LOGIN_TIME, LAST_LOGIN_IP, LOGIN_COUNT, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679150590230530, passwordtest, $2a$10$/dEnt2Wdt24Q73ZeyB1D5uQLQq03ix7zzKMoAufZ6jU3DrLo.bvyW, <EMAIL>, 13800139001, 密码测试用户, null, ACTIVE, 1, null, null, 0, 1, 0, 2025-06-30 21:35:21.653779, 2025-06-30 21:35:21.653805, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@693676d]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@693676d] from current transaction
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@693676d]
2025-06-30T21:35:21.891+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 56, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.891+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 57, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.891+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 58, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.891+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 59, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.891+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 60, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.892+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 61, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.892+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 62, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.892+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 63, missCount = 1, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@693676d]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@693676d]
2025-06-30T21:35:21.892+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testChangePassword]
2025-06-30T21:35:21.892+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [UserServiceIntegrationTest], method [testChangePassword], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:21.892+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
]]></system-out>
  </testcase>
  <testcase name="testBatchOperations" classname="com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest" time="0.131">
    <system-out><![CDATA[2025-06-30T21:35:21.892+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 64, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.892+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:35:21.893+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:35:21.893+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 65, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.893+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 66, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.893+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [UserServiceIntegrationTest], method [testBatchOperations], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:21.893+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest] and test method [testBatchOperations]
2025-06-30T21:35:21.893+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 67, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.893+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest.testBatchOperations()]
2025-06-30T21:35:21.893+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testBatchOperations]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@52e5ad71]; rollback [true]
2025-06-30T21:35:21.893+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 68, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.893+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 69, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.894+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 70, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.894+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 71, missCount = 1, failureCount = 0]
2025-06-30T21:35:21.894+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 创建用户: batchtest1
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4efb7c99]
2025-06-30T21:35:21.953+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:21.953+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
JDBC Connection [HikariProxyConnection@34222360 wrapping conn0: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: INSERT INTO sys_users ( id, username, password_hash, email, phone, real_name, status, department_id, login_count, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679151848521729(Long), batchtest1(String), $2a$10$ldfZkCd5/Z3cMebsFH5.aevlNn6.vKYvEG0UVqPs3PbrF3fBl2v8W(String), <EMAIL>(String), 13800139008(String), 批量测试用户1(String), ACTIVE(String), 1(Long), 0(Integer), 2025-06-30T21:35:21.953705(LocalDateTime), 2025-06-30T21:35:21.953744(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4efb7c99]
2025-06-30T21:35:21.954+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户创建成功: userId=1939679151848521729
2025-06-30T21:35:21.954+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 创建用户: batchtest2
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4efb7c99] from current transaction
2025-06-30T21:35:22.013+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:22.013+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_users ( id, username, password_hash, email, phone, real_name, status, department_id, login_count, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679152095985666(Long), batchtest2(String), $2a$10$ea6FA7lGtTlVH9QsUzLbKul4KJIiddNgjB6Dsw.wf29UE4118GuXW(String), <EMAIL>(String), 13800139009(String), 批量测试用户2(String), ACTIVE(String), 1(Long), 0(Integer), 2025-06-30T21:35:22.013326(LocalDateTime), 2025-06-30T21:35:22.013346(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4efb7c99]
2025-06-30T21:35:22.013+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户创建成功: userId=1939679152095985666
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4efb7c99] from current transaction
==>  Preparing: UPDATE sys_users SET status = ?, updater_id = ?, update_time = NOW() WHERE id IN ( ? , ? ) AND deleted = 0
==> Parameters: INACTIVE(String), 1(Long), 1939679151848521729(Long), 1939679152095985666(Long)
<==    Updates: 2
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4efb7c99]
2025-06-30T21:35:22.015+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 批量更新用户状态成功: count=2, status=INACTIVE
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4efb7c99] from current transaction
==>  Preparing: SELECT id,username,password_hash AS password,email,phone,real_name,avatar_url,status,department_id,last_login_time,last_login_ip,login_count,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_users WHERE id=? AND deleted=0
==> Parameters: 1939679151848521729(Long)
<==    Columns: ID, USERNAME, PASSWORD, EMAIL, PHONE, REAL_NAME, AVATAR_URL, STATUS, DEPARTMENT_ID, LAST_LOGIN_TIME, LAST_LOGIN_IP, LOGIN_COUNT, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679151848521729, batchtest1, $2a$10$ldfZkCd5/Z3cMebsFH5.aevlNn6.vKYvEG0UVqPs3PbrF3fBl2v8W, <EMAIL>, 13800139008, 批量测试用户1, null, INACTIVE, 1, null, null, 0, 0, 0, 2025-06-30 21:35:21.953705, 2025-06-30 21:35:22.015479, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4efb7c99]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4efb7c99] from current transaction
==>  Preparing: SELECT id,username,password_hash AS password,email,phone,real_name,avatar_url,status,department_id,last_login_time,last_login_ip,login_count,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_users WHERE id=? AND deleted=0
==> Parameters: 1939679152095985666(Long)
<==    Columns: ID, USERNAME, PASSWORD, EMAIL, PHONE, REAL_NAME, AVATAR_URL, STATUS, DEPARTMENT_ID, LAST_LOGIN_TIME, LAST_LOGIN_IP, LOGIN_COUNT, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679152095985666, batchtest2, $2a$10$ea6FA7lGtTlVH9QsUzLbKul4KJIiddNgjB6Dsw.wf29UE4118GuXW, <EMAIL>, 13800139009, 批量测试用户2, null, INACTIVE, 1, null, null, 0, 0, 0, 2025-06-30 21:35:22.013326, 2025-06-30 21:35:22.015479, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4efb7c99]
2025-06-30T21:35:22.017+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 批量删除用户: userIds=[1939679151848521729, 1939679152095985666]
Load compatibleSet: com.baomidou.mybatisplus.extension.spi.SpringCompatibleSet@192f807
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4efb7c99] from current transaction
2025-06-30T21:35:22.020+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始更新填充...
2025-06-30T21:35:22.020+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 更新填充完成
==>  Preparing: UPDATE sys_users SET update_time=?,updater_id=?, deleted=1 WHERE id IN ( ? , ? ) AND deleted=0
==> Parameters: 2025-06-30T21:35:22.020891(LocalDateTime), 1(Long), 1939679151848521729(Long), 1939679152095985666(Long)
<==    Updates: 2
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4efb7c99]
2025-06-30T21:35:22.021+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 批量删除用户成功: count=2
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4efb7c99] from current transaction
==>  Preparing: SELECT id,username,password_hash AS password,email,phone,real_name,avatar_url,status,department_id,last_login_time,last_login_ip,login_count,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_users WHERE id=? AND deleted=0
==> Parameters: 1939679151848521729(Long)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4efb7c99]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4efb7c99] from current transaction
==>  Preparing: SELECT id,username,password_hash AS password,email,phone,real_name,avatar_url,status,department_id,last_login_time,last_login_ip,login_count,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_users WHERE id=? AND deleted=0
==> Parameters: 1939679152095985666(Long)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4efb7c99]
2025-06-30T21:35:22.023+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 72, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.023+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 73, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.023+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 74, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.023+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 75, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.023+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 76, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.023+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 77, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.023+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 78, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.024+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 79, missCount = 1, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4efb7c99]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4efb7c99]
2025-06-30T21:35:22.024+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testBatchOperations]
2025-06-30T21:35:22.024+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [UserServiceIntegrationTest], method [testBatchOperations], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:22.024+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
]]></system-out>
  </testcase>
  <testcase name="testUserQuery" classname="com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest" time="0.162">
    <system-out><![CDATA[2025-06-30T21:35:22.024+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 80, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.024+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:35:22.024+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:35:22.024+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 81, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.025+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 82, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.025+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [UserServiceIntegrationTest], method [testUserQuery], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:22.025+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest] and test method [testUserQuery]
2025-06-30T21:35:22.025+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 83, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.025+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest.testUserQuery()]
2025-06-30T21:35:22.025+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testUserQuery]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@52e5ad71]; rollback [true]
2025-06-30T21:35:22.025+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 84, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.025+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 85, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.025+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 86, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.026+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 87, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.026+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 创建用户: querytest1
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@53425450]
2025-06-30T21:35:22.083+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:22.083+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
JDBC Connection [HikariProxyConnection@1914888552 wrapping conn0: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: INSERT INTO sys_users ( id, username, password_hash, email, phone, real_name, status, department_id, login_count, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679152393781249(Long), querytest1(String), $2a$10$U7TfukjIkqeJD6mjPKHcYO60PtQePkIMC.F0laUIalYsShWZBN2Nq(String), <EMAIL>(String), 13800139003(String), 查询测试用户1(String), ACTIVE(String), 1(Long), 0(Integer), 2025-06-30T21:35:22.083759(LocalDateTime), 2025-06-30T21:35:22.083778(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@53425450]
2025-06-30T21:35:22.084+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户创建成功: userId=1939679152393781249
2025-06-30T21:35:22.084+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 创建用户: querytest2
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@53425450] from current transaction
2025-06-30T21:35:22.143+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:22.143+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_users ( id, username, password_hash, email, phone, real_name, status, department_id, login_count, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679152641245186(Long), querytest2(String), $2a$10$5dVTTa.SCwTEtGm/r1hlNODL4JfNyR9H7HRNZPQ.u7yzvUKcY5pUi(String), <EMAIL>(String), 13800139004(String), 查询测试用户2(String), ACTIVE(String), 2(Long), 0(Integer), 2025-06-30T21:35:22.143412(LocalDateTime), 2025-06-30T21:35:22.143431(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@53425450]
2025-06-30T21:35:22.143+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户创建成功: userId=1939679152641245186
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@53425450] from current transaction
==>  Preparing: SELECT COUNT(*) AS total FROM sys_users WHERE deleted = 0 AND (username LIKE ?)
==> Parameters: %querytest%(String)
<==    Columns: TOTAL
<==        Row: 2
<==      Total: 1
==>  Preparing: SELECT id,username,password_hash AS password,email,phone,real_name,avatar_url,status,department_id,last_login_time,last_login_ip,login_count,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_users WHERE deleted=0 AND (username LIKE ?) ORDER BY create_time DESC LIMIT ?
==> Parameters: %querytest%(String), 10(Long)
<==    Columns: ID, USERNAME, PASSWORD, EMAIL, PHONE, REAL_NAME, AVATAR_URL, STATUS, DEPARTMENT_ID, LAST_LOGIN_TIME, LAST_LOGIN_IP, LOGIN_COUNT, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679152641245186, querytest2, $2a$10$5dVTTa.SCwTEtGm/r1hlNODL4JfNyR9H7HRNZPQ.u7yzvUKcY5pUi, <EMAIL>, 13800139004, 查询测试用户2, null, ACTIVE, 2, null, null, 0, 0, 0, 2025-06-30 21:35:22.143412, 2025-06-30 21:35:22.143431, 1, 1, <<BLOB>>, null
<==        Row: 1939679152393781249, querytest1, $2a$10$U7TfukjIkqeJD6mjPKHcYO60PtQePkIMC.F0laUIalYsShWZBN2Nq, <EMAIL>, 13800139003, 查询测试用户1, null, ACTIVE, 1, null, null, 0, 0, 0, 2025-06-30 21:35:22.083759, 2025-06-30 21:35:22.083778, 1, 1, <<BLOB>>, null
<==      Total: 2
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@53425450]
2025-06-30T21:35:22.185+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 88, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.185+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 89, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.186+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 90, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.186+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 91, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.186+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 92, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.186+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 93, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.186+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 94, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.186+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 95, missCount = 1, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@53425450]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@53425450]
2025-06-30T21:35:22.187+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testUserQuery]
2025-06-30T21:35:22.187+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [UserServiceIntegrationTest], method [testUserQuery], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:22.187+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
]]></system-out>
  </testcase>
  <testcase name="testGetUsersByDepartment" classname="com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest" time="0.184">
    <system-out><![CDATA[2025-06-30T21:35:22.187+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 96, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.187+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:35:22.187+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:35:22.187+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 97, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.187+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 98, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.187+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [UserServiceIntegrationTest], method [testGetUsersByDepartment], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:22.187+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest] and test method [testGetUsersByDepartment]
2025-06-30T21:35:22.187+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 99, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.187+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest.testGetUsersByDepartment()]
2025-06-30T21:35:22.187+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testGetUsersByDepartment]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@52e5ad71]; rollback [true]
2025-06-30T21:35:22.187+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 100, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.188+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 101, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.188+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 102, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.188+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 103, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.188+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 创建用户: depttest1
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@221383c9]
2025-06-30T21:35:22.248+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:22.248+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
JDBC Connection [HikariProxyConnection@1352794956 wrapping conn0: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: INSERT INTO sys_users ( id, username, password_hash, email, phone, real_name, status, department_id, login_count, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679153081647105(Long), depttest1(String), $2a$10$MthfOevQsouijZfYkMkprOkrb.assvzLMIzTo2vMBfNiHC6KikJAO(String), <EMAIL>(String), 13800139005(String), 部门测试用户1(String), ACTIVE(String), 1(Long), 0(Integer), 2025-06-30T21:35:22.248099(LocalDateTime), 2025-06-30T21:35:22.248118(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@221383c9]
2025-06-30T21:35:22.248+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户创建成功: userId=1939679153081647105
2025-06-30T21:35:22.248+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 创建用户: depttest2
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@221383c9] from current transaction
2025-06-30T21:35:22.307+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:22.307+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_users ( id, username, password_hash, email, phone, real_name, status, department_id, login_count, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679153333305345(Long), depttest2(String), $2a$10$wsE0oRjJbig5Nb3SFREKneQwk4YQvbyKOXap8..rWzpQhH2FwJszy(String), <EMAIL>(String), 13800139006(String), 部门测试用户2(String), ACTIVE(String), 1(Long), 0(Integer), 2025-06-30T21:35:22.307747(LocalDateTime), 2025-06-30T21:35:22.307769(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@221383c9]
2025-06-30T21:35:22.308+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户创建成功: userId=1939679153333305345
2025-06-30T21:35:22.308+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 创建用户: depttest3
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@221383c9] from current transaction
2025-06-30T21:35:22.366+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:22.366+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_users ( id, username, password_hash, email, phone, real_name, status, department_id, login_count, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679153580769281(Long), depttest3(String), $2a$10$4q90k98UHD.fiViT9oKHmegByNWKMcV7iqX7X55N167DmiHYQX/x6(String), <EMAIL>(String), 13800139007(String), 部门测试用户3(String), ACTIVE(String), 2(Long), 0(Integer), 2025-06-30T21:35:22.366950(LocalDateTime), 2025-06-30T21:35:22.366970(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@221383c9]
2025-06-30T21:35:22.367+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户创建成功: userId=1939679153580769281
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@221383c9] from current transaction
==>  Preparing: SELECT id,username,password_hash AS password,email,phone,real_name,avatar_url,status,department_id,last_login_time,last_login_ip,login_count,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_users WHERE deleted=0 AND (department_id = ?) ORDER BY create_time DESC
==> Parameters: 1(Long)
<==    Columns: ID, USERNAME, PASSWORD, EMAIL, PHONE, REAL_NAME, AVATAR_URL, STATUS, DEPARTMENT_ID, LAST_LOGIN_TIME, LAST_LOGIN_IP, LOGIN_COUNT, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679153333305345, depttest2, $2a$10$wsE0oRjJbig5Nb3SFREKneQwk4YQvbyKOXap8..rWzpQhH2FwJszy, <EMAIL>, 13800139006, 部门测试用户2, null, ACTIVE, 1, null, null, 0, 0, 0, 2025-06-30 21:35:22.307747, 2025-06-30 21:35:22.307769, 1, 1, <<BLOB>>, null
<==        Row: 1939679153081647105, depttest1, $2a$10$MthfOevQsouijZfYkMkprOkrb.assvzLMIzTo2vMBfNiHC6KikJAO, <EMAIL>, 13800139005, 部门测试用户1, null, ACTIVE, 1, null, null, 0, 0, 0, 2025-06-30 21:35:22.248099, 2025-06-30 21:35:22.248118, 1, 1, <<BLOB>>, null
<==      Total: 2
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@221383c9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@221383c9] from current transaction
==>  Preparing: SELECT id,username,password_hash AS password,email,phone,real_name,avatar_url,status,department_id,last_login_time,last_login_ip,login_count,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_users WHERE deleted=0 AND (department_id = ?) ORDER BY create_time DESC
==> Parameters: 2(Long)
<==    Columns: ID, USERNAME, PASSWORD, EMAIL, PHONE, REAL_NAME, AVATAR_URL, STATUS, DEPARTMENT_ID, LAST_LOGIN_TIME, LAST_LOGIN_IP, LOGIN_COUNT, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679153580769281, depttest3, $2a$10$4q90k98UHD.fiViT9oKHmegByNWKMcV7iqX7X55N167DmiHYQX/x6, <EMAIL>, 13800139007, 部门测试用户3, null, ACTIVE, 2, null, null, 0, 0, 0, 2025-06-30 21:35:22.36695, 2025-06-30 21:35:22.36697, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@221383c9]
2025-06-30T21:35:22.369+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 104, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.369+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 105, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.370+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 106, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.370+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 107, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.370+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 108, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.370+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 109, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.370+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 110, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.370+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 111, missCount = 1, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@221383c9]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@221383c9]
2025-06-30T21:35:22.371+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testGetUsersByDepartment]
2025-06-30T21:35:22.371+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [UserServiceIntegrationTest], method [testGetUsersByDepartment], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:22.371+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
]]></system-out>
  </testcase>
  <testcase name="testCreateAndGetUser" classname="com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest" time="0.064">
    <system-out><![CDATA[2025-06-30T21:35:22.371+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 112, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.371+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:35:22.371+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
2025-06-30T21:35:22.371+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 113, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.371+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 114, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.371+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [UserServiceIntegrationTest], method [testCreateAndGetUser], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:22.372+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest] and test method [testCreateAndGetUser]
2025-06-30T21:35:22.372+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 115, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.372+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest.testCreateAndGetUser()]
2025-06-30T21:35:22.372+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testCreateAndGetUser]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@52e5ad71]; rollback [true]
2025-06-30T21:35:22.372+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 116, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.372+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 117, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.372+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 118, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.372+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 119, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.373+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 创建用户: integrationtest
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7508912e]
2025-06-30T21:35:22.432+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:22.432+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
JDBC Connection [HikariProxyConnection@1334234448 wrapping conn0: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: INSERT INTO sys_users ( id, username, password_hash, email, phone, real_name, status, department_id, login_count, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679153857593345(Long), integrationtest(String), $2a$10$pwyhU5xFM.NhVeHC914mhu2prWGDrGvoRlFDEi2PSm1CU5VEcQeWS(String), <EMAIL>(String), 13800138888(String), 集成测试用户(String), ACTIVE(String), 1(Long), 0(Integer), 2025-06-30T21:35:22.432595(LocalDateTime), 2025-06-30T21:35:22.432614(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7508912e]
2025-06-30T21:35:22.433+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.service.impl.UserServiceImpl   : 用户创建成功: userId=1939679153857593345
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7508912e] from current transaction
==>  Preparing: SELECT id,username,password_hash AS password,email,phone,real_name,avatar_url,status,department_id,last_login_time,last_login_ip,login_count,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_users WHERE id=? AND deleted=0
==> Parameters: 1939679153857593345(Long)
<==    Columns: ID, USERNAME, PASSWORD, EMAIL, PHONE, REAL_NAME, AVATAR_URL, STATUS, DEPARTMENT_ID, LAST_LOGIN_TIME, LAST_LOGIN_IP, LOGIN_COUNT, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679153857593345, integrationtest, $2a$10$pwyhU5xFM.NhVeHC914mhu2prWGDrGvoRlFDEi2PSm1CU5VEcQeWS, <EMAIL>, 13800138888, 集成测试用户, null, ACTIVE, 1, null, null, 0, 0, 0, 2025-06-30 21:35:22.432595, 2025-06-30 21:35:22.432614, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7508912e]
2025-06-30T21:35:22.433+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 120, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.434+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 121, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.434+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 122, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.434+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 123, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.434+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 124, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.434+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 125, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.434+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 126, missCount = 1, failureCount = 0]
2025-06-30T21:35:22.434+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 1, maxSize = 32, parentContextCount = 0, hitCount = 127, missCount = 1, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7508912e]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7508912e]
2025-06-30T21:35:22.434+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest]; test method [testCreateAndGetUser]
2025-06-30T21:35:22.435+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [UserServiceIntegrationTest], method [testCreateAndGetUser], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:22.435+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.integration.UserServiceIntegrationTest
]]></system-out>
  </testcase>
</testsuite>