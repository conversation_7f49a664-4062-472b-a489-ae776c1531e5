<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest" time="0.558" tests="16" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="21"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/target/test-classes:/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.4.7/spring-boot-starter-web-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.4.7/spring-boot-starter-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.4.7/spring-boot-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.4.7/spring-boot-starter-logging-3.4.7.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.5.18/logback-classic-1.5.18.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.4.7/spring-boot-starter-json-3.4.7.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.18.4/jackson-datatype-jdk8-2.18.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.18.4/jackson-module-parameter-names-2.18.4.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.2.8/spring-web-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.2.8/spring-beans-6.2.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.14.8/micrometer-observation-1.14.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.14.8/micrometer-commons-1.14.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.2.8/spring-webmvc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.2.8/spring-context-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.2.8/spring-expression-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-undertow/3.4.7/spring-boot-starter-undertow-3.4.7.jar:/Users/<USER>/.m2/repository/io/undertow/undertow-core/2.3.18.Final/undertow-core-2.3.18.Final.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.6.1.Final/jboss-logging-3.6.1.Final.jar:/Users/<USER>/.m2/repository/org/jboss/xnio/xnio-api/3.8.16.Final/xnio-api-3.8.16.Final.jar:/Users/<USER>/.m2/repository/org/wildfly/common/wildfly-common/1.5.4.Final/wildfly-common-1.5.4.Final.jar:/Users/<USER>/.m2/repository/org/wildfly/client/wildfly-client-config/1.0.1.Final/wildfly-client-config-1.0.1.Final.jar:/Users/<USER>/.m2/repository/org/jboss/xnio/xnio-nio/3.8.16.Final/xnio-nio-3.8.16.Final.jar:/Users/<USER>/.m2/repository/org/jboss/threads/jboss-threads/3.5.0.Final/jboss-threads-3.5.0.Final.jar:/Users/<USER>/.m2/repository/io/undertow/undertow-servlet/2.3.18.Final/undertow-servlet-2.3.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/servlet/jakarta.servlet-api/6.0.0/jakarta.servlet-api-6.0.0.jar:/Users/<USER>/.m2/repository/io/undertow/undertow-websockets-jsr/2.3.18.Final/undertow-websockets-jsr-2.3.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/websocket/jakarta.websocket-api/2.1.1/jakarta.websocket-api-2.1.1.jar:/Users/<USER>/.m2/repository/jakarta/websocket/jakarta.websocket-client-api/2.1.1/jakarta.websocket-client-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.42/tomcat-embed-el-10.1.42.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.4.7/spring-boot-starter-validation-3.4.7.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.2.Final/hibernate-validator-8.0.2.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.7.0/classmate-1.7.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.4.7/spring-boot-starter-security-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.2.8/spring-aop-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.4.7/spring-security-config-6.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.4.7/spring-security-web-6.4.7.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-discovery/2023.0.3.3/spring-cloud-starter-alibaba-nacos-discovery-2023.0.3.3.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-alibaba-commons/2023.0.3.3/spring-cloud-alibaba-commons-2023.0.3.3.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.4.2/nacos-client-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-auth-plugin/2.4.2/nacos-auth-plugin-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-encryption-plugin/2.4.2/nacos-encryption-plugin-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-logback-adapter-12/2.4.2/nacos-logback-adapter-12-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/logback-adapter/1.1.3/logback-adapter-1.1.3.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-log4j2-adapter/2.4.2/nacos-log4j2-adapter-2.4.2.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.17.2/commons-codec-1.17.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.18.4.1/jackson-core-2.18.4.1.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpasyncclient/4.1.5/httpasyncclient-4.1.5.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore-nio/4.4.16/httpcore-nio-4.4.16.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient/0.16.0/simpleclient-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_otel/0.16.0/simpleclient_tracer_otel-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_common/0.16.0/simpleclient_tracer_common-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_otel_agent/0.16.0/simpleclient_tracer_otel_agent-0.16.0.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.14.8/micrometer-core-1.14.8.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.2.2/HdrHistogram-2.2.2.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-commons/4.2.1/spring-cloud-commons-4.2.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.4.7/spring-security-crypto-6.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-context/4.2.1/spring-cloud-context-4.2.1.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-config/2023.0.3.3/spring-cloud-starter-alibaba-nacos-config-2023.0.3.3.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-alibaba-nacos-config/2023.0.3.3/spring-alibaba-nacos-config-2023.0.3.3.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jdbc/3.4.7/spring-boot-starter-data-jdbc-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.4.7/spring-boot-starter-jdbc-3.4.7.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/5.1.0/HikariCP-5.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.2.8/spring-jdbc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jdbc/3.4.7/spring-data-jdbc-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-relational/3.4.7/spring-data-relational-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.4.7/spring-data-commons-3.4.7.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter/3.5.12/mybatis-plus-spring-boot3-starter-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.12/mybatis-plus-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.12/mybatis-plus-core-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.12/mybatis-plus-annotation-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring/3.5.12/mybatis-plus-spring-3.5.12.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.19/mybatis-3.5.19.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/3.0.4/mybatis-spring-3.0.4.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.12/mybatis-plus-spring-boot-autoconfigure-3.5.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.4.7/spring-boot-autoconfigure-3.4.7.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-jsqlparser/3.5.12/mybatis-plus-jsqlparser-3.5.12.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/5.1/jsqlparser-5.1.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-jsqlparser-common/3.5.12/mybatis-plus-jsqlparser-common-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.12/mybatis-plus-extension-3.5.12.jar:/Users/<USER>/.m2/repository/org/postgresql/postgresql/42.7.7/postgresql-42.7.7.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.49.3/checker-qual-3.49.3.jar:/Users/<USER>/.m2/repository/org/liquibase/liquibase-core/4.29.2/liquibase-core-4.29.2.jar:/Users/<USER>/.m2/repository/com/opencsv/opencsv/5.9/opencsv-5.9.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.3/snakeyaml-2.3.jar:/Users/<USER>/.m2/repository/javax/xml/bind/jaxb-api/2.3.1/jaxb-api-2.3.1.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.17.0/commons-io-2.17.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.5.0/commons-collections4-4.5.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.13.1/commons-text-1.13.1.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar:/Users/<USER>/.m2/repository/com/bdyl/erp/pisp/pisp-common-core/1.0.0-SNAPSHOT/pisp-common-core-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.38/hutool-all-5.8.38.jar:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.6.3/mapstruct-1.6.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.18.4/jackson-databind-2.18.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.18.4/jackson-annotations-2.18.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.18.4/jackson-datatype-jsr310-2.18.4.jar:/Users/<USER>/.m2/repository/com/bdyl/erp/pisp/pisp-common-security/1.0.0-SNAPSHOT/pisp-common-security-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.12.6/jjwt-api-0.12.6.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.12.6/jjwt-impl-0.12.6.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.12.6/jjwt-jackson-0.12.6.jar:/Users/<USER>/.m2/repository/com/bdyl/erp/pisp/pisp-common-web/1.0.0-SNAPSHOT/pisp-common-web-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/com/bdyl/erp/pisp/pisp-common-redis/1.0.0-SNAPSHOT/pisp-common-redis-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/3.4.7/spring-boot-starter-data-redis-3.4.7.jar:/Users/<USER>/.m2/repository/io/lettuce/lettuce-core/6.4.2.RELEASE/lettuce-core-6.4.2.RELEASE.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.122.Final/netty-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.122.Final/netty-handler-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.122.Final/netty-resolver-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.122.Final/netty-buffer-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.122.Final/netty-transport-native-unix-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.122.Final/netty-codec-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.122.Final/netty-transport-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.7.7/reactor-core-3.7.7.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/3.4.7/spring-data-redis-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/3.4.7/spring-data-keyvalue-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.2.8/spring-oxm-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.2.8/spring-context-support-6.2.8.jar:/Users/<USER>/.m2/repository/com/bdyl/erp/pisp/pisp-api/1.0.0-SNAPSHOT/pisp-api-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.4.7/spring-boot-starter-test-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.4.7/spring-boot-test-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.4.7/spring-boot-test-autoconfigure-3.4.7.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/9.7.1/asm-9.7.1.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.26.3/assertj-core-3.26.3.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.15.11/byte-buddy-1.15.11.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.11.4/junit-jupiter-5.11.4.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.11.4/junit-jupiter-api-5.11.4.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.11.4/junit-platform-commons-1.11.4.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.11.4/junit-jupiter-params-5.11.4.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.11.4/junit-jupiter-engine-5.11.4.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.11.4/junit-platform-engine-1.11.4.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.14.2/mockito-core-5.14.2.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.15.11/byte-buddy-agent-1.15.11.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.14.2/mockito-junit-jupiter-5.14.2.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.2.8/spring-core-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.2.8/spring-jcl-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.2.8/spring-test-6.2.8.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.10.2/xmlunit-core-2.10.2.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-test/6.4.7/spring-security-test-6.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.4.7/spring-security-core-6.4.7.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter-test/3.5.12/mybatis-plus-spring-boot3-starter-test-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-test-autoconfigure/3.5.12/mybatis-plus-spring-boot-test-autoconfigure-3.5.12.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.2.8/spring-tx-6.2.8.jar:/Users/<USER>/.m2/repository/org/testcontainers/postgresql/1.20.6/postgresql-1.20.6.jar:/Users/<USER>/.m2/repository/org/testcontainers/jdbc/1.20.6/jdbc-1.20.6.jar:/Users/<USER>/.m2/repository/org/testcontainers/database-commons/1.20.6/database-commons-1.20.6.jar:/Users/<USER>/.m2/repository/org/testcontainers/testcontainers/1.20.6/testcontainers-1.20.6.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/repository/org/rnorth/duct-tape/duct-tape/1.0.8/duct-tape-1.0.8.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/17.0.0/annotations-17.0.0.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-api/3.4.1/docker-java-api-3.4.1.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-transport-zerodep/3.4.1/docker-java-transport-zerodep-3.4.1.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-transport/3.4.1/docker-java-transport-3.4.1.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.13.0/jna-5.13.0.jar:/Users/<USER>/.m2/repository/com/h2database/h2/2.3.232/h2-2.3.232.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.38/lombok-1.18.38.jar:"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="org.jboss.logging.provider" value="slf4j"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="21"/>
    <property name="APPLICATION_NAME" value="pisp-user-service-test"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="/Users/<USER>/.sdkman/candidates/java/21.0.7-oracle/lib"/>
    <property name="sun.java.command" value="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/target/surefire/surefirebooter-20250630213518713_3.jar /Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/target/surefire 2025-06-30T21-35-18_678-jvmRun1 surefire-20250630213518713_1tmp surefire_0-20250630213518713_2tmp"/>
    <property name="http.nonProxyHosts" value="127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|&lt;local&gt;|*.&lt;local&gt;"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/target/test-classes:/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.4.7/spring-boot-starter-web-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.4.7/spring-boot-starter-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.4.7/spring-boot-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.4.7/spring-boot-starter-logging-3.4.7.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.5.18/logback-classic-1.5.18.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.4.7/spring-boot-starter-json-3.4.7.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.18.4/jackson-datatype-jdk8-2.18.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.18.4/jackson-module-parameter-names-2.18.4.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.2.8/spring-web-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.2.8/spring-beans-6.2.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.14.8/micrometer-observation-1.14.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.14.8/micrometer-commons-1.14.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.2.8/spring-webmvc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.2.8/spring-context-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.2.8/spring-expression-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-undertow/3.4.7/spring-boot-starter-undertow-3.4.7.jar:/Users/<USER>/.m2/repository/io/undertow/undertow-core/2.3.18.Final/undertow-core-2.3.18.Final.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.6.1.Final/jboss-logging-3.6.1.Final.jar:/Users/<USER>/.m2/repository/org/jboss/xnio/xnio-api/3.8.16.Final/xnio-api-3.8.16.Final.jar:/Users/<USER>/.m2/repository/org/wildfly/common/wildfly-common/1.5.4.Final/wildfly-common-1.5.4.Final.jar:/Users/<USER>/.m2/repository/org/wildfly/client/wildfly-client-config/1.0.1.Final/wildfly-client-config-1.0.1.Final.jar:/Users/<USER>/.m2/repository/org/jboss/xnio/xnio-nio/3.8.16.Final/xnio-nio-3.8.16.Final.jar:/Users/<USER>/.m2/repository/org/jboss/threads/jboss-threads/3.5.0.Final/jboss-threads-3.5.0.Final.jar:/Users/<USER>/.m2/repository/io/undertow/undertow-servlet/2.3.18.Final/undertow-servlet-2.3.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/servlet/jakarta.servlet-api/6.0.0/jakarta.servlet-api-6.0.0.jar:/Users/<USER>/.m2/repository/io/undertow/undertow-websockets-jsr/2.3.18.Final/undertow-websockets-jsr-2.3.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/websocket/jakarta.websocket-api/2.1.1/jakarta.websocket-api-2.1.1.jar:/Users/<USER>/.m2/repository/jakarta/websocket/jakarta.websocket-client-api/2.1.1/jakarta.websocket-client-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.42/tomcat-embed-el-10.1.42.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.4.7/spring-boot-starter-validation-3.4.7.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.2.Final/hibernate-validator-8.0.2.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.7.0/classmate-1.7.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.4.7/spring-boot-starter-security-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.2.8/spring-aop-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.4.7/spring-security-config-6.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.4.7/spring-security-web-6.4.7.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-discovery/2023.0.3.3/spring-cloud-starter-alibaba-nacos-discovery-2023.0.3.3.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-alibaba-commons/2023.0.3.3/spring-cloud-alibaba-commons-2023.0.3.3.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.4.2/nacos-client-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-auth-plugin/2.4.2/nacos-auth-plugin-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-encryption-plugin/2.4.2/nacos-encryption-plugin-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-logback-adapter-12/2.4.2/nacos-logback-adapter-12-2.4.2.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/logback-adapter/1.1.3/logback-adapter-1.1.3.jar:/Users/<USER>/.m2/repository/com/alibaba/nacos/nacos-log4j2-adapter/2.4.2/nacos-log4j2-adapter-2.4.2.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.17.2/commons-codec-1.17.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.18.4.1/jackson-core-2.18.4.1.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpasyncclient/4.1.5/httpasyncclient-4.1.5.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore-nio/4.4.16/httpcore-nio-4.4.16.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient/0.16.0/simpleclient-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_otel/0.16.0/simpleclient_tracer_otel-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_common/0.16.0/simpleclient_tracer_common-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_otel_agent/0.16.0/simpleclient_tracer_otel_agent-0.16.0.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.14.8/micrometer-core-1.14.8.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.2.2/HdrHistogram-2.2.2.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-commons/4.2.1/spring-cloud-commons-4.2.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.4.7/spring-security-crypto-6.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-context/4.2.1/spring-cloud-context-4.2.1.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-config/2023.0.3.3/spring-cloud-starter-alibaba-nacos-config-2023.0.3.3.jar:/Users/<USER>/.m2/repository/com/alibaba/cloud/spring-alibaba-nacos-config/2023.0.3.3/spring-alibaba-nacos-config-2023.0.3.3.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jdbc/3.4.7/spring-boot-starter-data-jdbc-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.4.7/spring-boot-starter-jdbc-3.4.7.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/5.1.0/HikariCP-5.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.2.8/spring-jdbc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jdbc/3.4.7/spring-data-jdbc-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-relational/3.4.7/spring-data-relational-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.4.7/spring-data-commons-3.4.7.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter/3.5.12/mybatis-plus-spring-boot3-starter-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.12/mybatis-plus-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.12/mybatis-plus-core-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.12/mybatis-plus-annotation-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring/3.5.12/mybatis-plus-spring-3.5.12.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.19/mybatis-3.5.19.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/3.0.4/mybatis-spring-3.0.4.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.12/mybatis-plus-spring-boot-autoconfigure-3.5.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.4.7/spring-boot-autoconfigure-3.4.7.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-jsqlparser/3.5.12/mybatis-plus-jsqlparser-3.5.12.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/5.1/jsqlparser-5.1.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-jsqlparser-common/3.5.12/mybatis-plus-jsqlparser-common-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.12/mybatis-plus-extension-3.5.12.jar:/Users/<USER>/.m2/repository/org/postgresql/postgresql/42.7.7/postgresql-42.7.7.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.49.3/checker-qual-3.49.3.jar:/Users/<USER>/.m2/repository/org/liquibase/liquibase-core/4.29.2/liquibase-core-4.29.2.jar:/Users/<USER>/.m2/repository/com/opencsv/opencsv/5.9/opencsv-5.9.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.3/snakeyaml-2.3.jar:/Users/<USER>/.m2/repository/javax/xml/bind/jaxb-api/2.3.1/jaxb-api-2.3.1.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.17.0/commons-io-2.17.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.5.0/commons-collections4-4.5.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.13.1/commons-text-1.13.1.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar:/Users/<USER>/.m2/repository/com/bdyl/erp/pisp/pisp-common-core/1.0.0-SNAPSHOT/pisp-common-core-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.38/hutool-all-5.8.38.jar:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.6.3/mapstruct-1.6.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.18.4/jackson-databind-2.18.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.18.4/jackson-annotations-2.18.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.18.4/jackson-datatype-jsr310-2.18.4.jar:/Users/<USER>/.m2/repository/com/bdyl/erp/pisp/pisp-common-security/1.0.0-SNAPSHOT/pisp-common-security-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.12.6/jjwt-api-0.12.6.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.12.6/jjwt-impl-0.12.6.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.12.6/jjwt-jackson-0.12.6.jar:/Users/<USER>/.m2/repository/com/bdyl/erp/pisp/pisp-common-web/1.0.0-SNAPSHOT/pisp-common-web-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/com/bdyl/erp/pisp/pisp-common-redis/1.0.0-SNAPSHOT/pisp-common-redis-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/3.4.7/spring-boot-starter-data-redis-3.4.7.jar:/Users/<USER>/.m2/repository/io/lettuce/lettuce-core/6.4.2.RELEASE/lettuce-core-6.4.2.RELEASE.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.122.Final/netty-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.122.Final/netty-handler-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.122.Final/netty-resolver-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.122.Final/netty-buffer-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.122.Final/netty-transport-native-unix-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.122.Final/netty-codec-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.122.Final/netty-transport-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.7.7/reactor-core-3.7.7.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/3.4.7/spring-data-redis-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/3.4.7/spring-data-keyvalue-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.2.8/spring-oxm-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.2.8/spring-context-support-6.2.8.jar:/Users/<USER>/.m2/repository/com/bdyl/erp/pisp/pisp-api/1.0.0-SNAPSHOT/pisp-api-1.0.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.4.7/spring-boot-starter-test-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.4.7/spring-boot-test-3.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.4.7/spring-boot-test-autoconfigure-3.4.7.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/9.7.1/asm-9.7.1.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.26.3/assertj-core-3.26.3.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.15.11/byte-buddy-1.15.11.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.11.4/junit-jupiter-5.11.4.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.11.4/junit-jupiter-api-5.11.4.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.11.4/junit-platform-commons-1.11.4.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.11.4/junit-jupiter-params-5.11.4.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.11.4/junit-jupiter-engine-5.11.4.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.11.4/junit-platform-engine-1.11.4.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.14.2/mockito-core-5.14.2.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.15.11/byte-buddy-agent-1.15.11.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.14.2/mockito-junit-jupiter-5.14.2.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.2.8/spring-core-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.2.8/spring-jcl-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.2.8/spring-test-6.2.8.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.10.2/xmlunit-core-2.10.2.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-test/6.4.7/spring-security-test-6.4.7.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.4.7/spring-security-core-6.4.7.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter-test/3.5.12/mybatis-plus-spring-boot3-starter-test-3.5.12.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-test-autoconfigure/3.5.12/mybatis-plus-spring-boot-test-autoconfigure-3.5.12.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.2.8/spring-tx-6.2.8.jar:/Users/<USER>/.m2/repository/org/testcontainers/postgresql/1.20.6/postgresql-1.20.6.jar:/Users/<USER>/.m2/repository/org/testcontainers/jdbc/1.20.6/jdbc-1.20.6.jar:/Users/<USER>/.m2/repository/org/testcontainers/database-commons/1.20.6/database-commons-1.20.6.jar:/Users/<USER>/.m2/repository/org/testcontainers/testcontainers/1.20.6/testcontainers-1.20.6.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/repository/org/rnorth/duct-tape/duct-tape/1.0.8/duct-tape-1.0.8.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/17.0.0/annotations-17.0.0.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-api/3.4.1/docker-java-api-3.4.1.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-transport-zerodep/3.4.1/docker-java-transport-zerodep-3.4.1.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-transport/3.4.1/docker-java-transport-3.4.1.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.13.0/jna-5.13.0.jar:/Users/<USER>/.m2/repository/com/h2database/h2/2.3.232/h2-2.3.232.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.38/lombok-1.18.38.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="zh"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-04-15"/>
    <property name="java.home" value="/Users/<USER>/.sdkman/candidates/java/21.0.7-oracle"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user"/>
    <property name="java.vm.compressedOopsMode" value="Non-zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="checkstyle.skip" value="true"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="FILE_LOG_CHARSET" value="UTF-8"/>
    <property name="java.awt.headless" value="true"/>
    <property name="apple.awt.application.name" value="ForkedBooter"/>
    <property name="surefire.real.class.path" value="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user/target/surefire/surefirebooter-20250630213518713_3.jar"/>
    <property name="user.script" value="Hans"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|&lt;local&gt;|*.&lt;local&gt;"/>
    <property name="java.runtime.version" value="21.0.7+8-LTS-245"/>
    <property name="user.name" value="jeffery"/>
    <property name="stdout.encoding" value="UTF-8"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.5"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="/var/folders/2c/k8bwfr1j5cz_4rmd02kztnph0000gn/T/"/>
    <property name="com.zaxxer.hikari.pool_number" value="2"/>
    <property name="java.version" value="21.0.7"/>
    <property name="user.dir" value="/Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user"/>
    <property name="os.arch" value="aarch64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="PID" value="13932"/>
    <property name="CONSOLE_LOG_CHARSET" value="UTF-8"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="UTF-8"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="21.0.7+8-LTS-245"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="socksNonProxyHosts" value="127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|&lt;local&gt;|*.&lt;local&gt;"/>
    <property name="java.class.version" value="65.0"/>
    <property name="LOGGED_APPLICATION_NAME" value="[pisp-user-service-test] "/>
  </properties>
  <testcase name="testDeleteDepartment" classname="com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest" time="0.016">
    <system-out><![CDATA[2025-06-30T21:35:22.882+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.support.AbstractContextLoader    : Could not detect default resource locations for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-06-30T21:35:22.882+08:00  INFO 13932 --- [pisp-user-service-test] [           main] t.c.s.AnnotationConfigContextLoaderUtils : Could not detect default configuration classes for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]: DepartmentServiceImplTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
2025-06-30T21:35:22.892+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] .s.t.c.u.TestContextSpringFactoriesUtils : Skipping candidate TestExecutionListener [org.springframework.test.context.observation.MicrometerObservationRegistryTestExecutionListener] due to a missing dependency. Specify custom TestExecutionListener classes or make the default TestExecutionListener classes and their required dependencies available. Offending class: io.micrometer.context.ThreadLocalAccessor. MicrometerObservationRegistryTestExecutionListener requires io.micrometer:micrometer-observation:1.10.8 or higher and io.micrometer:context-propagation:1.0.3 or higher.
2025-06-30T21:35:22.893+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test class: class [DepartmentServiceImplTest], class annotated with @DirtiesContext [false] with mode [null]

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.7)

2025-06-30T21:35:22.931+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.i.DepartmentServiceImplTest  : Starting DepartmentServiceImplTest using Java 21.0.7 with PID 13932 (started by jeffery in /Users/<USER>/work/bdyl/projects/pisp/pisp-services/pisp-user)
2025-06-30T21:35:22.931+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.i.DepartmentServiceImplTest  : Running with Spring Boot v3.4.7, Spring v6.2.8
2025-06-30T21:35:22.931+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.i.DepartmentServiceImplTest  : The following 1 profile is active: "test"
Logging initialized using 'class org.apache.ibatis.logging.stdout.StdOutImpl' adapter.
Get /192.168.2.219 network interface 
Get network interface info: name:en0 (en0)
Initialization Sequence datacenterId:20 workerId:24
2025-06-30T21:35:23.256+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.erp.pisp.user.config.SecurityConfig  : 配置Spring Security过滤器链
2025-06-30T21:35:23.260+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.erp.pisp.user.config.SecurityConfig  : Spring Security配置完成
2025-06-30T21:35:23.319+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.i.DepartmentServiceImplTest  : Started DepartmentServiceImplTest in 0.426 seconds (process running for 4.57)
2025-06-30T21:35:23.320+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 129, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.320+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.320+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.320+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 130, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.320+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 131, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.321+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [DepartmentServiceImplTest], method [testDeleteDepartment], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.321+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest] and test method [testDeleteDepartment]
2025-06-30T21:35:23.321+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 132, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.321+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest.testDeleteDepartment()]
2025-06-30T21:35:23.321+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testDeleteDepartment]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@5b265379]; rollback [true]
2025-06-30T21:35:23.321+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 133, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.321+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 134, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.322+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 135, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.323+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 136, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.323+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 创建部门: deptName=单元测试部门1751290523322, deptCode=TEST_DEPT_1751290523323
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@159c9c91]
JDBC Connection [HikariProxyConnection@945657984 wrapping conn2: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_code = ? AND deleted = 0
==> Parameters: TEST_DEPT_1751290523323(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@159c9c91]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@159c9c91] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_name = ? AND deleted = 0 AND parent_id IS NULL
==> Parameters: 单元测试部门1751290523322(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@159c9c91]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@159c9c91] from current transaction
2025-06-30T21:35:23.327+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:23.327+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_departments ( id, dept_name, dept_code, dept_level, description, sort_order, status, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679157607301121(Long), 单元测试部门1751290523322(String), TEST_DEPT_1751290523323(String), 1(Integer), 这是一个测试部门(String), 1(Integer), ACTIVE(String), 2025-06-30T21:35:23.327163(LocalDateTime), 2025-06-30T21:35:23.327203(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@159c9c91]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@159c9c91] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157607301121(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157607301121, 单元测试部门1751290523322, TEST_DEPT_1751290523323, null, 1, null, 这是一个测试部门, null, null, null, 1, ACTIVE, 0, 0, 2025-06-30 21:35:23.327163, 2025-06-30 21:35:23.327203, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@159c9c91]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@159c9c91] from current transaction
==>  Preparing: UPDATE sys_departments SET dept_path = ?, update_time = NOW(), version = version + 1 WHERE id = ? AND deleted = 0
==> Parameters: /1939679157607301121(String), 1939679157607301121(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@159c9c91]
2025-06-30T21:35:23.330+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门创建成功: deptId=1939679157607301121
2025-06-30T21:35:23.331+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 删除部门: deptId=1939679157607301121
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@159c9c91] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157607301121(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157607301121, 单元测试部门1751290523322, TEST_DEPT_1751290523323, null, 1, /1939679157607301121, 这是一个测试部门, null, null, null, 1, ACTIVE, 1, 0, 2025-06-30 21:35:23.327163, 2025-06-30 21:35:23.330179, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@159c9c91]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@159c9c91] from current transaction
==>  Preparing: SELECT * FROM sys_departments WHERE parent_id = ? AND deleted = 0 ORDER BY sort_order ASC, create_time ASC
==> Parameters: 1939679157607301121(Long)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@159c9c91]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@159c9c91] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_users WHERE department_id = ? AND deleted = 0 AND status = 'ACTIVE'
==> Parameters: 1939679157607301121(Long)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@159c9c91]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@159c9c91] from current transaction
2025-06-30T21:35:23.333+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始更新填充...
2025-06-30T21:35:23.333+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 更新填充完成
==>  Preparing: UPDATE sys_departments SET update_time=?,updater_id=?, deleted=1 WHERE id=? AND deleted=0
==> Parameters: 2025-06-30T21:35:23.333373(LocalDateTime), 1(Long), 1939679157607301121(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@159c9c91]
2025-06-30T21:35:23.333+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门删除成功: deptId=1939679157607301121
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@159c9c91] from current transaction
==>  Preparing: SELECT d.*, u.id AS leader_user_id, u.username AS leader_username, u.real_name AS leader_real_name, u.email AS leader_email, u.phone AS leader_phone FROM sys_departments d LEFT JOIN sys_users u ON d.leader_id = u.id AND u.deleted = 0 WHERE d.id = ? AND d.deleted = 0
==> Parameters: 1939679157607301121(Long)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@159c9c91]
2025-06-30T21:35:23.335+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 137, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.335+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 138, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.336+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 139, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.336+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 140, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.336+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 141, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.336+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 142, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.336+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 143, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.336+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 144, missCount = 2, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@159c9c91]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@159c9c91]
2025-06-30T21:35:23.337+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testDeleteDepartment]
2025-06-30T21:35:23.337+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [DepartmentServiceImplTest], method [testDeleteDepartment], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.337+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
]]></system-out>
  </testcase>
  <testcase name="testDeleteDepartmentWithChildren" classname="com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest" time="0.011">
    <system-out><![CDATA[2025-06-30T21:35:23.337+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 145, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.337+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.337+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.337+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 146, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.337+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 147, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.337+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [DepartmentServiceImplTest], method [testDeleteDepartmentWithChildren], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.337+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest] and test method [testDeleteDepartmentWithChildren]
2025-06-30T21:35:23.337+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 148, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.338+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest.testDeleteDepartmentWithChildren()]
2025-06-30T21:35:23.338+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testDeleteDepartmentWithChildren]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@5b265379]; rollback [true]
2025-06-30T21:35:23.338+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 149, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.338+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 150, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.338+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 151, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.338+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 152, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.338+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 创建部门: deptName=单元测试部门1751290523338, deptCode=TEST_DEPT_1751290523338
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490]
JDBC Connection [HikariProxyConnection@1709848929 wrapping conn2: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_code = ? AND deleted = 0
==> Parameters: TEST_DEPT_1751290523338(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_name = ? AND deleted = 0 AND parent_id IS NULL
==> Parameters: 单元测试部门1751290523338(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490] from current transaction
2025-06-30T21:35:23.340+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:23.340+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_departments ( id, dept_name, dept_code, dept_level, description, sort_order, status, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679157661827073(Long), 单元测试部门1751290523338(String), TEST_DEPT_1751290523338(String), 1(Integer), 这是一个测试部门(String), 1(Integer), ACTIVE(String), 2025-06-30T21:35:23.340158(LocalDateTime), 2025-06-30T21:35:23.340174(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157661827073(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157661827073, 单元测试部门1751290523338, TEST_DEPT_1751290523338, null, 1, null, 这是一个测试部门, null, null, null, 1, ACTIVE, 0, 0, 2025-06-30 21:35:23.340158, 2025-06-30 21:35:23.340174, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490] from current transaction
==>  Preparing: UPDATE sys_departments SET dept_path = ?, update_time = NOW(), version = version + 1 WHERE id = ? AND deleted = 0
==> Parameters: /1939679157661827073(String), 1939679157661827073(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490]
2025-06-30T21:35:23.341+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门创建成功: deptId=1939679157661827073
2025-06-30T21:35:23.341+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 创建部门: deptName=子部门1751290523341, deptCode=SUB_DEPT_1751290523341
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_code = ? AND deleted = 0
==> Parameters: SUB_DEPT_1751290523341(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_name = ? AND deleted = 0 AND parent_id = ?
==> Parameters: 子部门1751290523341(String), 1939679157661827073(Long)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157661827073(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157661827073, 单元测试部门1751290523338, TEST_DEPT_1751290523338, null, 1, /1939679157661827073, 这是一个测试部门, null, null, null, 1, ACTIVE, 1, 0, 2025-06-30 21:35:23.340158, 2025-06-30 21:35:23.341364, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490] from current transaction
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490] from current transaction
2025-06-30T21:35:23.343+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:23.343+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_departments ( id, dept_name, dept_code, parent_id, dept_level, sort_order, status, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679157674409986(Long), 子部门1751290523341(String), SUB_DEPT_1751290523341(String), 1939679157661827073(Long), 2(Integer), 0(Integer), ACTIVE(String), 2025-06-30T21:35:23.343103(LocalDateTime), 2025-06-30T21:35:23.343116(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157674409986(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157674409986, 子部门1751290523341, SUB_DEPT_1751290523341, 1939679157661827073, 2, null, null, null, null, null, 0, ACTIVE, 0, 0, 2025-06-30 21:35:23.343103, 2025-06-30 21:35:23.343116, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157661827073(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157661827073, 单元测试部门1751290523338, TEST_DEPT_1751290523338, null, 1, /1939679157661827073, 这是一个测试部门, null, null, null, 1, ACTIVE, 1, 0, 2025-06-30 21:35:23.340158, 2025-06-30 21:35:23.341364, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490] from current transaction
==>  Preparing: UPDATE sys_departments SET dept_path = ?, update_time = NOW(), version = version + 1 WHERE id = ? AND deleted = 0
==> Parameters: /1939679157661827073/1939679157674409986(String), 1939679157674409986(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490]
2025-06-30T21:35:23.344+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门创建成功: deptId=1939679157674409986
2025-06-30T21:35:23.345+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 删除部门: deptId=1939679157661827073
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157661827073(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157661827073, 单元测试部门1751290523338, TEST_DEPT_1751290523338, null, 1, /1939679157661827073, 这是一个测试部门, null, null, null, 1, ACTIVE, 1, 0, 2025-06-30 21:35:23.340158, 2025-06-30 21:35:23.341364, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490] from current transaction
==>  Preparing: SELECT * FROM sys_departments WHERE parent_id = ? AND deleted = 0 ORDER BY sort_order ASC, create_time ASC
==> Parameters: 1939679157661827073(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, TENANT_ID, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157674409986, 子部门1751290523341, SUB_DEPT_1751290523341, 1939679157661827073, 2, /1939679157661827073/1939679157674409986, null, null, null, null, 0, ACTIVE, 1, 0, 1, 2025-06-30 21:35:23.343103, 2025-06-30 21:35:23.341364, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490]
2025-06-30T21:35:23.346+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 153, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.347+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 154, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.347+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 155, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.347+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 156, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.347+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 157, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.347+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 158, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.347+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 159, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.347+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 160, missCount = 2, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37c48490]
2025-06-30T21:35:23.348+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testDeleteDepartmentWithChildren]
2025-06-30T21:35:23.348+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [DepartmentServiceImplTest], method [testDeleteDepartmentWithChildren], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.348+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
]]></system-out>
  </testcase>
  <testcase name="testUpdateDepartment" classname="com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest" time="0.007">
    <system-out><![CDATA[2025-06-30T21:35:23.348+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 161, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.348+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.348+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.348+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 162, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.348+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 163, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.348+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [DepartmentServiceImplTest], method [testUpdateDepartment], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.348+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest] and test method [testUpdateDepartment]
2025-06-30T21:35:23.348+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 164, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.348+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest.testUpdateDepartment()]
2025-06-30T21:35:23.348+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testUpdateDepartment]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@5b265379]; rollback [true]
2025-06-30T21:35:23.348+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 165, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.348+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 166, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.349+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 167, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.349+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 168, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.349+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 创建部门: deptName=单元测试部门1751290523349, deptCode=TEST_DEPT_1751290523349
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7f56a025]
JDBC Connection [HikariProxyConnection@1446242386 wrapping conn2: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_code = ? AND deleted = 0
==> Parameters: TEST_DEPT_1751290523349(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7f56a025]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7f56a025] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_name = ? AND deleted = 0 AND parent_id IS NULL
==> Parameters: 单元测试部门1751290523349(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7f56a025]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7f56a025] from current transaction
2025-06-30T21:35:23.350+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:23.350+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_departments ( id, dept_name, dept_code, dept_level, description, sort_order, status, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679157707964418(Long), 单元测试部门1751290523349(String), TEST_DEPT_1751290523349(String), 1(Integer), 这是一个测试部门(String), 1(Integer), ACTIVE(String), 2025-06-30T21:35:23.350723(LocalDateTime), 2025-06-30T21:35:23.350735(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7f56a025]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7f56a025] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157707964418(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157707964418, 单元测试部门1751290523349, TEST_DEPT_1751290523349, null, 1, null, 这是一个测试部门, null, null, null, 1, ACTIVE, 0, 0, 2025-06-30 21:35:23.350723, 2025-06-30 21:35:23.350735, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7f56a025]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7f56a025] from current transaction
==>  Preparing: UPDATE sys_departments SET dept_path = ?, update_time = NOW(), version = version + 1 WHERE id = ? AND deleted = 0
==> Parameters: /1939679157707964418(String), 1939679157707964418(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7f56a025]
2025-06-30T21:35:23.351+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门创建成功: deptId=1939679157707964418
2025-06-30T21:35:23.351+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 更新部门: deptId=1939679157707964418
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7f56a025] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157707964418(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157707964418, 单元测试部门1751290523349, TEST_DEPT_1751290523349, null, 1, /1939679157707964418, 这是一个测试部门, null, null, null, 1, ACTIVE, 1, 0, 2025-06-30 21:35:23.350723, 2025-06-30 21:35:23.351517, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7f56a025]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7f56a025] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_name = ? AND deleted = 0 AND parent_id IS NULL AND id != ?
==> Parameters: 更新后的部门名称(String), 1939679157707964418(Long)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7f56a025]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7f56a025] from current transaction
2025-06-30T21:35:23.353+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始更新填充...
2025-06-30T21:35:23.353+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 更新填充完成
==>  Preparing: UPDATE sys_departments SET dept_name=?, dept_code=?, dept_level=?, dept_path=?, description=?, create_time=?, update_time=?, creator_id=?, updater_id=? WHERE id=? AND deleted=0
==> Parameters: 更新后的部门名称(String), TEST_DEPT_1751290523349(String), 1(Integer), /1939679157707964418(String), 更新后的描述(String), 2025-06-30T21:35:23.350723(LocalDateTime), 2025-06-30T21:35:23.351517(LocalDateTime), 1(Long), 1(Long), 1939679157707964418(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7f56a025]
2025-06-30T21:35:23.353+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门更新成功: deptId=1939679157707964418
2025-06-30T21:35:23.354+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 169, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.354+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 170, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.354+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 171, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.354+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 172, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.354+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 173, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.354+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 174, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.355+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 175, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.355+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 176, missCount = 2, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7f56a025]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7f56a025]
2025-06-30T21:35:23.355+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testUpdateDepartment]
2025-06-30T21:35:23.355+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [DepartmentServiceImplTest], method [testUpdateDepartment], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.355+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
]]></system-out>
  </testcase>
  <testcase name="testBatchDeleteDepartments" classname="com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest" time="0.009">
    <system-out><![CDATA[2025-06-30T21:35:23.355+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 177, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.355+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.355+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.355+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 178, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.356+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 179, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.356+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [DepartmentServiceImplTest], method [testBatchDeleteDepartments], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.356+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest] and test method [testBatchDeleteDepartments]
2025-06-30T21:35:23.356+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 180, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.356+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest.testBatchDeleteDepartments()]
2025-06-30T21:35:23.356+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testBatchDeleteDepartments]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@5b265379]; rollback [true]
2025-06-30T21:35:23.356+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 181, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.356+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 182, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.356+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 183, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.356+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 184, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.357+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 创建部门: deptName=批量测试部门1_1751290523356, deptCode=BATCH_DEPT_1_1751290523356
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed]
JDBC Connection [HikariProxyConnection@1033887610 wrapping conn2: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_code = ? AND deleted = 0
==> Parameters: BATCH_DEPT_1_1751290523356(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_name = ? AND deleted = 0 AND parent_id IS NULL
==> Parameters: 批量测试部门1_1751290523356(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed] from current transaction
2025-06-30T21:35:23.357+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:23.357+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_departments ( id, dept_name, dept_code, dept_level, sort_order, status, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679157737324546(Long), 批量测试部门1_1751290523356(String), BATCH_DEPT_1_1751290523356(String), 1(Integer), 0(Integer), ACTIVE(String), 2025-06-30T21:35:23.357943(LocalDateTime), 2025-06-30T21:35:23.357954(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157737324546(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157737324546, 批量测试部门1_1751290523356, BATCH_DEPT_1_1751290523356, null, 1, null, null, null, null, null, 0, ACTIVE, 0, 0, 2025-06-30 21:35:23.357943, 2025-06-30 21:35:23.357954, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed] from current transaction
==>  Preparing: UPDATE sys_departments SET dept_path = ?, update_time = NOW(), version = version + 1 WHERE id = ? AND deleted = 0
==> Parameters: /1939679157737324546(String), 1939679157737324546(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed]
2025-06-30T21:35:23.358+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门创建成功: deptId=1939679157737324546
2025-06-30T21:35:23.359+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 创建部门: deptName=批量测试部门2_1751290523356, deptCode=BATCH_DEPT_2_1751290523356
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_code = ? AND deleted = 0
==> Parameters: BATCH_DEPT_2_1751290523356(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_name = ? AND deleted = 0 AND parent_id IS NULL
==> Parameters: 批量测试部门2_1751290523356(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed] from current transaction
2025-06-30T21:35:23.359+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:23.359+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_departments ( id, dept_name, dept_code, dept_level, sort_order, status, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679157745713153(Long), 批量测试部门2_1751290523356(String), BATCH_DEPT_2_1751290523356(String), 1(Integer), 0(Integer), ACTIVE(String), 2025-06-30T21:35:23.359555(LocalDateTime), 2025-06-30T21:35:23.359564(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157745713153(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157745713153, 批量测试部门2_1751290523356, BATCH_DEPT_2_1751290523356, null, 1, null, null, null, null, null, 0, ACTIVE, 0, 0, 2025-06-30 21:35:23.359555, 2025-06-30 21:35:23.359564, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed] from current transaction
==>  Preparing: UPDATE sys_departments SET dept_path = ?, update_time = NOW(), version = version + 1 WHERE id = ? AND deleted = 0
==> Parameters: /1939679157745713153(String), 1939679157745713153(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed]
2025-06-30T21:35:23.360+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门创建成功: deptId=1939679157745713153
2025-06-30T21:35:23.360+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 批量删除部门: deptIds=[1939679157737324546, 1939679157745713153]
2025-06-30T21:35:23.360+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 删除部门: deptId=1939679157737324546
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157737324546(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157737324546, 批量测试部门1_1751290523356, BATCH_DEPT_1_1751290523356, null, 1, /1939679157737324546, null, null, null, null, 0, ACTIVE, 1, 0, 2025-06-30 21:35:23.357943, 2025-06-30 21:35:23.358889, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed] from current transaction
==>  Preparing: SELECT * FROM sys_departments WHERE parent_id = ? AND deleted = 0 ORDER BY sort_order ASC, create_time ASC
==> Parameters: 1939679157737324546(Long)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_users WHERE department_id = ? AND deleted = 0 AND status = 'ACTIVE'
==> Parameters: 1939679157737324546(Long)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed] from current transaction
2025-06-30T21:35:23.361+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始更新填充...
2025-06-30T21:35:23.361+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 更新填充完成
==>  Preparing: UPDATE sys_departments SET update_time=?,updater_id=?, deleted=1 WHERE id=? AND deleted=0
==> Parameters: 2025-06-30T21:35:23.361534(LocalDateTime), 1(Long), 1939679157737324546(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed]
2025-06-30T21:35:23.362+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门删除成功: deptId=1939679157737324546
2025-06-30T21:35:23.362+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 删除部门: deptId=1939679157745713153
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157745713153(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157745713153, 批量测试部门2_1751290523356, BATCH_DEPT_2_1751290523356, null, 1, /1939679157745713153, null, null, null, null, 0, ACTIVE, 1, 0, 2025-06-30 21:35:23.359555, 2025-06-30 21:35:23.358889, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed] from current transaction
==>  Preparing: SELECT * FROM sys_departments WHERE parent_id = ? AND deleted = 0 ORDER BY sort_order ASC, create_time ASC
==> Parameters: 1939679157745713153(Long)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_users WHERE department_id = ? AND deleted = 0 AND status = 'ACTIVE'
==> Parameters: 1939679157745713153(Long)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed] from current transaction
2025-06-30T21:35:23.363+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始更新填充...
2025-06-30T21:35:23.363+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 更新填充完成
==>  Preparing: UPDATE sys_departments SET update_time=?,updater_id=?, deleted=1 WHERE id=? AND deleted=0
==> Parameters: 2025-06-30T21:35:23.363109(LocalDateTime), 1(Long), 1939679157745713153(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed]
2025-06-30T21:35:23.363+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门删除成功: deptId=1939679157745713153
2025-06-30T21:35:23.363+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 批量删除部门完成: count=2
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed] from current transaction
==>  Preparing: SELECT d.*, u.id AS leader_user_id, u.username AS leader_username, u.real_name AS leader_real_name, u.email AS leader_email, u.phone AS leader_phone FROM sys_departments d LEFT JOIN sys_users u ON d.leader_id = u.id AND u.deleted = 0 WHERE d.id = ? AND d.deleted = 0
==> Parameters: 1939679157737324546(Long)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed] from current transaction
==>  Preparing: SELECT d.*, u.id AS leader_user_id, u.username AS leader_username, u.real_name AS leader_real_name, u.email AS leader_email, u.phone AS leader_phone FROM sys_departments d LEFT JOIN sys_users u ON d.leader_id = u.id AND u.deleted = 0 WHERE d.id = ? AND d.deleted = 0
==> Parameters: 1939679157745713153(Long)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed]
2025-06-30T21:35:23.364+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 185, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.364+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 186, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.364+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 187, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.364+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 188, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.364+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 189, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.364+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 190, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.364+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 191, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.364+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 192, missCount = 2, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d5105ed]
2025-06-30T21:35:23.365+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testBatchDeleteDepartments]
2025-06-30T21:35:23.365+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [DepartmentServiceImplTest], method [testBatchDeleteDepartments], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.365+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
]]></system-out>
  </testcase>
  <testcase name="testIsDeptCodeExists" classname="com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest" time="0.004">
    <system-out><![CDATA[2025-06-30T21:35:23.365+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 193, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.365+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.365+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.365+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 194, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.365+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 195, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.365+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [DepartmentServiceImplTest], method [testIsDeptCodeExists], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.365+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest] and test method [testIsDeptCodeExists]
2025-06-30T21:35:23.365+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 196, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.365+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest.testIsDeptCodeExists()]
2025-06-30T21:35:23.365+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testIsDeptCodeExists]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@5b265379]; rollback [true]
2025-06-30T21:35:23.365+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 197, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.366+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 198, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.366+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 199, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.366+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 200, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.366+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 创建部门: deptName=单元测试部门1751290523366, deptCode=TEST_DEPT_1751290523366
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@561a4a5]
JDBC Connection [HikariProxyConnection@596629964 wrapping conn2: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_code = ? AND deleted = 0
==> Parameters: TEST_DEPT_1751290523366(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@561a4a5]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@561a4a5] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_name = ? AND deleted = 0 AND parent_id IS NULL
==> Parameters: 单元测试部门1751290523366(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@561a4a5]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@561a4a5] from current transaction
2025-06-30T21:35:23.367+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:23.367+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_departments ( id, dept_name, dept_code, dept_level, description, sort_order, status, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679157775073281(Long), 单元测试部门1751290523366(String), TEST_DEPT_1751290523366(String), 1(Integer), 这是一个测试部门(String), 1(Integer), ACTIVE(String), 2025-06-30T21:35:23.367304(LocalDateTime), 2025-06-30T21:35:23.367314(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@561a4a5]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@561a4a5] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157775073281(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157775073281, 单元测试部门1751290523366, TEST_DEPT_1751290523366, null, 1, null, 这是一个测试部门, null, null, null, 1, ACTIVE, 0, 0, 2025-06-30 21:35:23.367304, 2025-06-30 21:35:23.367314, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@561a4a5]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@561a4a5] from current transaction
==>  Preparing: UPDATE sys_departments SET dept_path = ?, update_time = NOW(), version = version + 1 WHERE id = ? AND deleted = 0
==> Parameters: /1939679157775073281(String), 1939679157775073281(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@561a4a5]
2025-06-30T21:35:23.368+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门创建成功: deptId=1939679157775073281
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@561a4a5] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_code = ? AND deleted = 0
==> Parameters: TEST_DEPT_1751290523366(String)
<==    Columns: ?column?
<==        Row: 1
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@561a4a5]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@561a4a5] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_code = ? AND deleted = 0
==> Parameters: NON_EXISTENT_CODE(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@561a4a5]
2025-06-30T21:35:23.368+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 201, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.368+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 202, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.368+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 203, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.368+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 204, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.368+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 205, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.369+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 206, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.369+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 207, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.369+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 208, missCount = 2, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@561a4a5]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@561a4a5]
2025-06-30T21:35:23.369+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testIsDeptCodeExists]
2025-06-30T21:35:23.369+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [DepartmentServiceImplTest], method [testIsDeptCodeExists], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.369+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
]]></system-out>
  </testcase>
  <testcase name="testIsDeptNameExists" classname="com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest" time="0.005">
    <system-out><![CDATA[2025-06-30T21:35:23.369+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 209, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.369+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.369+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.369+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 210, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.369+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 211, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.370+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [DepartmentServiceImplTest], method [testIsDeptNameExists], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.370+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest] and test method [testIsDeptNameExists]
2025-06-30T21:35:23.370+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 212, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.370+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest.testIsDeptNameExists()]
2025-06-30T21:35:23.370+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testIsDeptNameExists]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@5b265379]; rollback [true]
2025-06-30T21:35:23.370+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 213, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.370+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 214, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.370+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 215, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.370+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 216, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.370+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 创建部门: deptName=单元测试部门1751290523370, deptCode=TEST_DEPT_1751290523370
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ad58e2c]
JDBC Connection [HikariProxyConnection@1322609917 wrapping conn2: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_code = ? AND deleted = 0
==> Parameters: TEST_DEPT_1751290523370(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ad58e2c]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ad58e2c] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_name = ? AND deleted = 0 AND parent_id IS NULL
==> Parameters: 单元测试部门1751290523370(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ad58e2c]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ad58e2c] from current transaction
2025-06-30T21:35:23.371+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:23.371+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_departments ( id, dept_name, dept_code, dept_level, description, sort_order, status, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679157796044801(Long), 单元测试部门1751290523370(String), TEST_DEPT_1751290523370(String), 1(Integer), 这是一个测试部门(String), 1(Integer), ACTIVE(String), 2025-06-30T21:35:23.371692(LocalDateTime), 2025-06-30T21:35:23.371704(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ad58e2c]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ad58e2c] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157796044801(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157796044801, 单元测试部门1751290523370, TEST_DEPT_1751290523370, null, 1, null, 这是一个测试部门, null, null, null, 1, ACTIVE, 0, 0, 2025-06-30 21:35:23.371692, 2025-06-30 21:35:23.371704, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ad58e2c]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ad58e2c] from current transaction
==>  Preparing: UPDATE sys_departments SET dept_path = ?, update_time = NOW(), version = version + 1 WHERE id = ? AND deleted = 0
==> Parameters: /1939679157796044801(String), 1939679157796044801(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ad58e2c]
2025-06-30T21:35:23.372+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门创建成功: deptId=1939679157796044801
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ad58e2c] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_name = ? AND deleted = 0 AND parent_id IS NULL
==> Parameters: 单元测试部门1751290523370(String)
<==    Columns: ?column?
<==        Row: 1
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ad58e2c]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ad58e2c] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_name = ? AND deleted = 0 AND parent_id IS NULL
==> Parameters: 不存在的部门名称(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ad58e2c]
2025-06-30T21:35:23.374+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 217, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.374+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 218, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.374+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 219, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.374+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 220, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.374+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 221, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.374+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 222, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.374+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 223, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.374+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 224, missCount = 2, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ad58e2c]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ad58e2c]
2025-06-30T21:35:23.374+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testIsDeptNameExists]
2025-06-30T21:35:23.374+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [DepartmentServiceImplTest], method [testIsDeptNameExists], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.374+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
]]></system-out>
  </testcase>
  <testcase name="testActivateAndDeactivateDepartment" classname="com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest" time="0.006">
    <system-out><![CDATA[2025-06-30T21:35:23.375+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 225, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.375+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.375+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.375+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 226, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.375+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 227, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.375+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [DepartmentServiceImplTest], method [testActivateAndDeactivateDepartment], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.375+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest] and test method [testActivateAndDeactivateDepartment]
2025-06-30T21:35:23.375+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 228, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.375+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest.testActivateAndDeactivateDepartment()]
2025-06-30T21:35:23.375+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testActivateAndDeactivateDepartment]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@5b265379]; rollback [true]
2025-06-30T21:35:23.375+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 229, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.375+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 230, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.375+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 231, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.376+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 232, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.376+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 创建部门: deptName=单元测试部门1751290523375, deptCode=TEST_DEPT_1751290523375
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7668b4ff]
JDBC Connection [HikariProxyConnection@799515859 wrapping conn2: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_code = ? AND deleted = 0
==> Parameters: TEST_DEPT_1751290523375(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7668b4ff]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7668b4ff] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_name = ? AND deleted = 0 AND parent_id IS NULL
==> Parameters: 单元测试部门1751290523375(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7668b4ff]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7668b4ff] from current transaction
2025-06-30T21:35:23.376+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:23.376+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_departments ( id, dept_name, dept_code, dept_level, description, sort_order, status, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679157817016321(Long), 单元测试部门1751290523375(String), TEST_DEPT_1751290523375(String), 1(Integer), 这是一个测试部门(String), 1(Integer), ACTIVE(String), 2025-06-30T21:35:23.376790(LocalDateTime), 2025-06-30T21:35:23.376800(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7668b4ff]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7668b4ff] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157817016321(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157817016321, 单元测试部门1751290523375, TEST_DEPT_1751290523375, null, 1, null, 这是一个测试部门, null, null, null, 1, ACTIVE, 0, 0, 2025-06-30 21:35:23.37679, 2025-06-30 21:35:23.3768, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7668b4ff]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7668b4ff] from current transaction
==>  Preparing: UPDATE sys_departments SET dept_path = ?, update_time = NOW(), version = version + 1 WHERE id = ? AND deleted = 0
==> Parameters: /1939679157817016321(String), 1939679157817016321(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7668b4ff]
2025-06-30T21:35:23.377+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门创建成功: deptId=1939679157817016321
2025-06-30T21:35:23.377+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 禁用部门: deptId=1939679157817016321
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7668b4ff] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157817016321(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157817016321, 单元测试部门1751290523375, TEST_DEPT_1751290523375, null, 1, /1939679157817016321, 这是一个测试部门, null, null, null, 1, ACTIVE, 1, 0, 2025-06-30 21:35:23.37679, 2025-06-30 21:35:23.377525, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7668b4ff]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7668b4ff] from current transaction
2025-06-30T21:35:23.378+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始更新填充...
2025-06-30T21:35:23.378+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 更新填充完成
==>  Preparing: UPDATE sys_departments SET dept_name=?, dept_code=?, dept_level=?, dept_path=?, description=?, sort_order=?, status=?, version=?, create_time=?, update_time=?, creator_id=?, updater_id=? WHERE id=? AND version=? AND deleted=0
==> Parameters: 单元测试部门1751290523375(String), TEST_DEPT_1751290523375(String), 1(Integer), /1939679157817016321(String), 这是一个测试部门(String), 1(Integer), INACTIVE(String), 2(Integer), 2025-06-30T21:35:23.376790(LocalDateTime), 2025-06-30T21:35:23.377525(LocalDateTime), 1(Long), 1(Long), 1939679157817016321(Long), 1(Integer)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7668b4ff]
2025-06-30T21:35:23.378+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门禁用成功: deptId=1939679157817016321
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7668b4ff] from current transaction
==>  Preparing: SELECT d.*, u.id AS leader_user_id, u.username AS leader_username, u.real_name AS leader_real_name, u.email AS leader_email, u.phone AS leader_phone FROM sys_departments d LEFT JOIN sys_users u ON d.leader_id = u.id AND u.deleted = 0 WHERE d.id = ? AND d.deleted = 0
==> Parameters: 1939679157817016321(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, TENANT_ID, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK, LEADER_USER_ID, LEADER_USERNAME, LEADER_REAL_NAME, LEADER_EMAIL, LEADER_PHONE
<==        Row: 1939679157817016321, 单元测试部门1751290523375, TEST_DEPT_1751290523375, null, 1, /1939679157817016321, 这是一个测试部门, null, null, null, 1, INACTIVE, 2, 0, 1, 2025-06-30 21:35:23.37679, 2025-06-30 21:35:23.377525, 1, 1, <<BLOB>>, null, null, null, null, null, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7668b4ff]
2025-06-30T21:35:23.379+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 启用部门: deptId=1939679157817016321
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7668b4ff] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157817016321(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157817016321, 单元测试部门1751290523375, TEST_DEPT_1751290523375, null, 1, /1939679157817016321, 这是一个测试部门, null, null, null, 1, INACTIVE, 2, 0, 2025-06-30 21:35:23.37679, 2025-06-30 21:35:23.377525, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7668b4ff]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7668b4ff] from current transaction
2025-06-30T21:35:23.379+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始更新填充...
2025-06-30T21:35:23.379+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 更新填充完成
==>  Preparing: UPDATE sys_departments SET dept_name=?, dept_code=?, dept_level=?, dept_path=?, description=?, sort_order=?, status=?, version=?, create_time=?, update_time=?, creator_id=?, updater_id=? WHERE id=? AND version=? AND deleted=0
==> Parameters: 单元测试部门1751290523375(String), TEST_DEPT_1751290523375(String), 1(Integer), /1939679157817016321(String), 这是一个测试部门(String), 1(Integer), ACTIVE(String), 3(Integer), 2025-06-30T21:35:23.376790(LocalDateTime), 2025-06-30T21:35:23.377525(LocalDateTime), 1(Long), 1(Long), 1939679157817016321(Long), 2(Integer)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7668b4ff]
2025-06-30T21:35:23.380+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门启用成功: deptId=1939679157817016321
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7668b4ff] from current transaction
==>  Preparing: SELECT d.*, u.id AS leader_user_id, u.username AS leader_username, u.real_name AS leader_real_name, u.email AS leader_email, u.phone AS leader_phone FROM sys_departments d LEFT JOIN sys_users u ON d.leader_id = u.id AND u.deleted = 0 WHERE d.id = ? AND d.deleted = 0
==> Parameters: 1939679157817016321(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, TENANT_ID, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK, LEADER_USER_ID, LEADER_USERNAME, LEADER_REAL_NAME, LEADER_EMAIL, LEADER_PHONE
<==        Row: 1939679157817016321, 单元测试部门1751290523375, TEST_DEPT_1751290523375, null, 1, /1939679157817016321, 这是一个测试部门, null, null, null, 1, ACTIVE, 3, 0, 1, 2025-06-30 21:35:23.37679, 2025-06-30 21:35:23.377525, 1, 1, <<BLOB>>, null, null, null, null, null, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7668b4ff]
2025-06-30T21:35:23.380+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 233, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.380+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 234, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.381+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 235, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.381+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 236, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.381+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 237, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.381+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 238, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.381+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 239, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.381+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 240, missCount = 2, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7668b4ff]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7668b4ff]
2025-06-30T21:35:23.381+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testActivateAndDeactivateDepartment]
2025-06-30T21:35:23.381+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [DepartmentServiceImplTest], method [testActivateAndDeactivateDepartment], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.381+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
]]></system-out>
  </testcase>
  <testcase name="testSetDepartmentLeader" classname="com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest" time="0.005">
    <system-out><![CDATA[2025-06-30T21:35:23.381+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 241, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.381+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.381+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.381+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 242, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.382+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 243, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.382+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [DepartmentServiceImplTest], method [testSetDepartmentLeader], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.382+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest] and test method [testSetDepartmentLeader]
2025-06-30T21:35:23.382+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 244, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.382+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest.testSetDepartmentLeader()]
2025-06-30T21:35:23.382+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testSetDepartmentLeader]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@5b265379]; rollback [true]
2025-06-30T21:35:23.382+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 245, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.382+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 246, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.382+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 247, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.382+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 248, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.382+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 创建部门: deptName=单元测试部门1751290523382, deptCode=TEST_DEPT_1751290523382
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@299a0651]
JDBC Connection [HikariProxyConnection@2127132683 wrapping conn2: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_code = ? AND deleted = 0
==> Parameters: TEST_DEPT_1751290523382(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@299a0651]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@299a0651] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_name = ? AND deleted = 0 AND parent_id IS NULL
==> Parameters: 单元测试部门1751290523382(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@299a0651]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@299a0651] from current transaction
2025-06-30T21:35:23.383+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:23.383+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_departments ( id, dept_name, dept_code, dept_level, description, sort_order, status, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679157846376449(Long), 单元测试部门1751290523382(String), TEST_DEPT_1751290523382(String), 1(Integer), 这是一个测试部门(String), 1(Integer), ACTIVE(String), 2025-06-30T21:35:23.383624(LocalDateTime), 2025-06-30T21:35:23.383634(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@299a0651]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@299a0651] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157846376449(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157846376449, 单元测试部门1751290523382, TEST_DEPT_1751290523382, null, 1, null, 这是一个测试部门, null, null, null, 1, ACTIVE, 0, 0, 2025-06-30 21:35:23.383624, 2025-06-30 21:35:23.383634, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@299a0651]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@299a0651] from current transaction
==>  Preparing: UPDATE sys_departments SET dept_path = ?, update_time = NOW(), version = version + 1 WHERE id = ? AND deleted = 0
==> Parameters: /1939679157846376449(String), 1939679157846376449(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@299a0651]
2025-06-30T21:35:23.384+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门创建成功: deptId=1939679157846376449
2025-06-30T21:35:23.384+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 设置部门负责人: deptId=1939679157846376449, leaderId=1
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@299a0651] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157846376449(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157846376449, 单元测试部门1751290523382, TEST_DEPT_1751290523382, null, 1, /1939679157846376449, 这是一个测试部门, null, null, null, 1, ACTIVE, 1, 0, 2025-06-30 21:35:23.383624, 2025-06-30 21:35:23.384352, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@299a0651]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@299a0651] from current transaction
2025-06-30T21:35:23.385+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始更新填充...
2025-06-30T21:35:23.385+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 更新填充完成
==>  Preparing: UPDATE sys_departments SET dept_name=?, dept_code=?, dept_level=?, dept_path=?, description=?, leader_id=?, sort_order=?, status=?, version=?, create_time=?, update_time=?, creator_id=?, updater_id=? WHERE id=? AND version=? AND deleted=0
==> Parameters: 单元测试部门1751290523382(String), TEST_DEPT_1751290523382(String), 1(Integer), /1939679157846376449(String), 这是一个测试部门(String), 1(Long), 1(Integer), ACTIVE(String), 2(Integer), 2025-06-30T21:35:23.383624(LocalDateTime), 2025-06-30T21:35:23.384352(LocalDateTime), 1(Long), 1(Long), 1939679157846376449(Long), 1(Integer)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@299a0651]
2025-06-30T21:35:23.385+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门负责人设置成功: deptId=1939679157846376449, leaderId=1
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@299a0651] from current transaction
==>  Preparing: SELECT d.*, u.id AS leader_user_id, u.username AS leader_username, u.real_name AS leader_real_name, u.email AS leader_email, u.phone AS leader_phone FROM sys_departments d LEFT JOIN sys_users u ON d.leader_id = u.id AND u.deleted = 0 WHERE d.id = ? AND d.deleted = 0
==> Parameters: 1939679157846376449(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, TENANT_ID, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK, LEADER_USER_ID, LEADER_USERNAME, LEADER_REAL_NAME, LEADER_EMAIL, LEADER_PHONE
<==        Row: 1939679157846376449, 单元测试部门1751290523382, TEST_DEPT_1751290523382, null, 1, /1939679157846376449, 这是一个测试部门, 1, null, null, 1, ACTIVE, 2, 0, 1, 2025-06-30 21:35:23.383624, 2025-06-30 21:35:23.384352, 1, 1, <<BLOB>>, null, null, null, null, null, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@299a0651]
2025-06-30T21:35:23.386+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 249, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.386+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 250, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.386+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 251, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.386+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 252, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.386+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 253, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.386+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 254, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.386+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 255, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.386+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 256, missCount = 2, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@299a0651]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@299a0651]
2025-06-30T21:35:23.387+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testSetDepartmentLeader]
2025-06-30T21:35:23.387+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [DepartmentServiceImplTest], method [testSetDepartmentLeader], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.387+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
]]></system-out>
  </testcase>
  <testcase name="testCreateDepartmentWithDuplicateCode" classname="com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest" time="0.005">
    <system-out><![CDATA[2025-06-30T21:35:23.387+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 257, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.387+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.387+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.387+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 258, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.387+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 259, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.387+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [DepartmentServiceImplTest], method [testCreateDepartmentWithDuplicateCode], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.387+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest] and test method [testCreateDepartmentWithDuplicateCode]
2025-06-30T21:35:23.387+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 260, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.387+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest.testCreateDepartmentWithDuplicateCode()]
2025-06-30T21:35:23.388+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testCreateDepartmentWithDuplicateCode]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@5b265379]; rollback [true]
2025-06-30T21:35:23.388+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 261, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.388+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 262, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.388+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 263, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.388+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 264, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.388+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 创建部门: deptName=单元测试部门1751290523388, deptCode=TEST_DEPT_1751290523388
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3c88bef5]
JDBC Connection [HikariProxyConnection@1758519413 wrapping conn2: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_code = ? AND deleted = 0
==> Parameters: TEST_DEPT_1751290523388(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3c88bef5]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3c88bef5] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_name = ? AND deleted = 0 AND parent_id IS NULL
==> Parameters: 单元测试部门1751290523388(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3c88bef5]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3c88bef5] from current transaction
2025-06-30T21:35:23.390+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:23.390+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_departments ( id, dept_name, dept_code, dept_level, description, sort_order, status, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679157871542273(Long), 单元测试部门1751290523388(String), TEST_DEPT_1751290523388(String), 1(Integer), 这是一个测试部门(String), 1(Integer), ACTIVE(String), 2025-06-30T21:35:23.390230(LocalDateTime), 2025-06-30T21:35:23.390242(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3c88bef5]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3c88bef5] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157871542273(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157871542273, 单元测试部门1751290523388, TEST_DEPT_1751290523388, null, 1, null, 这是一个测试部门, null, null, null, 1, ACTIVE, 0, 0, 2025-06-30 21:35:23.39023, 2025-06-30 21:35:23.390242, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3c88bef5]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3c88bef5] from current transaction
==>  Preparing: UPDATE sys_departments SET dept_path = ?, update_time = NOW(), version = version + 1 WHERE id = ? AND deleted = 0
==> Parameters: /1939679157871542273(String), 1939679157871542273(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3c88bef5]
2025-06-30T21:35:23.390+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门创建成功: deptId=1939679157871542273
2025-06-30T21:35:23.391+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 创建部门: deptName=重复代码部门1751290523391, deptCode=TEST_DEPT_1751290523388
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3c88bef5] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_code = ? AND deleted = 0
==> Parameters: TEST_DEPT_1751290523388(String)
<==    Columns: ?column?
<==        Row: 1
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3c88bef5]
2025-06-30T21:35:23.391+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 265, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.391+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 266, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.391+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 267, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.391+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 268, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.391+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 269, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.391+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 270, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.392+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 271, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.392+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 272, missCount = 2, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3c88bef5]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3c88bef5]
2025-06-30T21:35:23.392+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testCreateDepartmentWithDuplicateCode]
2025-06-30T21:35:23.392+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [DepartmentServiceImplTest], method [testCreateDepartmentWithDuplicateCode], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.392+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
]]></system-out>
  </testcase>
  <testcase name="testMoveDepartment" classname="com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest" time="0.012">
    <system-out><![CDATA[2025-06-30T21:35:23.392+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 273, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.392+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.392+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.392+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 274, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.392+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 275, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.392+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [DepartmentServiceImplTest], method [testMoveDepartment], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.392+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest] and test method [testMoveDepartment]
2025-06-30T21:35:23.392+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 276, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.392+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest.testMoveDepartment()]
2025-06-30T21:35:23.392+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testMoveDepartment]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@5b265379]; rollback [true]
2025-06-30T21:35:23.392+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 277, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.392+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 278, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.393+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 279, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.393+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 280, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.393+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 创建部门: deptName=单元测试部门1751290523393, deptCode=TEST_DEPT_1751290523393
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
JDBC Connection [HikariProxyConnection@1795671240 wrapping conn2: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_code = ? AND deleted = 0
==> Parameters: TEST_DEPT_1751290523393(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_name = ? AND deleted = 0 AND parent_id IS NULL
==> Parameters: 单元测试部门1751290523393(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a] from current transaction
2025-06-30T21:35:23.394+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:23.394+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_departments ( id, dept_name, dept_code, dept_level, description, sort_order, status, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679157888319490(Long), 单元测试部门1751290523393(String), TEST_DEPT_1751290523393(String), 1(Integer), 这是一个测试部门(String), 1(Integer), ACTIVE(String), 2025-06-30T21:35:23.394467(LocalDateTime), 2025-06-30T21:35:23.394483(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157888319490(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157888319490, 单元测试部门1751290523393, TEST_DEPT_1751290523393, null, 1, null, 这是一个测试部门, null, null, null, 1, ACTIVE, 0, 0, 2025-06-30 21:35:23.394467, 2025-06-30 21:35:23.394483, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a] from current transaction
==>  Preparing: UPDATE sys_departments SET dept_path = ?, update_time = NOW(), version = version + 1 WHERE id = ? AND deleted = 0
==> Parameters: /1939679157888319490(String), 1939679157888319490(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
2025-06-30T21:35:23.395+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门创建成功: deptId=1939679157888319490
2025-06-30T21:35:23.395+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 创建部门: deptName=子部门1751290523395, deptCode=SUB_DEPT_1751290523395
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_code = ? AND deleted = 0
==> Parameters: SUB_DEPT_1751290523395(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_name = ? AND deleted = 0 AND parent_id = ?
==> Parameters: 子部门1751290523395(String), 1939679157888319490(Long)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157888319490(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157888319490, 单元测试部门1751290523393, TEST_DEPT_1751290523393, null, 1, /1939679157888319490, 这是一个测试部门, null, null, null, 1, ACTIVE, 1, 0, 2025-06-30 21:35:23.394467, 2025-06-30 21:35:23.395197, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a] from current transaction
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a] from current transaction
2025-06-30T21:35:23.396+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:23.396+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_departments ( id, dept_name, dept_code, parent_id, dept_level, sort_order, status, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679157896708098(Long), 子部门1751290523395(String), SUB_DEPT_1751290523395(String), 1939679157888319490(Long), 2(Integer), 0(Integer), ACTIVE(String), 2025-06-30T21:35:23.396135(LocalDateTime), 2025-06-30T21:35:23.396145(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157896708098(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157896708098, 子部门1751290523395, SUB_DEPT_1751290523395, 1939679157888319490, 2, null, null, null, null, null, 0, ACTIVE, 0, 0, 2025-06-30 21:35:23.396135, 2025-06-30 21:35:23.396145, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157888319490(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157888319490, 单元测试部门1751290523393, TEST_DEPT_1751290523393, null, 1, /1939679157888319490, 这是一个测试部门, null, null, null, 1, ACTIVE, 1, 0, 2025-06-30 21:35:23.394467, 2025-06-30 21:35:23.395197, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a] from current transaction
==>  Preparing: UPDATE sys_departments SET dept_path = ?, update_time = NOW(), version = version + 1 WHERE id = ? AND deleted = 0
==> Parameters: /1939679157888319490/1939679157896708098(String), 1939679157896708098(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
2025-06-30T21:35:23.397+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门创建成功: deptId=1939679157896708098
2025-06-30T21:35:23.397+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 创建部门: deptName=新父部门1751290523395, deptCode=NEW_PARENT_1751290523395
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_code = ? AND deleted = 0
==> Parameters: NEW_PARENT_1751290523395(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_name = ? AND deleted = 0 AND parent_id IS NULL
==> Parameters: 新父部门1751290523395(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a] from current transaction
2025-06-30T21:35:23.397+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:23.398+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_departments ( id, dept_name, dept_code, dept_level, sort_order, status, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679157905096705(Long), 新父部门1751290523395(String), NEW_PARENT_1751290523395(String), 1(Integer), 0(Integer), ACTIVE(String), 2025-06-30T21:35:23.397894(LocalDateTime), 2025-06-30T21:35:23.397907(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157905096705(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157905096705, 新父部门1751290523395, NEW_PARENT_1751290523395, null, 1, null, null, null, null, null, 0, ACTIVE, 0, 0, 2025-06-30 21:35:23.397894, 2025-06-30 21:35:23.397907, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a] from current transaction
==>  Preparing: UPDATE sys_departments SET dept_path = ?, update_time = NOW(), version = version + 1 WHERE id = ? AND deleted = 0
==> Parameters: /1939679157905096705(String), 1939679157905096705(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
2025-06-30T21:35:23.398+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门创建成功: deptId=1939679157905096705
2025-06-30T21:35:23.398+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 移动部门: deptId=1939679157896708098, newParentId=1939679157905096705
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157896708098(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157896708098, 子部门1751290523395, SUB_DEPT_1751290523395, 1939679157888319490, 2, /1939679157888319490/1939679157896708098, null, null, null, null, 0, ACTIVE, 1, 0, 2025-06-30 21:35:23.396135, 2025-06-30 21:35:23.395197, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a] from current transaction
==>  Preparing: SELECT id FROM sys_departments WHERE parent_id = ? AND deleted = 0
==> Parameters: 1939679157896708098(Long)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a] from current transaction
2025-06-30T21:35:23.399+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始更新填充...
2025-06-30T21:35:23.399+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 更新填充完成
==>  Preparing: UPDATE sys_departments SET dept_name=?, dept_code=?, parent_id=?, dept_level=?, dept_path=?, sort_order=?, status=?, version=?, create_time=?, update_time=?, creator_id=?, updater_id=? WHERE id=? AND version=? AND deleted=0
==> Parameters: 子部门1751290523395(String), SUB_DEPT_1751290523395(String), 1939679157905096705(Long), 2(Integer), /1939679157888319490/1939679157896708098(String), 0(Integer), ACTIVE(String), 2(Integer), 2025-06-30T21:35:23.396135(LocalDateTime), 2025-06-30T21:35:23.395197(LocalDateTime), 1(Long), 1(Long), 1939679157896708098(Long), 1(Integer)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157896708098(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157896708098, 子部门1751290523395, SUB_DEPT_1751290523395, 1939679157905096705, 2, /1939679157888319490/1939679157896708098, null, null, null, null, 0, ACTIVE, 2, 0, 2025-06-30 21:35:23.396135, 2025-06-30 21:35:23.395197, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157905096705(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157905096705, 新父部门1751290523395, NEW_PARENT_1751290523395, null, 1, /1939679157905096705, null, null, null, null, 0, ACTIVE, 1, 0, 2025-06-30 21:35:23.397894, 2025-06-30 21:35:23.395197, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a] from current transaction
2025-06-30T21:35:23.400+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始更新填充...
2025-06-30T21:35:23.400+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 更新填充完成
==>  Preparing: UPDATE sys_departments SET dept_name=?, dept_code=?, parent_id=?, dept_level=?, dept_path=?, sort_order=?, status=?, version=?, create_time=?, update_time=?, creator_id=?, updater_id=? WHERE id=? AND version=? AND deleted=0
==> Parameters: 子部门1751290523395(String), SUB_DEPT_1751290523395(String), 1939679157905096705(Long), 2(Integer), /1939679157888319490/1939679157896708098(String), 0(Integer), ACTIVE(String), 3(Integer), 2025-06-30T21:35:23.396135(LocalDateTime), 2025-06-30T21:35:23.395197(LocalDateTime), 1(Long), 1(Long), 1939679157896708098(Long), 2(Integer)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157896708098(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157896708098, 子部门1751290523395, SUB_DEPT_1751290523395, 1939679157905096705, 2, /1939679157888319490/1939679157896708098, null, null, null, null, 0, ACTIVE, 3, 0, 2025-06-30 21:35:23.396135, 2025-06-30 21:35:23.395197, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157905096705(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157905096705, 新父部门1751290523395, NEW_PARENT_1751290523395, null, 1, /1939679157905096705, null, null, null, null, 0, ACTIVE, 1, 0, 2025-06-30 21:35:23.397894, 2025-06-30 21:35:23.395197, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a] from current transaction
==>  Preparing: UPDATE sys_departments SET dept_path = ?, update_time = NOW(), version = version + 1 WHERE id = ? AND deleted = 0
==> Parameters: /1939679157905096705/1939679157896708098(String), 1939679157896708098(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a] from current transaction
==>  Preparing: SELECT * FROM sys_departments WHERE parent_id = ? AND deleted = 0 ORDER BY sort_order ASC, create_time ASC
==> Parameters: 1939679157896708098(Long)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
2025-06-30T21:35:23.402+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门移动成功: deptId=1939679157896708098, newParentId=1939679157905096705
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a] from current transaction
==>  Preparing: SELECT d.*, u.id AS leader_user_id, u.username AS leader_username, u.real_name AS leader_real_name, u.email AS leader_email, u.phone AS leader_phone FROM sys_departments d LEFT JOIN sys_users u ON d.leader_id = u.id AND u.deleted = 0 WHERE d.id = ? AND d.deleted = 0
==> Parameters: 1939679157896708098(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, TENANT_ID, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK, LEADER_USER_ID, LEADER_USERNAME, LEADER_REAL_NAME, LEADER_EMAIL, LEADER_PHONE
<==        Row: 1939679157896708098, 子部门1751290523395, SUB_DEPT_1751290523395, 1939679157905096705, 2, /1939679157905096705/1939679157896708098, null, null, null, null, 0, ACTIVE, 4, 0, 1, 2025-06-30 21:35:23.396135, 2025-06-30 21:35:23.395197, 1, 1, <<BLOB>>, null, null, null, null, null, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
2025-06-30T21:35:23.402+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 281, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.402+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 282, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.403+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 283, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.403+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 284, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.403+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 285, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.403+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 286, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.403+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 287, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.403+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 288, missCount = 2, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c4d33a]
2025-06-30T21:35:23.403+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testMoveDepartment]
2025-06-30T21:35:23.403+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [DepartmentServiceImplTest], method [testMoveDepartment], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.403+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
]]></system-out>
  </testcase>
  <testcase name="testCreateSubDepartment" classname="com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest" time="0.005">
    <system-out><![CDATA[2025-06-30T21:35:23.404+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 289, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.404+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.404+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.404+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 290, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.404+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 291, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.404+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [DepartmentServiceImplTest], method [testCreateSubDepartment], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.404+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest] and test method [testCreateSubDepartment]
2025-06-30T21:35:23.404+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 292, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.404+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest.testCreateSubDepartment()]
2025-06-30T21:35:23.404+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testCreateSubDepartment]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@5b265379]; rollback [true]
2025-06-30T21:35:23.404+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 293, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.404+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 294, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.405+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 295, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.405+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 296, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.405+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 创建部门: deptName=单元测试部门1751290523405, deptCode=TEST_DEPT_1751290523405
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79e686ab]
JDBC Connection [HikariProxyConnection@143791945 wrapping conn2: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_code = ? AND deleted = 0
==> Parameters: TEST_DEPT_1751290523405(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79e686ab]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79e686ab] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_name = ? AND deleted = 0 AND parent_id IS NULL
==> Parameters: 单元测试部门1751290523405(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79e686ab]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79e686ab] from current transaction
2025-06-30T21:35:23.406+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:23.406+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_departments ( id, dept_name, dept_code, dept_level, description, sort_order, status, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679157938651138(Long), 单元测试部门1751290523405(String), TEST_DEPT_1751290523405(String), 1(Integer), 这是一个测试部门(String), 1(Integer), ACTIVE(String), 2025-06-30T21:35:23.406227(LocalDateTime), 2025-06-30T21:35:23.406238(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79e686ab]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79e686ab] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157938651138(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157938651138, 单元测试部门1751290523405, TEST_DEPT_1751290523405, null, 1, null, 这是一个测试部门, null, null, null, 1, ACTIVE, 0, 0, 2025-06-30 21:35:23.406227, 2025-06-30 21:35:23.406238, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79e686ab]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79e686ab] from current transaction
==>  Preparing: UPDATE sys_departments SET dept_path = ?, update_time = NOW(), version = version + 1 WHERE id = ? AND deleted = 0
==> Parameters: /1939679157938651138(String), 1939679157938651138(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79e686ab]
2025-06-30T21:35:23.406+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门创建成功: deptId=1939679157938651138
2025-06-30T21:35:23.407+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 创建部门: deptName=子部门1751290523407, deptCode=SUB_DEPT_1751290523407
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79e686ab] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_code = ? AND deleted = 0
==> Parameters: SUB_DEPT_1751290523407(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79e686ab]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79e686ab] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_name = ? AND deleted = 0 AND parent_id = ?
==> Parameters: 子部门1751290523407(String), 1939679157938651138(Long)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79e686ab]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79e686ab] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157938651138(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157938651138, 单元测试部门1751290523405, TEST_DEPT_1751290523405, null, 1, /1939679157938651138, 这是一个测试部门, null, null, null, 1, ACTIVE, 1, 0, 2025-06-30 21:35:23.406227, 2025-06-30 21:35:23.406859, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79e686ab]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79e686ab] from current transaction
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79e686ab]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79e686ab] from current transaction
2025-06-30T21:35:23.408+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:23.408+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_departments ( id, dept_name, dept_code, parent_id, dept_level, sort_order, status, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679157947039745(Long), 子部门1751290523407(String), SUB_DEPT_1751290523407(String), 1939679157938651138(Long), 2(Integer), 0(Integer), ACTIVE(String), 2025-06-30T21:35:23.408100(LocalDateTime), 2025-06-30T21:35:23.408110(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79e686ab]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79e686ab] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157947039745(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157947039745, 子部门1751290523407, SUB_DEPT_1751290523407, 1939679157938651138, 2, null, null, null, null, null, 0, ACTIVE, 0, 0, 2025-06-30 21:35:23.4081, 2025-06-30 21:35:23.40811, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79e686ab]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79e686ab] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157938651138(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157938651138, 单元测试部门1751290523405, TEST_DEPT_1751290523405, null, 1, /1939679157938651138, 这是一个测试部门, null, null, null, 1, ACTIVE, 1, 0, 2025-06-30 21:35:23.406227, 2025-06-30 21:35:23.406859, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79e686ab]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79e686ab] from current transaction
==>  Preparing: UPDATE sys_departments SET dept_path = ?, update_time = NOW(), version = version + 1 WHERE id = ? AND deleted = 0
==> Parameters: /1939679157938651138/1939679157947039745(String), 1939679157947039745(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79e686ab]
2025-06-30T21:35:23.408+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门创建成功: deptId=1939679157947039745
2025-06-30T21:35:23.408+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 297, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.409+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 298, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.409+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 299, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.409+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 300, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.409+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 301, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.409+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 302, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.409+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 303, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.409+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 304, missCount = 2, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79e686ab]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79e686ab]
2025-06-30T21:35:23.409+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testCreateSubDepartment]
2025-06-30T21:35:23.409+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [DepartmentServiceImplTest], method [testCreateSubDepartment], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.409+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
]]></system-out>
  </testcase>
  <testcase name="testGetDepartmentById" classname="com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest" time="0.004">
    <system-out><![CDATA[2025-06-30T21:35:23.409+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 305, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.409+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.409+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.409+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 306, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.409+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 307, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.410+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [DepartmentServiceImplTest], method [testGetDepartmentById], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.410+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest] and test method [testGetDepartmentById]
2025-06-30T21:35:23.410+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 308, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.410+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest.testGetDepartmentById()]
2025-06-30T21:35:23.410+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testGetDepartmentById]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@5b265379]; rollback [true]
2025-06-30T21:35:23.410+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 309, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.410+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 310, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.410+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 311, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.410+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 312, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.410+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 创建部门: deptName=单元测试部门1751290523410, deptCode=TEST_DEPT_1751290523410
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1b5496fd]
JDBC Connection [HikariProxyConnection@1797731899 wrapping conn2: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_code = ? AND deleted = 0
==> Parameters: TEST_DEPT_1751290523410(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1b5496fd]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1b5496fd] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_name = ? AND deleted = 0 AND parent_id IS NULL
==> Parameters: 单元测试部门1751290523410(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1b5496fd]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1b5496fd] from current transaction
2025-06-30T21:35:23.411+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:23.411+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_departments ( id, dept_name, dept_code, dept_level, description, sort_order, status, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679157963816961(Long), 单元测试部门1751290523410(String), TEST_DEPT_1751290523410(String), 1(Integer), 这是一个测试部门(String), 1(Integer), ACTIVE(String), 2025-06-30T21:35:23.411544(LocalDateTime), 2025-06-30T21:35:23.411559(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1b5496fd]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1b5496fd] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157963816961(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157963816961, 单元测试部门1751290523410, TEST_DEPT_1751290523410, null, 1, null, 这是一个测试部门, null, null, null, 1, ACTIVE, 0, 0, 2025-06-30 21:35:23.411544, 2025-06-30 21:35:23.411559, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1b5496fd]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1b5496fd] from current transaction
==>  Preparing: UPDATE sys_departments SET dept_path = ?, update_time = NOW(), version = version + 1 WHERE id = ? AND deleted = 0
==> Parameters: /1939679157963816961(String), 1939679157963816961(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1b5496fd]
2025-06-30T21:35:23.412+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门创建成功: deptId=1939679157963816961
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1b5496fd] from current transaction
==>  Preparing: SELECT d.*, u.id AS leader_user_id, u.username AS leader_username, u.real_name AS leader_real_name, u.email AS leader_email, u.phone AS leader_phone FROM sys_departments d LEFT JOIN sys_users u ON d.leader_id = u.id AND u.deleted = 0 WHERE d.id = ? AND d.deleted = 0
==> Parameters: 1939679157963816961(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, TENANT_ID, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK, LEADER_USER_ID, LEADER_USERNAME, LEADER_REAL_NAME, LEADER_EMAIL, LEADER_PHONE
<==        Row: 1939679157963816961, 单元测试部门1751290523410, TEST_DEPT_1751290523410, null, 1, /1939679157963816961, 这是一个测试部门, null, null, null, 1, ACTIVE, 1, 0, 1, 2025-06-30 21:35:23.411544, 2025-06-30 21:35:23.412347, 1, 1, <<BLOB>>, null, null, null, null, null, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1b5496fd]
2025-06-30T21:35:23.413+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 313, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.413+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 314, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.413+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 315, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.413+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 316, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.413+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 317, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.413+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 318, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.413+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 319, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.413+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 320, missCount = 2, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1b5496fd]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1b5496fd]
2025-06-30T21:35:23.413+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testGetDepartmentById]
2025-06-30T21:35:23.413+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [DepartmentServiceImplTest], method [testGetDepartmentById], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.413+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
]]></system-out>
  </testcase>
  <testcase name="testGetDepartmentPage" classname="com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest" time="0.009">
    <system-out><![CDATA[2025-06-30T21:35:23.414+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 321, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.414+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.414+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.414+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 322, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.414+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 323, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.414+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [DepartmentServiceImplTest], method [testGetDepartmentPage], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.414+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest] and test method [testGetDepartmentPage]
2025-06-30T21:35:23.414+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 324, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.414+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest.testGetDepartmentPage()]
2025-06-30T21:35:23.414+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testGetDepartmentPage]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@5b265379]; rollback [true]
2025-06-30T21:35:23.414+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 325, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.414+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 326, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.414+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 327, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.414+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 328, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.415+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 创建部门: deptName=测试部门1_1751290523414, deptCode=TEST_DEPT_1_1751290523414
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56b972a8]
JDBC Connection [HikariProxyConnection@1306085681 wrapping conn2: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_code = ? AND deleted = 0
==> Parameters: TEST_DEPT_1_1751290523414(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56b972a8]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56b972a8] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_name = ? AND deleted = 0 AND parent_id IS NULL
==> Parameters: 测试部门1_1751290523414(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56b972a8]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56b972a8] from current transaction
2025-06-30T21:35:23.415+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:23.415+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_departments ( id, dept_name, dept_code, dept_level, sort_order, status, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679157980594178(Long), 测试部门1_1751290523414(String), TEST_DEPT_1_1751290523414(String), 1(Integer), 0(Integer), ACTIVE(String), 2025-06-30T21:35:23.415766(LocalDateTime), 2025-06-30T21:35:23.415779(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56b972a8]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56b972a8] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157980594178(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157980594178, 测试部门1_1751290523414, TEST_DEPT_1_1751290523414, null, 1, null, null, null, null, null, 0, ACTIVE, 0, 0, 2025-06-30 21:35:23.415766, 2025-06-30 21:35:23.415779, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56b972a8]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56b972a8] from current transaction
==>  Preparing: UPDATE sys_departments SET dept_path = ?, update_time = NOW(), version = version + 1 WHERE id = ? AND deleted = 0
==> Parameters: /1939679157980594178(String), 1939679157980594178(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56b972a8]
2025-06-30T21:35:23.416+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门创建成功: deptId=1939679157980594178
2025-06-30T21:35:23.416+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 创建部门: deptName=测试部门2_1751290523414, deptCode=TEST_DEPT_2_1751290523414
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56b972a8] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_code = ? AND deleted = 0
==> Parameters: TEST_DEPT_2_1751290523414(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56b972a8]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56b972a8] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_name = ? AND deleted = 0 AND parent_id IS NULL
==> Parameters: 测试部门2_1751290523414(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56b972a8]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56b972a8] from current transaction
2025-06-30T21:35:23.417+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:23.417+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_departments ( id, dept_name, dept_code, dept_level, sort_order, status, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679157984788481(Long), 测试部门2_1751290523414(String), TEST_DEPT_2_1751290523414(String), 1(Integer), 0(Integer), ACTIVE(String), 2025-06-30T21:35:23.417186(LocalDateTime), 2025-06-30T21:35:23.417196(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56b972a8]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56b972a8] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679157984788481(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679157984788481, 测试部门2_1751290523414, TEST_DEPT_2_1751290523414, null, 1, null, null, null, null, null, 0, ACTIVE, 0, 0, 2025-06-30 21:35:23.417186, 2025-06-30 21:35:23.417196, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56b972a8]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56b972a8] from current transaction
==>  Preparing: UPDATE sys_departments SET dept_path = ?, update_time = NOW(), version = version + 1 WHERE id = ? AND deleted = 0
==> Parameters: /1939679157984788481(String), 1939679157984788481(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56b972a8]
2025-06-30T21:35:23.417+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门创建成功: deptId=1939679157984788481
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56b972a8] from current transaction
==>  Preparing: SELECT COUNT(*) AS total FROM sys_departments d WHERE d.deleted = 0
==> Parameters: 
<==    Columns: TOTAL
<==        Row: 4
<==      Total: 1
==>  Preparing: SELECT d.*, u.id AS leader_user_id, u.username AS leader_username, u.real_name AS leader_real_name, u.email AS leader_email, u.phone AS leader_phone FROM sys_departments d LEFT JOIN sys_users u ON d.leader_id = u.id AND u.deleted = 0 WHERE d.deleted = 0 ORDER BY d.sort_order ASC, d.create_time DESC LIMIT ?
==> Parameters: 10(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, TENANT_ID, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK, LEADER_USER_ID, LEADER_USERNAME, LEADER_REAL_NAME, LEADER_EMAIL, LEADER_PHONE
<==        Row: 1939679157984788481, 测试部门2_1751290523414, TEST_DEPT_2_1751290523414, null, 1, /1939679157984788481, null, null, null, null, 0, ACTIVE, 1, 0, 1, 2025-06-30 21:35:23.417186, 2025-06-30 21:35:23.416418, 1, 1, <<BLOB>>, null, null, null, null, null, null
<==        Row: 1939679157980594178, 测试部门1_1751290523414, TEST_DEPT_1_1751290523414, null, 1, /1939679157980594178, null, null, null, null, 0, ACTIVE, 1, 0, 1, 2025-06-30 21:35:23.415766, 2025-06-30 21:35:23.416418, 1, 1, <<BLOB>>, null, null, null, null, null, null
<==        Row: 1, 测试部门1, TEST_DEPT_001, null, 1, null, null, null, null, null, 1, ACTIVE, 0, 0, 1, 2025-06-30 21:35:20.385232, 2025-06-30 21:35:20.385232, 0, 0, <<BLOB>>, null, null, null, null, null, null
<==        Row: 2, 测试部门2, TEST_DEPT_002, null, 1, null, null, null, null, null, 2, ACTIVE, 0, 0, 1, 2025-06-30 21:35:20.385232, 2025-06-30 21:35:20.385232, 0, 0, <<BLOB>>, null, null, null, null, null, null
<==      Total: 4
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56b972a8]
2025-06-30T21:35:23.422+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 329, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.422+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 330, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.422+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 331, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.422+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 332, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.422+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 333, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.423+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 334, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.423+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 335, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.423+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 336, missCount = 2, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56b972a8]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56b972a8]
2025-06-30T21:35:23.423+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testGetDepartmentPage]
2025-06-30T21:35:23.423+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [DepartmentServiceImplTest], method [testGetDepartmentPage], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.423+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
]]></system-out>
  </testcase>
  <testcase name="testGetDepartmentTree" classname="com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest" time="0.007">
    <system-out><![CDATA[2025-06-30T21:35:23.423+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 337, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.423+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.423+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.423+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 338, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.423+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 339, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.424+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [DepartmentServiceImplTest], method [testGetDepartmentTree], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.424+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest] and test method [testGetDepartmentTree]
2025-06-30T21:35:23.424+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 340, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.424+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest.testGetDepartmentTree()]
2025-06-30T21:35:23.424+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testGetDepartmentTree]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@5b265379]; rollback [true]
2025-06-30T21:35:23.424+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 341, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.424+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 342, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.424+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 343, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.424+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 344, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.424+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 创建部门: deptName=单元测试部门1751290523424, deptCode=TEST_DEPT_1751290523424
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f8d6d7d]
JDBC Connection [HikariProxyConnection@249798694 wrapping conn2: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_code = ? AND deleted = 0
==> Parameters: TEST_DEPT_1751290523424(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f8d6d7d]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f8d6d7d] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_name = ? AND deleted = 0 AND parent_id IS NULL
==> Parameters: 单元测试部门1751290523424(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f8d6d7d]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f8d6d7d] from current transaction
2025-06-30T21:35:23.425+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:23.425+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_departments ( id, dept_name, dept_code, dept_level, description, sort_order, status, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679158022537218(Long), 单元测试部门1751290523424(String), TEST_DEPT_1751290523424(String), 1(Integer), 这是一个测试部门(String), 1(Integer), ACTIVE(String), 2025-06-30T21:35:23.425765(LocalDateTime), 2025-06-30T21:35:23.425778(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f8d6d7d]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f8d6d7d] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679158022537218(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679158022537218, 单元测试部门1751290523424, TEST_DEPT_1751290523424, null, 1, null, 这是一个测试部门, null, null, null, 1, ACTIVE, 0, 0, 2025-06-30 21:35:23.425765, 2025-06-30 21:35:23.425778, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f8d6d7d]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f8d6d7d] from current transaction
==>  Preparing: UPDATE sys_departments SET dept_path = ?, update_time = NOW(), version = version + 1 WHERE id = ? AND deleted = 0
==> Parameters: /1939679158022537218(String), 1939679158022537218(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f8d6d7d]
2025-06-30T21:35:23.426+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门创建成功: deptId=1939679158022537218
2025-06-30T21:35:23.426+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 创建部门: deptName=子部门, deptCode=SUB_DEPT
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f8d6d7d] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_code = ? AND deleted = 0
==> Parameters: SUB_DEPT(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f8d6d7d]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f8d6d7d] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_name = ? AND deleted = 0 AND parent_id = ?
==> Parameters: 子部门(String), 1939679158022537218(Long)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f8d6d7d]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f8d6d7d] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679158022537218(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679158022537218, 单元测试部门1751290523424, TEST_DEPT_1751290523424, null, 1, /1939679158022537218, 这是一个测试部门, null, null, null, 1, ACTIVE, 1, 0, 2025-06-30 21:35:23.425765, 2025-06-30 21:35:23.426462, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f8d6d7d]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f8d6d7d] from current transaction
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f8d6d7d]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f8d6d7d] from current transaction
2025-06-30T21:35:23.428+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:23.428+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_departments ( id, dept_name, dept_code, parent_id, dept_level, sort_order, status, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679158030925825(Long), 子部门(String), SUB_DEPT(String), 1939679158022537218(Long), 2(Integer), 0(Integer), ACTIVE(String), 2025-06-30T21:35:23.428293(LocalDateTime), 2025-06-30T21:35:23.428304(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f8d6d7d]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f8d6d7d] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679158030925825(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679158030925825, 子部门, SUB_DEPT, 1939679158022537218, 2, null, null, null, null, null, 0, ACTIVE, 0, 0, 2025-06-30 21:35:23.428293, 2025-06-30 21:35:23.428304, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f8d6d7d]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f8d6d7d] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679158022537218(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679158022537218, 单元测试部门1751290523424, TEST_DEPT_1751290523424, null, 1, /1939679158022537218, 这是一个测试部门, null, null, null, 1, ACTIVE, 1, 0, 2025-06-30 21:35:23.425765, 2025-06-30 21:35:23.426462, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f8d6d7d]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f8d6d7d] from current transaction
==>  Preparing: UPDATE sys_departments SET dept_path = ?, update_time = NOW(), version = version + 1 WHERE id = ? AND deleted = 0
==> Parameters: /1939679158022537218/1939679158030925825(String), 1939679158030925825(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f8d6d7d]
2025-06-30T21:35:23.429+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门创建成功: deptId=1939679158030925825
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f8d6d7d] from current transaction
==>  Preparing: SELECT d.*, u.id AS leader_user_id, u.username AS leader_username, u.real_name AS leader_real_name, u.email AS leader_email, u.phone AS leader_phone FROM sys_departments d LEFT JOIN sys_users u ON d.leader_id = u.id AND u.deleted = 0 WHERE d.deleted = 0 AND d.status = 'ACTIVE' ORDER BY d.dept_level ASC, d.sort_order ASC, d.create_time ASC
==> Parameters: 
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, TENANT_ID, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK, LEADER_USER_ID, LEADER_USERNAME, LEADER_REAL_NAME, LEADER_EMAIL, LEADER_PHONE
<==        Row: 1, 测试部门1, TEST_DEPT_001, null, 1, null, null, null, null, null, 1, ACTIVE, 0, 0, 1, 2025-06-30 21:35:20.385232, 2025-06-30 21:35:20.385232, 0, 0, <<BLOB>>, null, null, null, null, null, null
<==        Row: 1939679158022537218, 单元测试部门1751290523424, TEST_DEPT_1751290523424, null, 1, /1939679158022537218, 这是一个测试部门, null, null, null, 1, ACTIVE, 1, 0, 1, 2025-06-30 21:35:23.425765, 2025-06-30 21:35:23.426462, 1, 1, <<BLOB>>, null, null, null, null, null, null
<==        Row: 2, 测试部门2, TEST_DEPT_002, null, 1, null, null, null, null, null, 2, ACTIVE, 0, 0, 1, 2025-06-30 21:35:20.385232, 2025-06-30 21:35:20.385232, 0, 0, <<BLOB>>, null, null, null, null, null, null
<==        Row: 1939679158030925825, 子部门, SUB_DEPT, 1939679158022537218, 2, /1939679158022537218/1939679158030925825, null, null, null, null, 0, ACTIVE, 1, 0, 1, 2025-06-30 21:35:23.428293, 2025-06-30 21:35:23.426462, 1, 1, <<BLOB>>, null, null, null, null, null, null
<==      Total: 4
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f8d6d7d]
2025-06-30T21:35:23.430+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 345, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.430+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 346, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.431+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 347, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.431+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 348, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.431+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 349, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.431+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 350, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.431+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 351, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.431+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 352, missCount = 2, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f8d6d7d]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f8d6d7d]
2025-06-30T21:35:23.431+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testGetDepartmentTree]
2025-06-30T21:35:23.431+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [DepartmentServiceImplTest], method [testGetDepartmentTree], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.431+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
]]></system-out>
  </testcase>
  <testcase name="testCreateDepartment" classname="com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest" time="0.003">
    <system-out><![CDATA[2025-06-30T21:35:23.432+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 353, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.432+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.432+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.432+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 354, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.432+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 355, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.432+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [DepartmentServiceImplTest], method [testCreateDepartment], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.432+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest] and test method [testCreateDepartment]
2025-06-30T21:35:23.432+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 356, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.432+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest.testCreateDepartment()]
2025-06-30T21:35:23.432+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testCreateDepartment]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@5b265379]; rollback [true]
2025-06-30T21:35:23.432+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 357, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.432+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 358, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.432+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 359, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.433+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 360, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.433+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 创建部门: deptName=单元测试部门1751290523432, deptCode=TEST_DEPT_1751290523432
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150f3678]
JDBC Connection [HikariProxyConnection@324269333 wrapping conn2: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_code = ? AND deleted = 0
==> Parameters: TEST_DEPT_1751290523432(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150f3678]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150f3678] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_name = ? AND deleted = 0 AND parent_id IS NULL
==> Parameters: 单元测试部门1751290523432(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150f3678]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150f3678] from current transaction
2025-06-30T21:35:23.434+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:23.434+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_departments ( id, dept_name, dept_code, dept_level, description, sort_order, status, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679158056091649(Long), 单元测试部门1751290523432(String), TEST_DEPT_1751290523432(String), 1(Integer), 这是一个测试部门(String), 1(Integer), ACTIVE(String), 2025-06-30T21:35:23.434057(LocalDateTime), 2025-06-30T21:35:23.434068(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150f3678]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150f3678] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679158056091649(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679158056091649, 单元测试部门1751290523432, TEST_DEPT_1751290523432, null, 1, null, 这是一个测试部门, null, null, null, 1, ACTIVE, 0, 0, 2025-06-30 21:35:23.434057, 2025-06-30 21:35:23.434068, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150f3678]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150f3678] from current transaction
==>  Preparing: UPDATE sys_departments SET dept_path = ?, update_time = NOW(), version = version + 1 WHERE id = ? AND deleted = 0
==> Parameters: /1939679158056091649(String), 1939679158056091649(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150f3678]
2025-06-30T21:35:23.434+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门创建成功: deptId=1939679158056091649
2025-06-30T21:35:23.434+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 361, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.434+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 362, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.435+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 363, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.435+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 364, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.435+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 365, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.435+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 366, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.435+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 367, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.435+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 368, missCount = 2, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150f3678]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150f3678]
2025-06-30T21:35:23.435+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testCreateDepartment]
2025-06-30T21:35:23.435+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [DepartmentServiceImplTest], method [testCreateDepartment], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.435+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
]]></system-out>
  </testcase>
  <testcase name="testGetDepartmentByCode" classname="com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest" time="0.004">
    <system-out><![CDATA[2025-06-30T21:35:23.435+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 369, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.435+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.436+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] DependencyInjectionTestExecutionListener : Performing dependency injection for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
2025-06-30T21:35:23.436+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 370, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.436+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 371, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.436+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] sContextBeforeModesTestExecutionListener : Before test method: class [DepartmentServiceImplTest], method [testGetDepartmentByCode], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.436+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : Explicit transaction definition [PROPAGATION_REQUIRED,ISOLATION_DEFAULT] found for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest] and test method [testGetDepartmentByCode]
2025-06-30T21:35:23.436+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 372, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.436+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] t.c.t.TransactionalTestExecutionListener : No method-level @Rollback override: using default rollback [true] for test method [void com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest.testGetDepartmentByCode()]
2025-06-30T21:35:23.436+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testGetDepartmentByCode]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@5b265379]; rollback [true]
2025-06-30T21:35:23.436+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 373, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.436+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 374, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.436+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 375, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.436+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 376, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.436+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 创建部门: deptName=单元测试部门1751290523436, deptCode=TEST_DEPT_1751290523436
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6ad11552]
JDBC Connection [HikariProxyConnection@1750016234 wrapping conn2: url=jdbc:h2:mem:testdb user=SA] will be managed by Spring
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_code = ? AND deleted = 0
==> Parameters: TEST_DEPT_1751290523436(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6ad11552]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6ad11552] from current transaction
==>  Preparing: SELECT COUNT(*) FROM sys_departments WHERE dept_name = ? AND deleted = 0 AND parent_id IS NULL
==> Parameters: 单元测试部门1751290523436(String)
<==    Columns: ?column?
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6ad11552]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6ad11552] from current transaction
2025-06-30T21:35:23.437+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 开始插入填充...
2025-06-30T21:35:23.437+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] c.b.e.p.c.w.h.PispMetaObjectHandler      : 插入填充完成
==>  Preparing: INSERT INTO sys_departments ( id, dept_name, dept_code, dept_level, description, sort_order, status, create_time, update_time, creator_id, updater_id ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1939679158072868866(Long), 单元测试部门1751290523436(String), TEST_DEPT_1751290523436(String), 1(Integer), 这是一个测试部门(String), 1(Integer), ACTIVE(String), 2025-06-30T21:35:23.437807(LocalDateTime), 2025-06-30T21:35:23.437819(LocalDateTime), 1(Long), 1(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6ad11552]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6ad11552] from current transaction
==>  Preparing: SELECT id,dept_name,dept_code,parent_id,dept_level,dept_path,description,leader_id,phone,email,sort_order,status,version,deleted,create_time,update_time,creator_id,updater_id,additional_info,remark FROM sys_departments WHERE id=? AND deleted=0
==> Parameters: 1939679158072868866(Long)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679158072868866, 单元测试部门1751290523436, TEST_DEPT_1751290523436, null, 1, null, 这是一个测试部门, null, null, null, 1, ACTIVE, 0, 0, 2025-06-30 21:35:23.437807, 2025-06-30 21:35:23.437819, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6ad11552]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6ad11552] from current transaction
==>  Preparing: UPDATE sys_departments SET dept_path = ?, update_time = NOW(), version = version + 1 WHERE id = ? AND deleted = 0
==> Parameters: /1939679158072868866(String), 1939679158072868866(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6ad11552]
2025-06-30T21:35:23.438+08:00  INFO 13932 --- [pisp-user-service-test] [           main] c.b.e.p.u.s.impl.DepartmentServiceImpl   : 部门创建成功: deptId=1939679158072868866
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6ad11552] from current transaction
==>  Preparing: SELECT * FROM sys_departments WHERE dept_code = ? AND deleted = 0
==> Parameters: TEST_DEPT_1751290523436(String)
<==    Columns: ID, DEPT_NAME, DEPT_CODE, PARENT_ID, DEPT_LEVEL, DEPT_PATH, DESCRIPTION, LEADER_ID, PHONE, EMAIL, SORT_ORDER, STATUS, VERSION, DELETED, TENANT_ID, CREATE_TIME, UPDATE_TIME, CREATOR_ID, UPDATER_ID, ADDITIONAL_INFO, REMARK
<==        Row: 1939679158072868866, 单元测试部门1751290523436, TEST_DEPT_1751290523436, null, 1, /1939679158072868866, 这是一个测试部门, null, null, null, 1, ACTIVE, 1, 0, 1, 2025-06-30 21:35:23.437807, 2025-06-30 21:35:23.438576, 1, 1, <<BLOB>>, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6ad11552]
2025-06-30T21:35:23.439+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 377, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.439+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 378, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.439+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 379, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.439+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 380, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.439+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 381, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.439+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 382, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.439+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 383, missCount = 2, failureCount = 0]
2025-06-30T21:35:23.439+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] org.springframework.test.context.cache   : Spring test ApplicationContext cache statistics: [DefaultContextCache@2ab1c7a9 size = 2, maxSize = 32, parentContextCount = 0, hitCount = 384, missCount = 2, failureCount = 0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6ad11552]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6ad11552]
2025-06-30T21:35:23.439+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction (1) for test class [com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest]; test method [testGetDepartmentByCode]
2025-06-30T21:35:23.439+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] .c.s.DirtiesContextTestExecutionListener : After test method: class [DepartmentServiceImplTest], method [testGetDepartmentByCode], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-30T21:35:23.440+08:00 DEBUG 13932 --- [pisp-user-service-test] [           main] o.s.t.c.w.ServletTestExecutionListener   : Resetting RequestContextHolder for test class com.bdyl.erp.pisp.user.service.impl.DepartmentServiceImplTest
]]></system-out>
  </testcase>
</testsuite>