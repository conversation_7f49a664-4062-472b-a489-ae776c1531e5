# 测试环境配置
spring:
  application:
    name: pisp-user-service-test

  # 数据源配置 - 使用H2内存数据库
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=PostgreSQL;DATABASE_TO_LOWER=TRUE;DEFAULT_NULL_ORDERING=HIGH
    username: sa
    password:
    hikari:
      minimum-idle: 1
      maximum-pool-size: 5

  # 启用Liquibase进行数据库初始化 - 使用测试专用changelog
  liquibase:
    enabled: true
    change-log: classpath:db/changelog/test-db.changelog-master.xml
    contexts: test

  # 禁用Redis
  data:
    redis:
      repositories:
        enabled: false

  # 禁用Nacos
  cloud:
    nacos:
      discovery:
        enabled: false
      config:
        enabled: false
    # 禁用服务发现
    discovery:
      enabled: false
    # 禁用配置中心
    config:
      enabled: false

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
    banner: false

# 日志配置
logging:
  level:
    root: WARN
    com.bdyl.erp.pisp: DEBUG
    org.springframework.test: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

# 禁用管理端点
management:
  endpoints:
    enabled-by-default: false

# 测试配置
pisp:
  user:
    password:
      strength: 4  # 测试环境使用较低强度
    cache:
      user-info-ttl: 60
      permission-ttl: 30
