<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.20.xsd">

    <!-- 创建部门表 - H2兼容版本 -->
    <changeSet id="test-001-create-departments-table" author="pisp-system">
        <createTable tableName="sys_departments">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="dept_name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="dept_code" type="VARCHAR(50)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="parent_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="dept_level" type="INTEGER" defaultValue="1">
                <constraints nullable="false"/>
            </column>
            <column name="dept_path" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
            <column name="description" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
            <column name="leader_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="phone" type="VARCHAR(20)">
                <constraints nullable="true"/>
            </column>
            <column name="email" type="VARCHAR(100)">
                <constraints nullable="true"/>
            </column>
            <column name="sort_order" type="INTEGER" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="VARCHAR(20)" defaultValue="ACTIVE">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="INTEGER" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="deleted" type="INTEGER" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="tenant_id" type="BIGINT" defaultValue="1">
                <constraints nullable="false"/>
            </column>
            <column name="create_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="update_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="creator_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="updater_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="additional_info" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="remark" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        
        <!-- 创建索引 -->
        <createIndex tableName="sys_departments" indexName="idx_dept_code">
            <column name="dept_code"/>
        </createIndex>
        <createIndex tableName="sys_departments" indexName="idx_dept_parent_id">
            <column name="parent_id"/>
        </createIndex>
        <createIndex tableName="sys_departments" indexName="idx_dept_status">
            <column name="status"/>
        </createIndex>
        <createIndex tableName="sys_departments" indexName="idx_dept_tenant_deleted">
            <column name="tenant_id"/>
            <column name="deleted"/>
        </createIndex>
    </changeSet>

    <!-- 创建用户表 - H2兼容版本 -->
    <changeSet id="test-002-create-users-table" author="pisp-system">
        <createTable tableName="sys_users">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="username" type="VARCHAR(50)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="password_hash" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="email" type="VARCHAR(100)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="phone" type="VARCHAR(20)">
                <constraints nullable="true"/>
            </column>
            <column name="real_name" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="avatar_url" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
            <column name="status" type="VARCHAR(20)" defaultValue="ACTIVE">
                <constraints nullable="false"/>
            </column>
            <column name="department_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="last_login_time" type="TIMESTAMP">
                <constraints nullable="true"/>
            </column>
            <column name="last_login_ip" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="login_count" type="INTEGER" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="INTEGER" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="deleted" type="INTEGER" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="tenant_id" type="BIGINT" defaultValue="1">
                <constraints nullable="false"/>
            </column>
            <column name="create_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="update_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="creator_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="updater_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="additional_info" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="remark" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        
        <!-- 创建索引 -->
        <createIndex tableName="sys_users" indexName="idx_user_username">
            <column name="username"/>
        </createIndex>
        <createIndex tableName="sys_users" indexName="idx_user_email">
            <column name="email"/>
        </createIndex>
        <createIndex tableName="sys_users" indexName="idx_user_phone">
            <column name="phone"/>
        </createIndex>
        <createIndex tableName="sys_users" indexName="idx_user_dept_id">
            <column name="department_id"/>
        </createIndex>
        <createIndex tableName="sys_users" indexName="idx_user_status">
            <column name="status"/>
        </createIndex>
        <createIndex tableName="sys_users" indexName="idx_user_tenant_deleted">
            <column name="tenant_id"/>
            <column name="deleted"/>
        </createIndex>
        
        <!-- 添加外键约束 -->
        <addForeignKeyConstraint
            baseTableName="sys_users"
            baseColumnNames="department_id"
            referencedTableName="sys_departments"
            referencedColumnNames="id"
            constraintName="fk_user_department"/>
    </changeSet>

    <!-- 插入测试基础数据 -->
    <changeSet id="test-003-insert-test-data" author="pisp-system">
        <!-- 插入测试部门数据 -->
        <insert tableName="sys_departments">
            <column name="id" value="1"/>
            <column name="dept_name" value="测试部门1"/>
            <column name="dept_code" value="TEST_DEPT_001"/>
            <column name="dept_level" value="1"/>
            <column name="sort_order" value="1"/>
            <column name="status" value="ACTIVE"/>
            <column name="version" value="0"/>
            <column name="deleted" value="0"/>
            <column name="tenant_id" value="1"/>
            <column name="creator_id" value="0"/>
            <column name="updater_id" value="0"/>
        </insert>

        <insert tableName="sys_departments">
            <column name="id" value="2"/>
            <column name="dept_name" value="测试部门2"/>
            <column name="dept_code" value="TEST_DEPT_002"/>
            <column name="dept_level" value="1"/>
            <column name="sort_order" value="2"/>
            <column name="status" value="ACTIVE"/>
            <column name="version" value="0"/>
            <column name="deleted" value="0"/>
            <column name="tenant_id" value="1"/>
            <column name="creator_id" value="0"/>
            <column name="updater_id" value="0"/>
        </insert>
    </changeSet>

</databaseChangeLog>
