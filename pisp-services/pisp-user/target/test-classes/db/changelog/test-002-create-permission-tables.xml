<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.20.xsd">

    <!-- 创建权限表 -->
    <changeSet id="test-004-create-permissions-table" author="pisp-system">
        <createTable tableName="sys_permissions">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false" />
            </column>
            <column name="permission_name" type="VARCHAR(50)">
                <constraints nullable="false" />
            </column>
            <column name="permission_code" type="VARCHAR(100)">
                <constraints nullable="false" unique="true" />
            </column>
            <column name="description" type="VARCHAR(200)">
                <constraints nullable="true" />
            </column>
            <column name="permission_type" type="VARCHAR(20)">
                <constraints nullable="false" />
            </column>
            <column name="parent_id" type="BIGINT">
                <constraints nullable="true" />
            </column>
            <column name="permission_path" type="VARCHAR(200)">
                <constraints nullable="true" />
            </column>
            <column name="sort_order" type="INTEGER" defaultValue="0">
                <constraints nullable="false" />
            </column>
            <column name="is_system" type="BOOLEAN" defaultValue="false">
                <constraints nullable="false" />
            </column>
            <column name="version" type="INTEGER" defaultValue="0">
                <constraints nullable="false" />
            </column>
            <column name="deleted" type="INTEGER" defaultValue="0">
                <constraints nullable="false" />
            </column>
            <column name="tenant_id" type="BIGINT" defaultValue="1">
                <constraints nullable="false" />
            </column>
            <column name="create_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false" />
            </column>
            <column name="update_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false" />
            </column>
            <column name="creator_id" type="BIGINT">
                <constraints nullable="true" />
            </column>
            <column name="updater_id" type="BIGINT">
                <constraints nullable="true" />
            </column>
            <column name="additional_info" type="TEXT">
                <constraints nullable="true" />
            </column>
            <column name="remark" type="VARCHAR(500)">
                <constraints nullable="true" />
            </column>
        </createTable>

        <!-- 创建索引 -->
        <createIndex tableName="sys_permissions" indexName="idx_permission_code">
            <column name="permission_code" />
        </createIndex>
        <createIndex tableName="sys_permissions" indexName="idx_permission_parent_id">
            <column name="parent_id" />
        </createIndex>
        <createIndex tableName="sys_permissions" indexName="idx_permission_type">
            <column name="permission_type" />
        </createIndex>
        <createIndex tableName="sys_permissions" indexName="idx_permission_tenant_deleted">
            <column name="tenant_id" />
            <column name="deleted" />
        </createIndex>
    </changeSet>

    <!-- 创建角色表 -->
    <changeSet id="test-005-create-roles-table" author="pisp-system">
        <createTable tableName="sys_roles">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false" />
            </column>
            <column name="role_name" type="VARCHAR(50)">
                <constraints nullable="false" />
            </column>
            <column name="role_code" type="VARCHAR(50)">
                <constraints nullable="false" unique="true" />
            </column>
            <column name="description" type="VARCHAR(200)">
                <constraints nullable="true" />
            </column>
            <column name="is_system" type="BOOLEAN" defaultValue="false">
                <constraints nullable="false" />
            </column>
            <column name="sort_order" type="INTEGER" defaultValue="0">
                <constraints nullable="false" />
            </column>
            <column name="status" type="VARCHAR(20)" defaultValue="ACTIVE">
                <constraints nullable="false" />
            </column>
            <column name="version" type="INTEGER" defaultValue="0">
                <constraints nullable="false" />
            </column>
            <column name="deleted" type="INTEGER" defaultValue="0">
                <constraints nullable="false" />
            </column>
            <column name="tenant_id" type="BIGINT" defaultValue="1">
                <constraints nullable="false" />
            </column>
            <column name="create_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false" />
            </column>
            <column name="update_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false" />
            </column>
            <column name="creator_id" type="BIGINT">
                <constraints nullable="true" />
            </column>
            <column name="updater_id" type="BIGINT">
                <constraints nullable="true" />
            </column>
            <column name="additional_info" type="TEXT">
                <constraints nullable="true" />
            </column>
            <column name="remark" type="VARCHAR(500)">
                <constraints nullable="true" />
            </column>
        </createTable>

        <!-- 创建索引 -->
        <createIndex tableName="sys_roles" indexName="idx_role_code">
            <column name="role_code" />
        </createIndex>
        <createIndex tableName="sys_roles" indexName="idx_role_status">
            <column name="status" />
        </createIndex>
        <createIndex tableName="sys_roles" indexName="idx_role_tenant_deleted">
            <column name="tenant_id" />
            <column name="deleted" />
        </createIndex>
    </changeSet>

    <!-- 创建角色权限关联表 -->
    <changeSet id="test-006-create-role-permissions-table" author="pisp-system">
        <createTable tableName="sys_role_permissions">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false" />
            </column>
            <column name="role_id" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="permission_id" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="assigned_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false" />
            </column>
            <column name="assigned_by" type="BIGINT">
                <constraints nullable="true" />
            </column>
            <column name="version" type="INTEGER" defaultValue="0">
                <constraints nullable="false" />
            </column>
            <column name="deleted" type="INTEGER" defaultValue="0">
                <constraints nullable="false" />
            </column>
            <column name="tenant_id" type="BIGINT" defaultValue="1">
                <constraints nullable="false" />
            </column>
            <column name="create_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false" />
            </column>
            <column name="update_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false" />
            </column>
            <column name="creator_id" type="BIGINT">
                <constraints nullable="true" />
            </column>
            <column name="updater_id" type="BIGINT">
                <constraints nullable="true" />
            </column>
            <column name="additional_info" type="TEXT">
                <constraints nullable="true" />
            </column>
            <column name="remark" type="VARCHAR(500)">
                <constraints nullable="true" />
            </column>
        </createTable>

        <!-- 创建索引 -->
        <createIndex tableName="sys_role_permissions" indexName="idx_role_permission_role_id">
            <column name="role_id" />
        </createIndex>
        <createIndex tableName="sys_role_permissions" indexName="idx_role_permission_permission_id">
            <column name="permission_id" />
        </createIndex>
        <createIndex tableName="sys_role_permissions" indexName="idx_role_permission_tenant_deleted">
            <column name="tenant_id" />
            <column name="deleted" />
        </createIndex>

        <!-- 添加外键约束 -->
        <addForeignKeyConstraint
            baseTableName="sys_role_permissions"
            baseColumnNames="role_id"
            referencedTableName="sys_roles"
            referencedColumnNames="id"
            constraintName="fk_role_permission_role" />
        <addForeignKeyConstraint
            baseTableName="sys_role_permissions"
            baseColumnNames="permission_id"
            referencedTableName="sys_permissions"
            referencedColumnNames="id"
            constraintName="fk_role_permission_permission" />
    </changeSet>

    <!-- 创建用户角色关联表 -->
    <changeSet id="test-007-create-user-roles-table" author="pisp-system">
        <createTable tableName="sys_user_roles">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false" />
            </column>
            <column name="user_id" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="role_id" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="assigned_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false" />
            </column>
            <column name="assigned_by" type="BIGINT">
                <constraints nullable="true" />
            </column>
            <column name="version" type="INTEGER" defaultValue="0">
                <constraints nullable="false" />
            </column>
            <column name="deleted" type="INTEGER" defaultValue="0">
                <constraints nullable="false" />
            </column>
            <column name="tenant_id" type="BIGINT" defaultValue="1">
                <constraints nullable="false" />
            </column>
            <column name="create_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false" />
            </column>
            <column name="update_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false" />
            </column>
            <column name="creator_id" type="BIGINT">
                <constraints nullable="true" />
            </column>
            <column name="updater_id" type="BIGINT">
                <constraints nullable="true" />
            </column>
            <column name="additional_info" type="TEXT">
                <constraints nullable="true" />
            </column>
            <column name="remark" type="VARCHAR(500)">
                <constraints nullable="true" />
            </column>
        </createTable>

        <!-- 创建索引 -->
        <createIndex tableName="sys_user_roles" indexName="idx_user_role_user_id">
            <column name="user_id" />
        </createIndex>
        <createIndex tableName="sys_user_roles" indexName="idx_user_role_role_id">
            <column name="role_id" />
        </createIndex>
        <createIndex tableName="sys_user_roles" indexName="idx_user_role_tenant_deleted">
            <column name="tenant_id" />
            <column name="deleted" />
        </createIndex>

        <!-- 添加外键约束 -->
        <addForeignKeyConstraint
            baseTableName="sys_user_roles"
            baseColumnNames="user_id"
            referencedTableName="sys_users"
            referencedColumnNames="id"
            constraintName="fk_user_role_user" />
        <addForeignKeyConstraint
            baseTableName="sys_user_roles"
            baseColumnNames="role_id"
            referencedTableName="sys_roles"
            referencedColumnNames="id"
            constraintName="fk_user_role_role" />
    </changeSet>

</databaseChangeLog> 