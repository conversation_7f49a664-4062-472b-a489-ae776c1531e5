# PISP用户服务认证功能说明

## 概述

PISP用户服务已成功集成认证功能，基于Spring Security + JWT实现，提供完整的用户认证、授权和会话管理能力。

## 功能特性

### 1. 用户认证
- ✅ 用户名密码登录
- ✅ JWT令牌生成和验证
- ✅ 访问令牌和刷新令牌机制
- ✅ 用户会话管理
- ✅ 安全注销

### 2. 权限控制
- ✅ 基于角色的访问控制(RBAC)
- ✅ 方法级权限控制
- ✅ 接口权限验证
- ✅ 用户上下文管理

### 3. 安全配置
- ✅ Spring Security配置
- ✅ JWT过滤器链
- ✅ 异常处理机制
- ✅ 跨域配置

## API接口

### 认证相关接口

#### 1. 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "123456",
  "rememberMe": false
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiJ9...",
    "tokenType": "Bearer",
    "expiresIn": 86400,
    "userInfo": {
      "userId": 1,
      "username": "admin",
      "realName": "管理员",
      "email": "<EMAIL>",
      "phone": "13800138000",
      "departmentId": 1,
      "isAdmin": true
    },
    "roles": ["ADMIN", "USER"],
    "permissions": ["user:read", "user:write", "user:delete"]
  }
}
```

#### 2. 用户注销
```http
POST /api/auth/logout
Authorization: Bearer {accessToken}
```

#### 3. 刷新令牌
```http
POST /api/auth/refresh
Content-Type: application/json

{
  "refreshToken": "eyJhbGciOiJIUzI1NiJ9..."
}
```

#### 4. 获取当前用户信息
```http
GET /api/auth/me
Authorization: Bearer {accessToken}
```

#### 5. 验证令牌
```http
GET /api/auth/validate
Authorization: Bearer {accessToken}
```

## 配置说明

### JWT配置
```yaml
pisp:
  security:
    jwt:
      # JWT密钥
      secret: "pisp-user-service-jwt-secret-key-for-token-generation-and-validation-2024"
      # JWT过期时间（秒）- 24小时
      expiration: 86400
      # 刷新Token过期时间（秒）- 7天
      refresh-expiration: 604800
      # JWT发行者
      issuer: "pisp-user-service"
```

### 安全配置
- **无状态会话**：使用JWT令牌，不依赖服务器会话
- **CSRF禁用**：API服务不需要CSRF保护
- **CORS处理**：由网关统一处理跨域
- **异常处理**：统一的认证和授权异常处理

## 核心组件

### 1. 认证服务 (AuthService)
- `login()` - 用户登录
- `logout()` - 用户注销
- `refreshToken()` - 刷新令牌
- `validateToken()` - 验证令牌
- `getCurrentUserInfo()` - 获取当前用户信息

### 2. 用户详情服务 (PispUserDetailsService)
- 实现Spring Security的UserDetailsService接口
- 从数据库加载用户信息
- 构建用户权限和角色信息

### 3. JWT工具类 (JwtTokenUtil)
- 生成访问令牌和刷新令牌
- 验证令牌有效性
- 从令牌中提取用户信息

### 4. 安全配置 (SecurityConfig)
- Spring Security过滤器链配置
- 认证管理器配置
- 权限控制规则

## 使用示例

### 前端集成示例

```javascript
// 登录
const login = async (username, password) => {
  const response = await fetch('/api/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ username, password })
  });
  
  const result = await response.json();
  if (result.code === 200) {
    // 保存令牌
    localStorage.setItem('accessToken', result.data.accessToken);
    localStorage.setItem('refreshToken', result.data.refreshToken);
    return result.data;
  }
  throw new Error(result.message);
};

// 请求拦截器 - 添加认证头
const apiRequest = (url, options = {}) => {
  const token = localStorage.getItem('accessToken');
  return fetch(url, {
    ...options,
    headers: {
      ...options.headers,
      'Authorization': token ? `Bearer ${token}` : ''
    }
  });
};

// 注销
const logout = async () => {
  await apiRequest('/api/auth/logout', { method: 'POST' });
  localStorage.removeItem('accessToken');
  localStorage.removeItem('refreshToken');
};
```

## 测试验证

认证功能已通过完整的单元测试验证：

- ✅ AuthControllerTest - 认证控制器测试
- ✅ UserControllerTest - 用户控制器测试  
- ✅ UserControllerSimpleTest - 用户控制器简单测试

所有测试用例均通过，确保认证功能的稳定性和可靠性。

## 后续扩展

### 计划功能
- [ ] 多因素认证(MFA)
- [ ] 单点登录(SSO)
- [ ] OAuth2集成
- [ ] 验证码功能
- [ ] 密码策略配置
- [ ] 登录日志记录
- [ ] 异地登录检测

### 性能优化
- [ ] Redis缓存用户会话
- [ ] 令牌黑名单机制
- [ ] 分布式会话管理

## 注意事项

1. **安全性**：
   - JWT密钥应使用强随机字符串
   - 生产环境应配置HTTPS
   - 定期轮换JWT密钥

2. **性能**：
   - 合理设置令牌过期时间
   - 使用Redis缓存用户会话信息
   - 避免在令牌中存储过多信息

3. **可维护性**：
   - 统一异常处理
   - 完善的日志记录
   - 清晰的权限模型

## 总结

PISP用户服务的认证功能已成功实现并通过测试验证。该认证系统具有良好的安全性、可扩展性和易用性，为整个PISP系统提供了坚实的安全基础。
