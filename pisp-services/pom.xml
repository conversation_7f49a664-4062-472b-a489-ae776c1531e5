<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.bdyl.erp.pisp</groupId>
        <artifactId>pisp-root</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>pisp-services</artifactId>
    <packaging>pom</packaging>
    <name>PISP Service Module</name>
    <description>PISP系统微服务模块集合</description>

    <modules>
        <module>pisp-user</module>
        <module>pisp-base-data</module>
        <module>pisp-purchase</module>
        <module>pisp-sales</module>
        <module>pisp-inventory</module>
        <module>pisp-finance</module>
        <module>pisp-report</module>
        <module>pisp-system</module>
        <module>pisp-retail</module>
    </modules>
</project>