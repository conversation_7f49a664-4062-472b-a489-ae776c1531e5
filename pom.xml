<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.bdyl.build</groupId>
        <artifactId>bdyl-parent</artifactId>
        <version>2025.2.0.0-SNAPSHOT</version>
    </parent>

    <groupId>com.bdyl.erp.pisp</groupId>
    <artifactId>pisp-root</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>PISP Purchase Inventory Sales Management System</name>
    <description>基于Spring Boot 3.4.7和Spring Cloud 2024.0.1的微服务架构进销存管理系统</description>

    <properties>
        <revision>1.0.0-SNAPSHOT</revision>
        <!-- Maven插件版本 -->
        <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
        <maven-surefire-plugin.version>3.2.2</maven-surefire-plugin.version>
        <maven-failsafe-plugin.version>3.2.2</maven-failsafe-plugin.version>
        <!-- JWT版本 -->
        <jjwt.version>0.12.6</jjwt.version>
    </properties>

    <!-- 依赖管理 -->
    <dependencyManagement>
        <dependencies>

            <!-- 项目内部模块依赖 -->
            <dependency>
                <groupId>com.bdyl.erp.pisp</groupId>
                <artifactId>pisp-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.bdyl.erp.pisp</groupId>
                <artifactId>pisp-common-core</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.bdyl.erp.pisp</groupId>
                <artifactId>pisp-common-web</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.bdyl.erp.pisp</groupId>
                <artifactId>pisp-common-security</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.bdyl.erp.pisp</groupId>
                <artifactId>pisp-common-redis</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.bdyl.erp.pisp</groupId>
                <artifactId>pisp-common-rocketmq</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- JWT依赖 -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-api</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!-- 公共依赖 -->
    <dependencies>
        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- Spring Boot Test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <!-- 子模块 -->
    <modules>
        <module>pisp-common</module>
        <module>pisp-gateway</module>
        <module>pisp-api</module>
        <module>pisp-services</module>
    </modules>

    <!-- 构建配置 -->
    <build>
        <pluginManagement>
            <plugins>

                <!-- 单元测试插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                </plugin>

                <!-- 集成测试插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-failsafe-plugin</artifactId>
                    <version>${maven-failsafe-plugin.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <!-- 仓库配置 -->
    <repositories>
        <repository>
            <id>central</id>
            <name>Maven Central</name>
            <url>https://repo1.maven.org/maven2</url>
        </repository>
        <repository>
            <id>spring-milestones</id>
            <name>Spring Milestones</name>
            <url>https://repo.spring.io/milestone</url>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>central</id>
            <name>Maven Central</name>
            <url>https://repo1.maven.org/maven2</url>
        </pluginRepository>
        <pluginRepository>
            <id>spring-milestones</id>
            <name>Spring Milestones</name>
            <url>https://repo.spring.io/milestone</url>
        </pluginRepository>
    </pluginRepositories>
</project>